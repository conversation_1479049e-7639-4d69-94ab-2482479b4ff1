// examples/realtime-usage.ts
// Comprehensive example showing how to use all realtime providers

import { 
  realtime, 
  getRealtime, 
  createPusherRealtime, 
  createWebSocketRealtime, 
  createSocketIORealtime 
} from '@/lib/realtime';

// 1. Using the default configured provider
export function useDefaultProvider() {
  // Server-side usage
  const triggerEvent = async () => {
    await realtime.server.trigger('chat-channel', 'new-message', {
      from: 'user123',
      message: 'Hello world!',
      timestamp: new Date().toISOString()
    });
  };

  // Client-side usage
  const subscribeToChannel = () => {
    const channel = realtime.client.subscribe('chat-channel');
    
    channel.bind('new-message', (data) => {
      console.log('New message received:', data);
    });

    channel.bind('user-typing', (data) => {
      console.log('User typing:', data);
    });

    // Cleanup
    return () => {
      channel.unbind_all();
      channel.unsubscribe();
    };
  };

  return { triggerEvent, subscribeToChannel };
}

// 2. Using specific providers
export function useSpecificProviders() {
  // Pusher
  const pusherRealtime = getRealtime('pusher');
  
  // WebSocket
  const websocketRealtime = getRealtime('websocket');
  
  // Socket.IO
  const socketioRealtime = getRealtime('socketio');

  return { pusherRealtime, websocketRealtime, socketioRealtime };
}

// 3. Creating custom provider instances
export function useCustomProviders() {
  // Custom Pusher instance
  const customPusher = createPusherRealtime();

  // Custom WebSocket instance
  const customWebSocket = createWebSocketRealtime({
    clientUrl: 'ws://custom-server:8080',
    serverApiEndpoint: '/api/v1/custom-websocket/trigger'
  });

  // Custom Socket.IO instance
  const customSocketIO = createSocketIORealtime({
    clientUrl: 'http://custom-server:3001',
    serverApiEndpoint: '/api/v1/custom-socketio/trigger',
    clientOptions: {
      transports: ['websocket'],
      timeout: 5000
    }
  });

  return { customPusher, customWebSocket, customSocketIO };
}

// 4. React Hook example
import { useEffect, useState } from 'react';

export function useRealtimeChat(channelName: string) {
  const [messages, setMessages] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Subscribe to channel
    const channel = realtime.client.subscribe(channelName);

    // Listen for connection events
    realtime.client.connection.bind('connected', () => {
      setIsConnected(true);
      console.log('Connected to realtime service');
    });

    realtime.client.connection.bind('disconnected', () => {
      setIsConnected(false);
      console.log('Disconnected from realtime service');
    });

    // Listen for new messages
    channel.bind('new-message', (message) => {
      setMessages(prev => [...prev, message]);
    });

    // Listen for message updates
    channel.bind('message-updated', (updatedMessage) => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === updatedMessage.id ? updatedMessage : msg
        )
      );
    });

    // Cleanup
    return () => {
      channel.unbind_all();
      channel.unsubscribe();
    };
  }, [channelName]);

  const sendMessage = async (message: string) => {
    try {
      await realtime.server.trigger(channelName, 'new-message', {
        id: Date.now().toString(),
        message,
        timestamp: new Date().toISOString(),
        user: 'current-user'
      });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  return {
    messages,
    isConnected,
    sendMessage,
    socketId: realtime.client.connection.socket_id
  };
}

// 5. Server-side API route example
export async function handleWebhook(eventType: string, payload: any) {
  try {
    switch (eventType) {
      case 'message':
        await realtime.server.trigger('chat-channel', 'new-message', payload);
        break;
      
      case 'presence':
        await realtime.server.trigger('presence-channel', 'user-presence', payload);
        break;
      
      case 'typing':
        await realtime.server.trigger('chat-channel', 'user-typing', payload);
        break;
      
      default:
        console.log('Unknown event type:', eventType);
    }
  } catch (error) {
    console.error('Failed to trigger realtime event:', error);
    throw error;
  }
}

// 6. Environment-based configuration example
export function getRealtimeConfig() {
  const provider = process.env.REALTIME_PROVIDER || 'pusher';
  
  const configs = {
    pusher: {
      description: 'Using Pusher for realtime communication',
      features: ['Hosted service', 'Global CDN', 'Built-in scaling']
    },
    websocket: {
      description: 'Using native WebSocket for realtime communication',
      features: ['Self-hosted', 'Low latency', 'Full control']
    },
    socketio: {
      description: 'Using Socket.IO for realtime communication',
      features: ['Fallback transports', 'Room management', 'Built-in reconnection']
    }
  };

  return {
    provider,
    config: configs[provider as keyof typeof configs],
    realtime: getRealtime(provider as any)
  };
}
