#!/usr/bin/env node

// scripts/start-socketio-server.js
// Standalone Socket.IO server for realtime communication

const { getSocketIOServer } = require('../lib/socketio-server');

// Configuration from environment variables
const port = parseInt(process.env.SOCKETIO_PORT || '3001');
const corsOrigin = process.env.SOCKETIO_CORS_ORIGIN || "*";

console.log('Starting Socket.IO server...');
console.log(`Port: ${port}`);
console.log(`CORS Origin: ${corsOrigin}`);

try {
  const server = getSocketIOServer({
    port,
    corsOptions: {
      origin: corsOrigin,
      methods: ["GET", "POST"]
    }
  });

  console.log(`✅ Socket.IO server started successfully on port ${port}`);
  console.log(`📡 Ready to handle realtime connections`);
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Socket.IO server...');
    server.close();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down Socket.IO server...');
    server.close();
    process.exit(0);
  });

} catch (error) {
  console.error('❌ Failed to start Socket.IO server:', error);
  process.exit(1);
}
