"use client"

import { useState } from "react"
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Tag } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

const templates = [
  {
    id: "1",
    title: "Order Status Inquiry",
    content:
      "Hi! I'd be happy to help you check on your order status. Could you please provide me with your order number so I can look into this for you?",
    category: "Orders",
    tags: ["order", "status", "inquiry"],
    usage: 45,
    isFavorite: true,
  },
  {
    id: "2",
    title: "Shipping Delay Apology",
    content:
      "I sincerely apologize for the delay with your shipment. I understand how frustrating this must be, and I'm here to help resolve this issue as quickly as possible.",
    category: "Shipping",
    tags: ["shipping", "delay", "apology"],
    usage: 32,
    isFavorite: false,
  },
  {
    id: "3",
    title: "Refund Process Explanation",
    content:
      "I'll be happy to process your refund request. The refund will be issued to your original payment method and typically takes 3-5 business days to appear on your statement.",
    category: "Billing",
    tags: ["refund", "billing", "process"],
    usage: 28,
    isFavorite: true,
  },
  {
    id: "4",
    title: "Technical Support Greeting",
    content:
      "Hello! I'm here to help you with your technical issue. To better assist you, could you please describe the problem you're experiencing in detail?",
    category: "Technical",
    tags: ["technical", "support", "greeting"],
    usage: 21,
    isFavorite: false,
  },
  {
    id: "5",
    title: "Account Setup Assistance",
    content:
      "Welcome! I'd be delighted to help you set up your account. Let me guide you through the process step by step to ensure everything is configured correctly.",
    category: "Onboarding",
    tags: ["account", "setup", "onboarding"],
    usage: 19,
    isFavorite: false,
  },
  {
    id: "6",
    title: "Escalation to Supervisor",
    content:
      "I understand your concern and want to ensure you receive the best possible assistance. Let me connect you with my supervisor who can provide additional support for this matter.",
    category: "Escalation",
    tags: ["escalation", "supervisor", "support"],
    usage: 15,
    isFavorite: false,
  },
]

const categories = ["All", "Orders", "Shipping", "Billing", "Technical", "Onboarding", "Escalation"]

interface TemplatesPanelProps {
  onClose: () => void
}

export function TemplatesPanel({ onClose }: TemplatesPanelProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesCategory = selectedCategory === "All" || template.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const handleUseTemplate = (template: any) => {
    // Here you would typically insert the template into the message input
    console.log("Using template:", template.content)
    // You could also close the panel after selection
    // onClose()
  }

  const toggleFavorite = (templateId: string) => {
    // Here you would typically update the favorite status in your backend
    console.log("Toggling favorite for template:", templateId)
  }

  return (
    <div className="w-80 bg-background flex flex-col h-full shadow-xl border-l">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Message Templates
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-1">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className="text-xs"
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Templates List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {filteredTemplates.length === 0 ? (
          <div className="text-center text-muted-foreground py-8">
            <Tag className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No templates found</p>
            <p className="text-sm">Try adjusting your search or filters</p>
          </div>
        ) : (
          filteredTemplates.map((template) => (
            <Card key={template.id} className="hover:shadow-sm transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-sm font-medium leading-tight">{template.title}</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleFavorite(template.id)}
                          className="h-6 w-6 p-0"
                        >
                          <Star
                            className={`h-3 w-3 ${
                              template.isFavorite ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"
                            }`}
                          />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        {template.isFavorite ? "Remove from favorites" : "Add to favorites"}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {template.category}
                  </Badge>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    Used {template.usage} times
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground mb-3 line-clamp-3">{template.content}</p>

                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-1">
                    {template.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs px-1.5 py-0">
                        {tag}
                      </Badge>
                    ))}
                    {template.tags.length > 2 && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0">
                        +{template.tags.length - 2}
                      </Badge>
                    )}
                  </div>

                  <div className="flex gap-1">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                            <Copy className="h-3 w-3" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Copy to clipboard</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <Button size="sm" onClick={() => handleUseTemplate(template)} className="text-xs px-2 py-1 h-6">
                      Use
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t bg-muted/30">
        <div className="text-xs text-muted-foreground text-center">
          {filteredTemplates.length} template{filteredTemplates.length !== 1 ? "s" : ""} found
        </div>
      </div>
    </div>
  )
}
