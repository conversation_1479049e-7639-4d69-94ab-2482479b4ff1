'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { 
  Download, 
  Upload, 
  Edit,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { TableComponent } from './index'
import BulkImportComponent from './BulkImportComponent'
import type { ImportResult, BulkImportConfig } from './BulkImportComponent'

// Types
export interface BulkUpdateConfig {
  // Data fetching
  fetchData: () => Promise<Record<string, any>[]>
  updateData: (data: Record<string, any>[]) => Promise<ImportResult>
  
  // Table configuration
  headers: string[]
  transformToTableRow: (item: Record<string, any>) => { id: string; columns: string[] }
  
  // Import configuration for the update workflow
  importConfig: BulkImportConfig
}

interface BulkUpdateComponentProps {
  config: BulkUpdateConfig
  onSuccess?: () => void
}

export default function BulkUpdateComponent({ config, onSuccess }: BulkUpdateComponentProps) {
  // State
  const [data, setData] = useState<Record<string, any>[]>([])
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showImport, setShowImport] = useState(false)
  const [exportedData, setExportedData] = useState<Record<string, any>[]>([])

  // Load data
  const loadData = async () => {
    setIsLoading(true)
    try {
      const fetchedData = await config.fetchData()
      setData(fetchedData)
    } catch (error) {
      console.error('Error loading data:', error)
      toast({
        title: 'Error',
        description: 'Failed to load data',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle row selection
  const handleRowSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds(prev => [...prev, id])
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id))
    }
  }

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(data.map(item => item.id))
    } else {
      setSelectedIds([])
    }
  }

  // Export selected data to CSV
  const exportSelectedData = () => {
    if (selectedIds.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select at least one record to export',
        variant: 'destructive'
      })
      return
    }

    const selectedData = data.filter(item => selectedIds.includes(item.id))
    setExportedData(selectedData)
    
    const csv = convertToCSV(selectedData)
    downloadFile(csv, 'bulk_update_data.csv', 'text/csv')
    
    toast({
      title: 'Success',
      description: `Exported ${selectedData.length} records to CSV`,
      variant: 'default'
    })
  }

  // Convert data to CSV
  const convertToCSV = (data: Record<string, any>[]): string => {
    if (data.length === 0) return ''
    
    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n')
    
    return csvContent
  }

  // Download file
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Show import interface
  const showImportInterface = () => {
    if (selectedIds.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select records first, then export and modify the CSV file',
        variant: 'destructive'
      })
      return
    }
    setShowImport(true)
  }

  // Handle successful import (update)
  const handleImportSuccess = () => {
    setShowImport(false)
    setSelectedIds([])
    loadData() // Refresh data
    if (onSuccess) {
      onSuccess()
    }
  }

  // Load data on component mount
  useEffect(() => {
    loadData()
  }, [])

  // Prepare table data with selection
  const tableData = data.map(item => {
    const row = config.transformToTableRow(item)
    return {
      ...row,
      selected: selectedIds.includes(item.id)
    }
  })

  // Enhanced import config for updates
  const updateImportConfig: BulkImportConfig = {
    ...config.importConfig,
    importData: async (importData: Record<string, any>[]) => {
      return await config.updateData(importData)
    }
  }

  if (showImport) {
    return (
      <div className="space-y-6">
        {/* Back to selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Import Updated Data</span>
              <Button variant="outline" onClick={() => setShowImport(false)}>
                Back to Selection
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div className="flex items-start gap-2">
                <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900">Update Workflow</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Upload the CSV file you exported and modified. The system will update the selected records with the new data.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Import component */}
        <BulkImportComponent 
          config={updateImportConfig}
          onSuccess={handleImportSuccess}
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit className="w-5 h-5" />
            Bulk Update Workflow
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-900 mb-2">How to bulk update:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-blue-700">
              <li>Select the records you want to update from the table below</li>
              <li>Click "Export Selected" to download a CSV file with the selected data</li>
              <li>Modify the CSV file with your changes (keep the ID column unchanged)</li>
              <li>Click "Import Updates" and upload your modified CSV file</li>
              <li>Review the preview and click "Import Data" to apply the updates</li>
            </ol>
          </div>
        </CardContent>
      </Card>

      {/* Selection Summary and Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Data Selection</span>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {selectedIds.length} of {data.length} selected
              </Badge>
              <Button 
                variant="outline" 
                onClick={exportSelectedData}
                disabled={selectedIds.length === 0}
              >
                <Download className="w-4 h-4 mr-2" />
                Export Selected
              </Button>
              <Button 
                onClick={showImportInterface}
                disabled={selectedIds.length === 0}
              >
                <Upload className="w-4 h-4 mr-2" />
                Import Updates
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Select All Checkbox */}
          <div className="flex items-center space-x-2 mb-4">
            <Checkbox
              id="select-all"
              checked={selectedIds.length === data.length && data.length > 0}
              onCheckedChange={handleSelectAll}
            />
            <label htmlFor="select-all" className="text-sm font-medium">
              Select All ({data.length} records)
            </label>
          </div>

          {/* Data Table with Selection */}
          <div className="border rounded-lg overflow-hidden">
            <table className="w-full text-sm">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left font-medium text-gray-900 w-12">
                    Select
                  </th>
                  {config.headers.map((header) => (
                    <th key={header} className="px-3 py-2 text-left font-medium text-gray-900">
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan={config.headers.length + 1} className="px-3 py-8 text-center text-gray-500">
                      Loading data...
                    </td>
                  </tr>
                ) : data.length === 0 ? (
                  <tr>
                    <td colSpan={config.headers.length + 1} className="px-3 py-8 text-center text-gray-500">
                      No data available
                    </td>
                  </tr>
                ) : (
                  data.map((item) => {
                    const row = config.transformToTableRow(item)
                    const isSelected = selectedIds.includes(item.id)
                    return (
                      <tr key={item.id} className={`border-t ${isSelected ? 'bg-blue-50' : ''}`}>
                        <td className="px-3 py-2">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={(checked) => handleRowSelect(item.id, checked as boolean)}
                          />
                        </td>
                        {row.columns.map((cell, cellIndex) => (
                          <td key={cellIndex} className="px-3 py-2">
                            {cell}
                          </td>
                        ))}
                      </tr>
                    )
                  })
                )}
              </tbody>
            </table>
          </div>

          {/* Selection Info */}
          {selectedIds.length > 0 && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-800">
                  {selectedIds.length} record{selectedIds.length !== 1 ? 's' : ''} selected for bulk update
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
