'use client'

import { ReactNode } from 'react'
import styles from './table.module.css'

// Types for pinned column functionality
export interface PinnedColumnConfig {
  columnName: string
  pinnedColumns: string[]
  columnWidth: Record<string, string>
  defaultColumnWidth: string
  rowNumberColumnWidth?: number
}

export interface PinnedColumnProps extends PinnedColumnConfig {
  children: ReactNode
  className?: string
  style?: React.CSSProperties
  isHeader?: boolean
  isSelected?: boolean
  isEvenRow?: boolean
  zIndex?: number
}

// Hook for pinned column logic
export const usePinnedColumns = (config: PinnedColumnConfig) => {
  const { columnName, pinnedColumns, columnWidth, defaultColumnWidth, rowNumberColumnWidth = 60 } = config

  const isPinnedColumn = (colName: string): boolean => {
    return pinnedColumns.includes(colName)
  }

  const getPinnedColumns = (): string[] => {
    return pinnedColumns
  }

  const getUnpinnedColumns = (headers: string[]): string[] => {
    return headers.filter(header => !isPinnedColumn(header))
  }

  const getColumnIndex = (headers: string[], colName: string): number => {
    return headers.indexOf(colName)
  }

  const getPinnedColumnLeftPosition = (colName: string): string => {
    const pinnedCols = getPinnedColumns()
    const index = pinnedCols.indexOf(colName)
    let leftPosition = rowNumberColumnWidth // Start after the row number column

    for (let i = 0; i < index; i++) {
      const colWidth = columnWidth[pinnedCols[i]] || defaultColumnWidth
      leftPosition += parseInt(colWidth.replace('px', ''))
    }

    return `${leftPosition}px`
  }

  const isPinned = isPinnedColumn(columnName)

  const pinnedStyle: React.CSSProperties = isPinned ? {
    position: 'sticky',
    left: getPinnedColumnLeftPosition(columnName),
    zIndex: 15
  } : {}

  const pinnedHeaderStyle: React.CSSProperties = isPinned ? {
    position: 'sticky',
    left: getPinnedColumnLeftPosition(columnName),
    zIndex: 25
  } : {}

  return {
    isPinned,
    pinnedStyle,
    pinnedHeaderStyle,
    getPinnedColumnLeftPosition,
    isPinnedColumn,
    getPinnedColumns,
    getUnpinnedColumns,
    getColumnIndex
  }
}

// PinnedColumn component
export const PinnedColumn: React.FC<PinnedColumnProps> = ({
  children,
  className = '',
  style = {},
  isHeader = false,
  isSelected = false,
  isEvenRow = false,
  zIndex,
  ...config
}) => {
  const { isPinned, pinnedStyle, pinnedHeaderStyle } = usePinnedColumns(config)

  if (!isPinned) {
    return (
      <td className={className} style={style}>
        {children}
      </td>
    )
  }

  const finalStyle = {
    ...style,
    ...(isHeader ? pinnedHeaderStyle : pinnedStyle),
    ...(zIndex && { zIndex })
  }

  const pinnedClasses = [
    styles.pinnedColumn,
    isHeader && styles.pinnedColumnHeader,
    isPinned && isEvenRow && 'bg-[#F5F5F5]',
    isPinned && !isEvenRow && !isSelected && 'bg-white',
    isPinned && isSelected && 'bg-[#000090] text-white font-bold'
  ].filter(Boolean).join(' ')

  const finalClassName = `${className} ${pinnedClasses}`.trim()

  if (isHeader) {
    return (
      <th className={finalClassName} style={finalStyle}>
        {children}
        <span className="ml-2 text-xs text-blue-600">📌</span>
      </th>
    )
  }

  return (
    <td className={finalClassName} style={finalStyle}>
      {children}
    </td>
  )
}

// PinnedColumnHeader component for table headers
export const PinnedColumnHeader: React.FC<PinnedColumnProps> = (props) => {
  return <PinnedColumn {...props} isHeader={true} />
}

// Utility function to get pinned column configuration
export const createPinnedColumnConfig = (
  columnName: string,
  pinnedColumns: string[],
  columnWidth: Record<string, string>,
  defaultColumnWidth: string,
  rowNumberColumnWidth?: number
): PinnedColumnConfig => ({
  columnName,
  pinnedColumns,
  columnWidth,
  defaultColumnWidth,
  rowNumberColumnWidth
})

// PinnedColumnManager component for managing pinned columns in DataPageEnhanced
export interface PinnedColumnManagerProps {
  headers: string[]
  pinnedColumns: string[]
  onPinnedColumnsChange: (pinnedColumns: string[]) => void
  className?: string
}

export const PinnedColumnManager: React.FC<PinnedColumnManagerProps> = ({
  headers,
  pinnedColumns,
  onPinnedColumnsChange,
  className = ''
}) => {
  const togglePinColumn = (columnName: string) => {
    const isPinned = pinnedColumns.includes(columnName)
    if (isPinned) {
      onPinnedColumnsChange(pinnedColumns.filter(col => col !== columnName))
    } else {
      onPinnedColumnsChange([...pinnedColumns, columnName])
    }
  }

  const unpinAllColumns = () => {
    onPinnedColumnsChange([])
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-700">Pinned Columns</h3>
        {pinnedColumns.length > 0 && (
          <button
            onClick={unpinAllColumns}
            className="text-xs text-red-600 hover:text-red-800 underline"
          >
            Unpin All
          </button>
        )}
      </div>

      <div className="space-y-2">
        {headers.map((header) => {
          const isPinned = pinnedColumns.includes(header)
          return (
            <label
              key={header}
              className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-1 rounded"
            >
              <input
                type="checkbox"
                checked={isPinned}
                onChange={() => togglePinColumn(header)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">{header}</span>
              {isPinned && (
                <span className="text-xs text-blue-600">📌</span>
              )}
            </label>
          )
        })}
      </div>

      {pinnedColumns.length > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            {pinnedColumns.length} column{pinnedColumns.length !== 1 ? 's' : ''} pinned
          </p>
          <div className="flex flex-wrap gap-1 mt-1">
            {pinnedColumns.map((col) => (
              <span
                key={col}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
              >
                {col}
                <button
                  onClick={() => togglePinColumn(col)}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default PinnedColumn
