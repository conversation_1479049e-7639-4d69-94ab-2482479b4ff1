'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  Download, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  FileText
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { TableComponent } from './index'

// Types
export interface ImportField {
  name: string
  label: string
  required: boolean
  type: 'string' | 'number' | 'date' | 'email' | 'phone'
  example?: string
}

export interface ValidationError {
  row: number
  field: string
  message: string
}

export interface ImportResult {
  total: number
  successful: number
  failed: number
  errors: ValidationError[]
}

export interface BulkImportConfig {
  fields: ImportField[]
  maxFileSize?: number // MB
  maxRecords?: number
  supportedFormats: string[]
  generateTemplate: () => Record<string, any>[]
  validateData: (data: Record<string, any>[]) => ValidationError[]
  importData: (data: Record<string, any>[]) => Promise<ImportResult>
}

interface BulkImportComponentProps {
  config: BulkImportConfig
  onSuccess?: () => void
}

export default function BulkImportComponent({ config, onSuccess }: BulkImportComponentProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // State
  const [uploadedData, setUploadedData] = useState<Record<string, any>[]>([])
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  // File upload handler
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file size
    if (config.maxFileSize && file.size > config.maxFileSize * 1024 * 1024) {
      toast({
        title: 'Error',
        description: `File size must not exceed ${config.maxFileSize}MB`,
        variant: 'destructive'
      })
      return
    }

    // Validate file format
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    if (!config.supportedFormats.includes(fileExtension || '')) {
      toast({
        title: 'Error',
        description: `Unsupported file format. Supported: ${config.supportedFormats.join(', ')}`,
        variant: 'destructive'
      })
      return
    }

    setSelectedFile(file)
    setIsProcessing(true)
    setProgress(10)

    try {
      const data = await parseFile(file, fileExtension!)
      setProgress(50)
      
      // Validate data
      const errors = config.validateData(data)
      setValidationErrors(errors)
      setProgress(80)
      
      setUploadedData(data)
      setProgress(100)
      
      toast({
        title: 'Success',
        description: `File uploaded successfully. ${data.length} records found.`,
        variant: 'default'
      })
    } catch (error) {
      console.error('File parsing error:', error)
      toast({
        title: 'Error',
        description: 'Failed to parse file. Please check the file format.',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  // Parse file based on format
  const parseFile = async (file: File, format: string): Promise<Record<string, any>[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string
          let data: Record<string, any>[] = []
          
          switch (format) {
            case 'csv':
              data = parseCSV(content)
              break
            case 'json':
              data = JSON.parse(content)
              if (!Array.isArray(data)) {
                throw new Error('JSON file must contain an array of objects')
              }
              break
            default:
              throw new Error(`Unsupported format: ${format}`)
          }
          
          // Validate record count
          if (config.maxRecords && data.length > config.maxRecords) {
            throw new Error(`Too many records. Maximum allowed: ${config.maxRecords}`)
          }
          
          resolve(data)
        } catch (error) {
          reject(error)
        }
      }
      
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsText(file)
    })
  }

  // Simple CSV parser
  const parseCSV = (content: string): Record<string, any>[] => {
    const lines = content.trim().split('\n')
    if (lines.length < 2) throw new Error('CSV file must have at least a header and one data row')
    
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const data: Record<string, any>[] = []
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
      const row: Record<string, any> = {}
      
      headers.forEach((header, index) => {
        row[header] = values[index] || ''
      })
      
      data.push(row)
    }
    
    return data
  }

  // Download template
  const downloadTemplate = () => {
    const templateData = config.generateTemplate()
    const csv = convertToCSV(templateData)
    downloadFile(csv, 'import_template.csv', 'text/csv')
  }

  // Convert data to CSV
  const convertToCSV = (data: Record<string, any>[]): string => {
    if (data.length === 0) return ''
    
    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n')
    
    return csvContent
  }

  // Download file
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Execute import
  const executeImport = async () => {
    if (uploadedData.length === 0) {
      toast({
        title: 'Error',
        description: 'No data to import',
        variant: 'destructive'
      })
      return
    }

    // Check if there are validation errors
    const criticalErrors = validationErrors.filter(error => 
      config.fields.find(field => field.name === error.field)?.required
    )

    if (criticalErrors.length > 0) {
      toast({
        title: 'Error',
        description: 'Please fix validation errors before importing',
        variant: 'destructive'
      })
      return
    }

    setIsProcessing(true)
    setProgress(0)

    try {
      const result = await config.importData(uploadedData)
      setImportResult(result)
      setProgress(100)

      toast({
        title: 'Import Complete',
        description: `${result.successful}/${result.total} records imported successfully`,
        variant: result.failed > 0 ? 'destructive' : 'default'
      })

      if (result.successful > 0 && onSuccess) {
        onSuccess()
      }
    } catch (error) {
      console.error('Import error:', error)
      toast({
        title: 'Error',
        description: 'Import failed. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Reset state
  const resetState = () => {
    setUploadedData([])
    setValidationErrors([])
    setImportResult(null)
    setSelectedFile(null)
    setProgress(0)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Prepare data for table display
  const tableHeaders = uploadedData.length > 0 ? Object.keys(uploadedData[0]) : []
  const tableData = uploadedData.map((row, index) => ({
    id: index.toString(),
    columns: tableHeaders.map(header => row[header] || '')
  }))

  return (
    <div className="space-y-6">
      {/* File Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Import Data
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* File Upload Area */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              ref={fileInputRef}
              type="file"
              accept={config.supportedFormats.map(f => `.${f}`).join(',')}
              onChange={handleFileUpload}
              className="hidden"
            />
            <div className="space-y-2">
              <Upload className="w-12 h-12 mx-auto text-gray-400" />
              <div>
                <Button onClick={() => fileInputRef.current?.click()}>
                  Choose File
                </Button>
                <p className="text-sm text-gray-500 mt-2">
                  Supported formats: {config.supportedFormats.join(', ').toUpperCase()}
                </p>
                {config.maxFileSize && (
                  <p className="text-xs text-gray-400">
                    Maximum file size: {config.maxFileSize}MB
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Template Download */}
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-medium">Need a template?</h3>
              <p className="text-sm text-gray-600">Download a template file with the correct format</p>
            </div>
            <Button variant="outline" onClick={downloadTemplate}>
              <Download className="w-4 h-4 mr-2" />
              Download Template
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Progress Bar */}
      {isProcessing && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Preview */}
      {uploadedData.length > 0 && !isProcessing && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Data Preview ({uploadedData.length} records)</span>
              <div className="flex gap-2">
                <Button variant="outline" onClick={resetState}>
                  Reset
                </Button>
                <Button onClick={executeImport} disabled={isProcessing}>
                  Import Data
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Validation Summary */}
            {validationErrors.length > 0 && (
              <div className="mb-4 space-y-2">
                <h4 className="font-medium">Validation Summary</h4>
                <div className="flex gap-4">
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Valid: {uploadedData.length - validationErrors.length}
                  </Badge>
                  <Badge variant="destructive">
                    <XCircle className="w-3 h-3 mr-1" />
                    Errors: {validationErrors.length}
                  </Badge>
                </div>
              </div>
            )}

            {/* Data Table */}
            <div className="border rounded-lg overflow-hidden">
              <TableComponent
                headers={tableHeaders}
                data={tableData.slice(0, 100)} // Show first 100 rows
                action={{ delete: false, edit: false }}
                isScroll={true}
                defaultColumnWidth="150px"
              />
              {uploadedData.length > 100 && (
                <div className="bg-gray-50 px-3 py-2 text-sm text-gray-600 text-center">
                  Showing first 100 records of {uploadedData.length} total
                </div>
              )}
            </div>

            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Validation Errors</h4>
                <div className="max-h-48 overflow-auto border rounded-lg">
                  {validationErrors.slice(0, 50).map((error, index) => (
                    <div key={index} className="p-3 border-b last:border-b-0 bg-red-50">
                      <div className="flex items-start gap-2">
                        <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                        <div>
                          <div className="font-medium text-red-900">
                            Row {error.row + 1}, Field: {error.field}
                          </div>
                          <div className="text-sm text-red-700">{error.message}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {validationErrors.length > 50 && (
                    <div className="p-3 text-center text-sm text-gray-600">
                      ... and {validationErrors.length - 50} more errors
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Import Results */}
      {importResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {importResult.failed === 0 ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <AlertCircle className="w-5 h-5 text-yellow-600" />
              )}
              Import Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{importResult.total}</div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{importResult.successful}</div>
                <div className="text-sm text-gray-600">Successful</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{importResult.failed}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
            </div>

            {/* Error Details */}
            {importResult.errors.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Import Errors</h4>
                <div className="max-h-48 overflow-auto border rounded-lg">
                  {importResult.errors.map((error, index) => (
                    <div key={index} className="p-3 border-b last:border-b-0 bg-red-50">
                      <div className="flex items-start gap-2">
                        <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                        <div>
                          <div className="font-medium text-red-900">
                            Row {error.row + 1}, Field: {error.field}
                          </div>
                          <div className="text-sm text-red-700">{error.message}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
