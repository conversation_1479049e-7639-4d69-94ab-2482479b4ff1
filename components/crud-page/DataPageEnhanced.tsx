'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from '@/hooks/use-toast'
import styles from './table.module.css'

import { TableRowData } from './types'
import PageHeaderComponent from './header'
import TableComponent from './table'
import DataStats from './DataStats'
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { BarChart3, Table, TrendingUp } from 'lucide-react'
import { locales } from './locales'
import { useLocalization } from '@/hooks/useLocalization/client'

// Base data item interface
interface BaseDataItem {
  id: string
  [key: string]: any
}

// Enhanced configuration interface with server-side support
interface DataPageEnhancedConfig {
  title: string
  subtitle?: string
  headers: string[]
  columnWidths?: Record<string, string>
  defaultColumnWidth?: string
  pinnedColumns?: string[]

  // Data transformation functions
  transformToTableRow: (item: BaseDataItem) => TableRowData
  transformToStatsData: (item: BaseDataItem) => any

  // Enhanced CRUD operations with server-side support
  fetchData: (params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: Array<{ field: string; direction: 'asc' | 'desc' }>;
    filters?: Array<{ field: string; value: any }>;
  }) => Promise<{ items: BaseDataItem[]; total: number; page?: number }>

  fetchStats?: (params?: {
    search?: string;
    includeDeleted?: boolean;
    filters?: Array<{ field: string; value: any }>;
    dateFrom?: string;
    dateTo?: string;
  }) => Promise<any>

  deleteItem: (id: string) => Promise<void>

  // Navigation
  addRoute: string
  editRoute: (id: string) => string
  bulkRoute?: string

  // Filtering and sorting
  sortOptions?: Array<{ value: string; label: string }>
  dateFilterOptions?: Array<{ value: string; label: string }>
  filters?: Array<{ id: string; name: string }>

  // Stats configuration
  statsConfig?: {
    title: string
    statusOptions: string[]
    categoryLabel: string
    assignedToLabel: string
    clientLabel: string
    amountLabel: string
    amountPrefix: string
    dateField: string
    statusField: string
    amountField?: string
    categoryField?: string
    assignedToField?: string
    clientIdField?: string
  }

  // Pagination settings
  defaultPageSize?: number
  pageSizeOptions?: number[]
}

interface DataPageEnhancedProps {
  config: DataPageEnhancedConfig
}

export default function DataPageEnhanced({ config }: DataPageEnhancedProps) {
  const { t } = useLocalization("crud-page", locales)
  const router = useRouter()

  // State management
  const [listData, setListData] = useState<TableRowData[]>([])
  const [rawData, setRawData] = useState<BaseDataItem[]>([])
  const [statsData, setStatsData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFilters, setSelectedFilters] = useState<string[]>([])
  const [selectedDateFilter, setSelectedDateFilter] = useState<string>('')
  const [sortField, setSortField] = useState('')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  // Server-side pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(config.defaultPageSize || 10)
  const [totalItems, setTotalItems] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  // Pinned columns state
  const [pinnedColumns, setPinnedColumns] = useState<string[]>(config.pinnedColumns || [])

  // Navigation handlers
  const onAdd = () => router.push(config.addRoute)
  const onEdit = (id: string) => router.push(config.editRoute(id))
  const onBulk = () => config.bulkRoute && router.push(config.bulkRoute)

  // Search handler
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1) // Reset to first page on search
  }

  // Filter handlers
  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters)
    setCurrentPage(1) // Reset to first page on filter change
  }

  const handleDateFilterChange = (dateFilter: string) => {
    setSelectedDateFilter(dateFilter)
    setCurrentPage(1) // Reset to first page on date filter change
  }

  const handleSortChange = (field: string, order: 'asc' | 'desc') => {
    setSortField(field)
    setSortOrder(order)
    setCurrentPage(1) // Reset to first page on sort change
  }

  // Print handler
  const handlePrint = () => {
    window.print()
  }

  // Pagination handler
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // Convert filters to API format
  const buildApiFilters = (): Array<{ field: string; value: any }> => {
    const apiFilters: Array<{ field: string; value: any }> = []

    // Add selected filters
    selectedFilters.forEach(filterId => {
      switch (filterId) {
        case 'active':
          apiFilters.push({ field: 'deletedAt', value: null })
          break
        case 'deleted':
          apiFilters.push({ field: 'deletedAt', value: 'not_null' })
          break
        case 'has_email':
          apiFilters.push({ field: 'email', value: 'not_empty' })
          break
        case 'has_tags':
          apiFilters.push({ field: 'tags', value: 'not_empty' })
          break
      }
    })

    // Add date filter
    if (selectedDateFilter && selectedDateFilter !== 'all') {
      const now = new Date()
      let dateFrom: Date | undefined
      let dateTo: Date | undefined

      switch (selectedDateFilter) {
        case 'today':
          dateFrom = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          dateTo = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
          break
        case 'yesterday':
          dateFrom = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
          dateTo = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'this_week':
          const startOfWeek = new Date(now)
          startOfWeek.setDate(now.getDate() - now.getDay())
          dateFrom = startOfWeek
          dateTo = new Date()
          break
        case 'this_month':
          dateFrom = new Date(now.getFullYear(), now.getMonth(), 1)
          dateTo = new Date()
          break
        case 'last_month':
          dateFrom = new Date(now.getFullYear(), now.getMonth() - 1, 1)
          dateTo = new Date(now.getFullYear(), now.getMonth(), 1)
          break
      }

      if (dateFrom) {
        apiFilters.push({ field: 'createdAt', value: `>=${dateFrom.toISOString()}` })
      }
      if (dateTo) {
        apiFilters.push({ field: 'createdAt', value: `<${dateTo.toISOString()}` })
      }
    }

    return apiFilters
  }

  // Build API sorts
  const buildApiSorts = (): Array<{ field: string; direction: 'asc' | 'desc' }> => {
    if (!sortField) return []
    return [{ field: sortField, direction: sortOrder }]
  }

  // Fetch data from server
  const fetchData = async () => {
    setIsLoading(true)
    try {
      const params = {
        search: searchQuery || undefined,
        includeDeleted: selectedFilters.includes('deleted'),
        page: currentPage,
        limit: pageSize,
        sorts: buildApiSorts(),
        filters: buildApiFilters()
      }

      const result = await config.fetchData(params)

      // Update data
      setRawData(result.items)
      setListData(result.items.map(config.transformToTableRow))
      setStatsData(result.items.map(config.transformToStatsData))

      // Update pagination
      setTotalItems(result.total)
      setTotalPages(Math.ceil(result.total / pageSize))

    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: t('error_title'),
        description: t('fetch_error'),
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Delete handler with optimistic updates
  const onDelete = async (id: string) => {
    // Store original data for rollback
    const originalRawData = [...rawData]
    const originalListData = [...listData]
    const originalStatsData = [...statsData]

    // Update state immediately for instant UI feedback
    const updatedRawData = rawData.filter(item => item.id !== id)
    const updatedListData = listData.filter(item => item.id !== id)
    const updatedStatsData = statsData.filter(item => item.id !== id)

    setRawData(updatedRawData)
    setListData(updatedListData)
    setStatsData(updatedStatsData)

    // Show success message immediately
    toast({
      title: t('success_title'),
      description: t('delete_success'),
      variant: 'default'
    })

    try {
      // Perform actual delete
      await config.deleteItem(id)

      // Refresh data to get updated totals and handle pagination
      await fetchData()

    } catch (error) {
      console.error('Delete error:', error)

      // Rollback optimistic update on error
      setRawData(originalRawData)
      setListData(originalListData)
      setStatsData(originalStatsData)

      // Show error message
      toast({
        title: t('error_title'),
        description: t('delete_error'),
        variant: 'destructive'
      })
    }
  }

  // Load data on component mount and when dependencies change
  useEffect(() => {
    fetchData()
  }, [searchQuery, selectedFilters, selectedDateFilter, sortField, sortOrder, currentPage])

  return (
    <div className="p-8 flex flex-col w-full h-full relative">
      {/* Page Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{config.title}</h1>
        {config.subtitle && (
          <p className="text-gray-600">{config.subtitle}</p>
        )}
      </div>

      {/* Tabs */}
      <Tabs defaultValue="table" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="table" className="flex items-center gap-2">
            <Table className="w-4 h-4" />
            {t('table_view')}
          </TabsTrigger>
          <TabsTrigger value="stats" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            {t('statistics')}
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            {t('trends')}
          </TabsTrigger>
        </TabsList>

        {/* Stats Tab Content */}
        <TabsContent value="stats" className="space-y-6 h-full">
          {config.statsConfig && (
            <DataStats
              data={statsData}
              title={config.statsConfig.title}
              statusOptions={config.statsConfig.statusOptions}
              categoryLabel={config.statsConfig.categoryLabel}
              assignedToLabel={config.statsConfig.assignedToLabel}
              clientLabel={config.statsConfig.clientLabel}
              amountLabel={config.statsConfig.amountLabel}
              amountPrefix={config.statsConfig.amountPrefix}
              isLoading={isLoading}
            />
          )}
        </TabsContent>

        {/* Trends Tab Content */}
        <TabsContent value="trends" className="space-y-6 h-full">
          <div className="text-center py-12">
            <TrendingUp className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">{t('trends_coming_soon')}</h3>
            <p className="text-gray-600">{t('trends_description')}</p>
          </div>
        </TabsContent>

        {/* Table Tab Content */}
        <TabsContent value="table" className="space-y-6 h-full">
          <PageHeaderComponent
            title={config.title}
            total={totalItems}
            onAdd={onAdd}
            onBulk={config.bulkRoute ? onBulk : undefined}
            onSearch={handleSearch}
            onCetak={handlePrint}
            onSort={handleSortChange}
            sortOptions={config.sortOptions}
            dateFilterOptions={config.dateFilterOptions}
            filters={config.filters}
            onFilter={handleFilterChange}
            onDateFilter={handleDateFilterChange}
          />

          <TableComponent
            headers={config.headers}
            data={listData}
            action={{
              edit: true,
              delete: true
            }}
            onEdit={onEdit}
            onDelete={onDelete}
            columnWidth={config.columnWidths}
            defaultColumnWidth={config.defaultColumnWidth || "150px"}
            isScroll={true}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            perPage={pageSize}
            pinnedColumns={pinnedColumns}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Export types for external use
export type { DataPageEnhancedConfig, BaseDataItem }
