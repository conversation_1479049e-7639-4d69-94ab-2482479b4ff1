import { ReactNode } from 'react'

// Base data item interface - flexible for different data types
export interface BaseDataItem {
  id: string
  date: string // Main date field for filtering
  [key: string]: any // Allow any additional fields
}

// Time period types
export type TimePeriod = 'today' | 'yesterday' | 'this_week' | 'last_week' | 'this_month' | 'last_month' | 'this_year' | 'last_year' | 'custom' | 'all'

export interface TimePeriodOption {
  value: TimePeriod
  label: string
  description: string
}

// Stat card interface
export interface StatCard {
  title: string
  value: string | number
  subtitle?: string
  icon: ReactNode
  color: string
  bgColor: string
  trend?: {
    value: string
    isPositive: boolean
  }
  visible?: boolean // Allow hiding cards based on data type
}

// Field configuration for different data types
export interface FieldConfig {
  key: string // Field key in the data
  label: string // Display label
  type: 'string' | 'number' | 'date' | 'boolean' | 'array'
  required?: boolean
  defaultValue?: any
}

// Stat calculation configuration
export interface StatCalculationConfig {
  // Basic counts
  enableTotalCount?: boolean
  enableTodayCount?: boolean
  enableWeekCount?: boolean
  enableMonthCount?: boolean

  // Status-based calculations
  statusField?: string // Field name for status (e.g., 'status', 'state')
  statusOptions?: string[] // Available status values
  enableStatusBreakdown?: boolean

  // Category-based calculations
  categoryField?: string // Field name for category (e.g., 'category', 'type', 'tags')
  enableCategoryBreakdown?: boolean
  categoryLabel?: string // Display label for category

  // Assignment-based calculations
  assignedToField?: string // Field name for assignee (e.g., 'assignedTo', 'createdBy')
  enableAssigneeBreakdown?: boolean
  assignedToLabel?: string // Display label for assignee

  // Amount/Revenue calculations
  amountField?: string // Field name for amount (e.g., 'amount', 'price', 'revenue')
  enableAmountCalculations?: boolean
  amountLabel?: string // Display label for amount
  amountPrefix?: string // Prefix for amount display (e.g., '$', '€')

  // Client/Customer calculations
  clientField?: string // Field name for client (e.g., 'clientId', 'customerId')
  enableClientCount?: boolean
  clientLabel?: string // Display label for client
}

// Main stats configuration interface
export interface StatsConfig {
  title?: string
  dateLabel?: string
  
  // Field configurations
  fields: FieldConfig[]
  
  // Calculation configurations
  calculations: StatCalculationConfig
  
  // Custom stat cards (optional)
  customStatCards?: StatCard[]
  
  // Time period options (optional override)
  timePeriodOptions?: TimePeriodOption[]
}

// Props for the main DataStats component
export interface DataStatsProps {
  data: BaseDataItem[]
  config: StatsConfig
  isLoading?: boolean
}

// Calculated stats interface
export interface CalculatedStats {
  total: number
  today: number
  thisWeek: number
  thisMonth: number
  
  // Status counts (dynamic based on statusOptions)
  statusCounts: Record<string, number>
  
  // Category breakdown
  topCategory?: { name: string; count: number }
  categoryCounts: Record<string, number>
  
  // Assignee breakdown
  topAssignee?: { name: string; count: number }
  assigneeCounts: Record<string, number>
  
  // Amount calculations
  totalAmount?: number
  thisWeekAmount?: number
  thisMonthAmount?: number
  
  // Client counts
  uniqueClients?: number
}
