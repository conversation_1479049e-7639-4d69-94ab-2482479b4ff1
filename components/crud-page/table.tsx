'use client'

import { useState, useRef, ComponentType } from 'react'
import { Edit, Trash2, <PERSON><PERSON>eft, ArrowR<PERSON> } from 'lucide-react'

import { TableComponentProps } from './types'
import styles from './table.module.css'

export default function TableComponent({
  isScroll = true,
  headers,
  data,
  action,
  selected,
  didSelectAt,
  onEdit,
  onDelete,
  rowComponents,
  columnWidth = {},
  defaultColumnWidth = '200px',
  currentPage = 1,
  totalPages = 1,
  onPageChange = () => {},
  perPage = 10,
  pinnedColumns = [],
  isLoading = false
}: TableComponentProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const scrollContainerRef = useRef<HTMLDivElement>(null)

  const pagination = totalPages > 1
  const tableData = data || []

  // Shimmer loading component
  const ShimmerRow = ({ rowIndex }: { rowIndex: number }) => (
    <tr className={`group/row-body ${rowIndex % 2 === 0 ? 'bg-white' : ''}`}>
      {/* Row number column */}
      <td
        className={`sticky left-0 px-6 py-3 w-10 text-center ${
          rowIndex % 2 !== 0 ? 'bg-[#F5F5F5]' : 'bg-white'
        }`}
      >
        <div className="animate-pulse bg-gray-200 h-4 w-6 rounded mx-auto"></div>
      </td>

      {/* Data columns */}
      {headers.map((header, index) => {
        const isPinned = isPinnedColumn(header)
        return (
          <td
            key={index}
            style={{
              minWidth: columnWidth[headers[index]] || defaultColumnWidth,
              ...(isPinned ? {
                position: 'sticky',
                left: getPinnedColumnLeftPosition(header),
                zIndex: 15
              } : {})
            }}
            className={`px-2 py-3 text-center ${
              isPinned ? (rowIndex % 2 !== 0 ? 'bg-[#F5F5F5]' : 'bg-white') : ''
            } ${
              isPinned ? styles.pinnedColumn : ''
            }`}
          >
            <div className="animate-pulse bg-gray-200 h-4 rounded"></div>
          </td>
        )
      })}

      {/* Spacer column */}
      <td className="w-full"></td>

      {/* Action column */}
      {isCanShowAction() && (
        <td
          style={{ minWidth: defaultColumnWidth }}
          className={`flex items-center justify-center px-6 py-3 space-x-4 sticky right-0 z-10 h-[58px] ${
            rowIndex % 2 !== 0 ? 'bg-[#F5F5F5]' : 'bg-white'
          }`}
        >
          {action?.edit && (
            <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
          )}
          {action?.delete && (
            <div className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
          )}
        </td>
      )}
    </tr>
  )

  // Generate 3 shimmer rows when loading
  const renderShimmerRows = () => {
    return Array.from({ length: 3 }, (_, index) => (
      <ShimmerRow key={`shimmer-${index}`} rowIndex={index} />
    ))
  }

  // Helper functions for pinned columns
  const isPinnedColumn = (columnName: string): boolean => {
    return pinnedColumns.includes(columnName)
  }

  const getPinnedColumns = (): string[] => {
    return headers.filter(header => isPinnedColumn(header))
  }

  const getUnpinnedColumns = (): string[] => {
    return headers.filter(header => !isPinnedColumn(header))
  }

  const getColumnIndex = (columnName: string): number => {
    return headers.indexOf(columnName)
  }

  const getPinnedColumnLeftPosition = (columnName: string): string => {
    const pinnedCols = getPinnedColumns()
    const index = pinnedCols.indexOf(columnName)
    let leftPosition = 60 // Start after the row number column (60px)

    for (let i = 0; i < index; i++) {
      const colWidth = columnWidth[pinnedCols[i]] || defaultColumnWidth
      leftPosition += parseInt(colWidth.replace('px', ''))
    }

    return `${leftPosition}px`
  }

  const isCustomComponent = (columnIndex: number): boolean => {
    return !!(rowComponents &&
      rowComponents.hasOwnProperty(headers[columnIndex]) &&
      rowComponents[headers[columnIndex]] !== null &&
      rowComponents[headers[columnIndex]] !== undefined)
  }

  const getCustomComponent = (columnIndex: number): ComponentType<any> => {
    return rowComponents![headers[columnIndex]].component
  }

  const getCustomComponentProps = (columnIndex: number): Record<string, any> => {
    return rowComponents![headers[columnIndex]].props || {}
  }

  const onSelectRow = (id: string) => {
    if (didSelectAt) {
      didSelectAt(id)
    }
  }

  const handleEdit = (id: string) => {
    if (onEdit) {
      onEdit(id)
    }
  }

  const handleDelete = (id: string) => {
    if (onDelete) {
      onDelete(id)
    }
  }

  const isCanShowAction = (): boolean => {
    return action !== null && action !== undefined
  }

  const startDrag = (event: React.MouseEvent) => {
    setIsDragging(true)
    setStartX(event.pageX - (scrollContainerRef.current?.scrollLeft || 0))
  }

  const stopDrag = () => {
    setIsDragging(false)
  }

  const drag = (event: React.MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return
    scrollContainerRef.current.scrollLeft = startX - event.pageX
  }

  const prevPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1)
    }
  }

  const nextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1)
    }
  }

  return (
    <div className={`w-full h-full flex flex-col items-center ${isScroll ? 'overflow-y-hidden' : ''}`}>
      <div
        className={`relative w-full rounded-b-lg rounded-t-lg h-full bg-[#F5F5F5] text-xs text-black ${isScroll ? 'overflow-y-auto' : ''}`}
        ref={scrollContainerRef}
        onMouseDown={startDrag}
        onMouseUp={stopDrag}
        onMouseLeave={stopDrag}
        onMouseMove={drag}
      >
        <table className={`min-w-full max-w-full ${isScroll ? 'absolute' : ''}`}>
          <thead className="sticky top-0 bg-gray-300 z-20">
            <tr>
              <th scope="col" className="sticky left-0 bg-gray-300 px-6 py-3 w-10 text-center z-30">
                No
              </th>
              {headers.map((item, index) => {
                const isPinned = isPinnedColumn(item)
                return (
                  <th
                    key={`th-${index}`}
                    scope="col"
                    style={{
                      minWidth: columnWidth[item] || defaultColumnWidth,
                      ...(isPinned ? {
                        position: 'sticky',
                        left: getPinnedColumnLeftPosition(item),
                        zIndex: 25
                      } : {})
                    }}
                    className={`px-6 py-3 text-center ${isPinned ? `bg-gray-300 ${styles.pinnedColumnHeader} ${styles.pinnedColumn}` : ''}`}
                  >
                    {item}
                    {isPinned && (
                      <span className="ml-2 text-xs text-blue-600">📌</span>
                    )}
                  </th>
                )
              })}
              <th className="w-full no-print"></th>
              {isCanShowAction() && (
                <th
                  scope="col"
                  className="text-center sticky right-0 bg-gray-300 z-10 no-print"
                  style={{ minWidth: defaultColumnWidth }}
                >
                  Action
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              renderShimmerRows()
            ) : tableData.length > 0 ? (
              tableData.map((item, index) => (
                <tr 
                  key={`tr-${index}`}
                  className={`group/row-body cursor-pointer ${
                    index % 2 === 0 ? 'bg-white' : ''
                  } ${
                    selected?.id === item.id ? 'bg-[#000090] text-white font-bold' : ''
                  }`}
                  onClick={() => onSelectRow(item.id)}
                >
                  <td 
                    scope="row" 
                    className={`group-hover/row-body:bg-[#000080] group-hover/row-body:text-white sticky left-0 px-6 py-3 w-10 text-center ${
                      index % 2 !== 0 ? 'bg-[#F5F5F5]' : 'bg-white'
                    } ${
                      selected?.id === item.id ? 'bg-[#000090] text-white font-bold' : ''
                    }`}
                  >
                    {(currentPage - 1) * perPage + index + 1}
                  </td>
                  {headers.map((header, columnIndex) => {
                    const isPinned = isPinnedColumn(header)
                    return (
                      <td
                        key={columnIndex}
                        style={{
                          minWidth: columnWidth[headers[columnIndex]] || defaultColumnWidth,
                          ...(isPinned ? {
                            position: 'sticky',
                            left: getPinnedColumnLeftPosition(header),
                            zIndex: 15
                          } : {})
                        }}
                        className={`group-hover/row-body:bg-[#000080] group-hover/row-body:text-white px-2 py-3 text-center ${
                          isPinned ? (index % 2 !== 0 ? 'bg-[#F5F5F5]' : 'bg-white') : ''
                        } ${
                          isPinned && selected?.id === item.id ? 'bg-[#000090] text-white font-bold' : ''
                        } ${
                          isPinned ? styles.pinnedColumn : ''
                        }`}
                      >
                        {isCustomComponent(columnIndex) ? (
                          (() => {
                            const CustomComponent = getCustomComponent(columnIndex)
                            const customProps = getCustomComponentProps(columnIndex)
                            return <CustomComponent {...customProps} data={item.columns[columnIndex]} />
                          })()
                        ) : (
                          <span>{item.columns[columnIndex]}</span>
                        )}
                      </td>
                    )
                  })}
                  <td className="w-full no-print group-hover/row-body:bg-[#000080] group-hover/row-body:text-white">
                  </td>
                  {isCanShowAction() && (
                    <td 
                      style={{ minWidth: defaultColumnWidth }} 
                      className={`group-hover/row-body:bg-[#000080] group-hover/row-body:text-white flex items-center justify-center px-6 py-3 space-x-4 sticky right-0 z-10 no-print h-[58px] ${
                        index % 2 !== 0 ? 'bg-[#F5F5F5]' : 'bg-white'
                      } ${
                        selected?.id === item.id ? 'bg-[#000090] text-white font-bold' : ''
                      }`}
                    >
                      {action?.edit && (
                        <button 
                          className="bg-yellow-500 rounded-md p-0.5" 
                          onClick={(e) => {
                            e.stopPropagation()
                            handleEdit(item.id)
                          }}
                        >
                          <Edit className="w-6 h-6" />
                        </button>
                      )}
                      {action?.delete && (
                        <button 
                          className="bg-red-500 rounded-md p-0.5" 
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDelete(item.id)
                          }}
                        >
                          <Trash2 className="w-6 h-6" />
                        </button>
                      )}
                    </td>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={headers.length + (isCanShowAction() ? 2 : 1)} className="text-center py-8">
                  No data available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {pagination && (
        <div className="flex space-x-4 items-center mt-4 p-1 bg-gray-200 rounded-lg px-2 no-print">
          <button 
            onClick={prevPage} 
            className={`px-4 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 flex items-center ${
              currentPage === 1 ? 'hidden' : ''
            }`}
          >
            <ArrowLeft className="w-4 h-4" />
          </button>
          <span className="text-gray-700">
            Halaman {currentPage} / {totalPages}
          </span>
          <button 
            onClick={nextPage} 
            className={`px-4 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 flex items-center ${
              currentPage === totalPages ? 'hidden' : ''
            }`}
          >
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  )
}
