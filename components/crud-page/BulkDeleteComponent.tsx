'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { 
  Trash2,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

// Types
export interface DeleteResult {
  total: number
  successful: number
  failed: number
  errors: Array<{
    id: string
    message: string
  }>
}

export interface BulkDeleteConfig {
  // Data fetching
  fetchData: () => Promise<Record<string, any>[]>
  deleteData: (ids: string[]) => Promise<DeleteResult>
  
  // Table configuration
  headers: string[]
  transformToTableRow: (item: Record<string, any>) => { id: string; columns: string[] }
  
  // Display configuration
  itemName: string // e.g., "contact", "product"
  itemNamePlural: string // e.g., "contacts", "products"
}

interface BulkDeleteComponentProps {
  config: BulkDeleteConfig
  onSuccess?: () => void
}

export default function BulkDeleteComponent({ config, onSuccess }: BulkDeleteComponentProps) {
  // State
  const [data, setData] = useState<Record<string, any>[]>([])
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteResult, setDeleteResult] = useState<DeleteResult | null>(null)

  // Load data
  const loadData = async () => {
    setIsLoading(true)
    try {
      const fetchedData = await config.fetchData()
      setData(fetchedData)
    } catch (error) {
      console.error('Error loading data:', error)
      toast({
        title: 'Error',
        description: 'Failed to load data',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle row selection
  const handleRowSelect = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds(prev => [...prev, id])
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id))
    }
  }

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(data.map(item => item.id))
    } else {
      setSelectedIds([])
    }
  }

  // Execute bulk delete
  const executeBulkDelete = async () => {
    if (selectedIds.length === 0) return

    setIsDeleting(true)
    setDeleteResult(null)

    try {
      const result = await config.deleteData(selectedIds)
      setDeleteResult(result)

      if (result.successful > 0) {
        toast({
          title: 'Delete Complete',
          description: `${result.successful} ${config.itemNamePlural} deleted successfully`,
          variant: result.failed > 0 ? 'destructive' : 'default'
        })

        // Refresh data and clear selection
        setSelectedIds([])
        await loadData()

        if (onSuccess) {
          onSuccess()
        }
      }

      if (result.failed > 0) {
        toast({
          title: 'Partial Success',
          description: `${result.failed} ${config.itemNamePlural} could not be deleted`,
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Delete error:', error)
      toast({
        title: 'Error',
        description: 'Delete operation failed. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsDeleting(false)
    }
  }

  // Get selected items for display
  const selectedItems = data.filter(item => selectedIds.includes(item.id))

  // Load data on component mount
  useEffect(() => {
    loadData()
  }, [])

  return (
    <div className="space-y-6">
      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="w-5 h-5" />
            Bulk Delete {config.itemNamePlural}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-red-900">Warning</h4>
                <p className="text-sm text-red-700 mt-1">
                  This action will permanently delete the selected {config.itemNamePlural}. 
                  This cannot be undone. Please review your selection carefully.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selection Summary and Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Select {config.itemNamePlural} to Delete</span>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {selectedIds.length} of {data.length} selected
              </Badge>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button 
                    variant="destructive"
                    disabled={selectedIds.length === 0 || isDeleting}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Selected ({selectedIds.length})
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle className="flex items-center gap-2">
                      <AlertTriangle className="w-5 h-5 text-red-600" />
                      Confirm Bulk Delete
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      <div className="space-y-3">
                        <p>
                          You are about to permanently delete <strong>{selectedIds.length}</strong> {' '}
                          {selectedIds.length === 1 ? config.itemName : config.itemNamePlural}.
                        </p>
                        
                        {selectedItems.length > 0 && (
                          <div>
                            <p className="font-medium mb-2">Selected {config.itemNamePlural}:</p>
                            <div className="max-h-32 overflow-y-auto bg-gray-50 rounded p-2">
                              {selectedItems.slice(0, 10).map((item) => {
                                const row = config.transformToTableRow(item)
                                return (
                                  <div key={item.id} className="text-sm py-1">
                                    {row.columns[0]} {/* Show first column (usually name) */}
                                  </div>
                                )
                              })}
                              {selectedItems.length > 10 && (
                                <div className="text-sm text-gray-500 py-1">
                                  ... and {selectedItems.length - 10} more
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                        
                        <p className="text-red-600 font-medium">
                          This action cannot be undone. Are you sure you want to continue?
                        </p>
                      </div>
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={executeBulkDelete}
                      className="bg-red-600 hover:bg-red-700"
                      disabled={isDeleting}
                    >
                      {isDeleting ? 'Deleting...' : `Delete ${selectedIds.length} ${selectedIds.length === 1 ? config.itemName : config.itemNamePlural}`}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Select All Checkbox */}
          <div className="flex items-center space-x-2 mb-4">
            <Checkbox
              id="select-all"
              checked={selectedIds.length === data.length && data.length > 0}
              onCheckedChange={handleSelectAll}
            />
            <label htmlFor="select-all" className="text-sm font-medium">
              Select All ({data.length} {data.length === 1 ? config.itemName : config.itemNamePlural})
            </label>
          </div>

          {/* Data Table with Selection */}
          <div className="border rounded-lg overflow-hidden">
            <table className="w-full text-sm">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left font-medium text-gray-900 w-12">
                    Select
                  </th>
                  {config.headers.map((header) => (
                    <th key={header} className="px-3 py-2 text-left font-medium text-gray-900">
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan={config.headers.length + 1} className="px-3 py-8 text-center text-gray-500">
                      Loading data...
                    </td>
                  </tr>
                ) : data.length === 0 ? (
                  <tr>
                    <td colSpan={config.headers.length + 1} className="px-3 py-8 text-center text-gray-500">
                      No data available
                    </td>
                  </tr>
                ) : (
                  data.map((item) => {
                    const row = config.transformToTableRow(item)
                    const isSelected = selectedIds.includes(item.id)
                    return (
                      <tr key={item.id} className={`border-t ${isSelected ? 'bg-red-50' : ''}`}>
                        <td className="px-3 py-2">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={(checked) => handleRowSelect(item.id, checked as boolean)}
                          />
                        </td>
                        {row.columns.map((cell, cellIndex) => (
                          <td key={cellIndex} className="px-3 py-2">
                            {cell}
                          </td>
                        ))}
                      </tr>
                    )
                  })
                )}
              </tbody>
            </table>
          </div>

          {/* Selection Info */}
          {selectedIds.length > 0 && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4 text-red-600" />
                <span className="text-sm text-red-800">
                  {selectedIds.length} {selectedIds.length === 1 ? config.itemName : config.itemNamePlural} selected for deletion
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Results */}
      {deleteResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {deleteResult.failed === 0 ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <XCircle className="w-5 h-5 text-red-600" />
              )}
              Delete Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{deleteResult.total}</div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{deleteResult.successful}</div>
                <div className="text-sm text-gray-600">Deleted</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{deleteResult.failed}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
            </div>

            {/* Error Details */}
            {deleteResult.errors.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Delete Errors</h4>
                <div className="max-h-48 overflow-auto border rounded-lg">
                  {deleteResult.errors.map((error, index) => (
                    <div key={index} className="p-3 border-b last:border-b-0 bg-red-50">
                      <div className="flex items-start gap-2">
                        <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                        <div>
                          <div className="font-medium text-red-900">ID: {error.id}</div>
                          <div className="text-sm text-red-700">{error.message}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
