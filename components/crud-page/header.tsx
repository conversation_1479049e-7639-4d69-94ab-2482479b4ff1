'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Search, ChevronDown, Printer } from 'lucide-react'

// Simple debounce function
function debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
  let timeoutId: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }) as T
}

import { PageHeaderProps, DateFilterOption } from './types'

export default function PageHeaderComponent({
  title,
  total,
  onAdd,
  onBulk,
  onSort,
  onSearch,
  onCetak,
  sortOptions,
  dateFilterOptions,
  cetakRef,
  filters,
  onFilter,
  onDateFilter
}: PageHeaderProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSortField, setSelectedSortField] = useState('')
  const [selectedSortOrder, setSelectedSortOrder] = useState<'asc' | 'desc'>('asc')
  const [selectedFilters, setSelectedFilters] = useState<string[]>([])
  const [selectedDateFilter, setSelectedDateFilter] = useState<string | null>(null)
  const [showFilterDropdown, setShowFilterDropdown] = useState(false)
  const [showDateFilterDropdown, setShowDateFilterDropdown] = useState(false)
  
  const containerRef = useRef<HTMLDivElement>(null)
  const debounceDelay = 300

  // Create debounced search function
  const debouncedSearch = useCallback(
    debounce((query: string) => {
      if (onSearch) {
        onSearch(query)
      }
    }, debounceDelay),
    [onSearch]
  )

  const filterLabel = (() => {
    if (selectedFilters.length === 0) {
      return 'Pilih Filter'
    } else if (selectedFilters.length === 1) {
      return filters?.find(f => f.id === selectedFilters[0])?.name || 'Pilih Filter'
    } else {
      return `${filters?.find(f => f.id === selectedFilters[0])?.name || 'Pilih Filter'} (+${selectedFilters.length - 1})`
    }
  })()

  const selectedDateFilterLabel = (() => {
    return dateFilterOptions?.find(option => option.value === selectedDateFilter)?.label || 'Pilih Waktu'
  })()

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value
    setSearchQuery(value)
    debouncedSearch(value)
  }

  const handleSortField = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value
    setSelectedSortField(value)
    applySorting(value, selectedSortOrder)
  }

  const handleSortOrder = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value as 'asc' | 'desc'
    setSelectedSortOrder(value)
    applySorting(selectedSortField, value)
  }

  const applySorting = (field: string, order: 'asc' | 'desc') => {
    if (field && onSort) {
      onSort(field, order)
    }
  }

  const toggleFilterDropdown = () => {
    setShowFilterDropdown(!showFilterDropdown)
  }

  const handleFilters = (filterId: string) => {
    const newSelectedFilters = selectedFilters.includes(filterId)
      ? selectedFilters.filter(id => id !== filterId)
      : [...selectedFilters, filterId]
    
    setSelectedFilters(newSelectedFilters)
    
    if (newSelectedFilters.length > 0 && onFilter) {
      onFilter(newSelectedFilters)
    }
  }

  const toggleDateFilterDropdown = () => {
    setShowDateFilterDropdown(!showDateFilterDropdown)
  }

  const handleDateFilterChange = (option: DateFilterOption) => {
    setSelectedDateFilter(option.value)
    setShowDateFilterDropdown(false)
    applyDateFilter(option.value)
  }

  const applyDateFilter = (value: string) => {
    if (value && onDateFilter) {
      onDateFilter(value)
    }
  }

  const handleCetak = () => {
    if (onCetak) {
      onCetak()
    } else if (cetakRef?.current) {
      // Clone the element to avoid modifying the original
      const clone = cetakRef.current.cloneNode(true) as HTMLElement

      // Remove elements with class 'no-print'
      const noPrintElements = clone.querySelectorAll('.no-print')
      noPrintElements.forEach(element => element.remove())

      // Remove all inline styles
      const elementsWithStyle = clone.querySelectorAll('[style]')
      elementsWithStyle.forEach(element => {
        element.removeAttribute('style')
      })

      // Remove all classes
      const elementsWithClass = clone.querySelectorAll('[class]')
      elementsWithClass.forEach(element => {
        element.removeAttribute('class')
      })

      // Generate HTML for the print window
      const tableHtml = clone.outerHTML
      const printWindow = window.open('', '', 'height=800,width=1000')
      if (printWindow) {
        printWindow.document.write(`<html><head><title>${title}</title>`)
        printWindow.document.write('<style>table { width: 100%; border-collapse: collapse; } th, td { border: 1px solid black; padding: 8px; } th { background-color: #f4f4f4; }</style>')
        printWindow.document.write('</head><body>')
        printWindow.document.write(tableHtml)
        printWindow.document.write('</body></html>')
        printWindow.document.close()
        printWindow.focus()
        printWindow.print()
      }
    } else {
      alert("Belum bisa cetak")
    }
  }

  const handleClickOutside = (event: MouseEvent) => {
    if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
      setShowFilterDropdown(false)
      setShowDateFilterDropdown(false)
    }
  }

  useEffect(() => {
    document.addEventListener('click', handleClickOutside)
    if (dateFilterOptions && dateFilterOptions.length > 0) {
      setSelectedDateFilter(dateFilterOptions[0].value)
    }
    
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [dateFilterOptions])

  return (
    <div className="flex flex-col">
      <h2 className="text-black text-[22px] font-bold font-['DM Sans'] tracking-tight">{title}</h2>
      <div className="mb-3 flex justify-end items-center" ref={containerRef}>
        <div className="text-black text-[22px] font-medium font-['DM Sans'] tracking-tight">Total: {total}</div>

        {/* Search Input */}
        {onSearch && (
          <div className="relative ml-auto">
            <input 
              className="h-9 w-96 p-3 bg-white rounded border" 
              type="text" 
              placeholder="Search" 
              onChange={handleSearch}
              value={searchQuery} 
            />
            <button className="absolute right-2 top-2 text-gray-500" onClick={() => handleSearch({ target: { value: searchQuery } } as any)}>
              <Search className="w-6 h-6" />
            </button>
          </div>
        )}

        {/* Date Filter Dropdown */}
        {dateFilterOptions && dateFilterOptions.length > 0 && (
          <div className="relative ml-2">
            <button 
              className="h-9 w-48 bg-white rounded border flex justify-between items-center px-3" 
              onClick={toggleDateFilterDropdown}
            >
              <span>{selectedDateFilterLabel || 'Select Date Range'}</span>
              <ChevronDown className="w-6 h-6 ml-2" />
            </button>
            {showDateFilterDropdown && (
              <div className="absolute bg-white border rounded shadow-md mt-2 w-full z-30">
                {dateFilterOptions.map((option) => (
                  <button 
                    key={option.value} 
                    className="flex items-center p-2 hover:bg-gray-300 w-full" 
                    onClick={() => handleDateFilterChange(option)}
                  >
                    <input 
                      type="radio" 
                      id={option.value} 
                      value={option.value} 
                      checked={selectedDateFilter === option.value}
                      readOnly
                    />
                    <label htmlFor={option.value} className="ml-2">{option.label}</label>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Filter Dropdown */}
        {filters && filters.length > 0 && (
          <div className="relative ml-2">
            <button 
              className="h-9 w-48 bg-white rounded border flex justify-between items-center px-3" 
              onClick={toggleFilterDropdown}
            >
              <span>{filterLabel}</span>
              <ChevronDown className="w-6 h-6 ml-2" />
            </button>
            {showFilterDropdown && (
              <div className="absolute bg-white border rounded shadow-md mt-2 w-full z-30">
                {filters.map((filter) => (
                  <div key={filter.id} className="flex items-center p-2 hover:bg-gray-300">
                    <input 
                      type="checkbox" 
                      id={filter.id} 
                      value={filter.id} 
                      checked={selectedFilters.includes(filter.id)}
                      onChange={() => handleFilters(filter.id)} 
                    />
                    <label htmlFor={filter.id} className="ml-2 w-full">{filter.name}</label>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Sort Field */}
        {sortOptions && sortOptions.length > 0 && onSort && (
          <div className="ml-2">
            <select className="h-9 w-48 bg-white rounded border" onChange={handleSortField}>
              <option value="">Pilih Kolom</option>
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Sort Order */}
        {sortOptions && sortOptions.length > 0 && onSort && (
          <div className="ml-2">
            <select className="h-9 w-16 bg-white rounded border" onChange={handleSortOrder}>
              <option value="asc">ASC</option>
              <option value="desc">DESC</option>
            </select>
          </div>
        )}

        {/* Print Button */}
        {(onCetak || cetakRef) && (
          <div className="ml-2">
            <button 
              className="h-9 bg-white rounded-[10px] border flex items-center px-3" 
              onClick={handleCetak}
            >
              <span>Cetak</span>
              <Printer className="w-6 h-6" />
            </button>
          </div>
        )}

        {/* Add Button */}
        {onAdd && (
          <div className="ml-2">
            <button
              className="h-9 bg-blue-500 rounded-[10px] border flex items-center px-3"
              onClick={onAdd}
            >
              <span>Tambah</span>
            </button>
          </div>
        )}

        {/* Bulk Operations Button */}
        {onBulk && (
          <div className="ml-2">
            <button
              className="h-9 bg-purple-500 rounded-[10px] border flex items-center px-3 text-white"
              onClick={onBulk}
            >
              <span>Bulk Ops</span>
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
