'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ArrowLeft, Save, Upload, X } from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from './locales'

// Field types
export type FieldType = 
  | 'text' 
  | 'email' 
  | 'phone' 
  | 'number' 
  | 'textarea' 
  | 'select' 
  | 'multiselect'
  | 'image' 
  | 'file'
  | 'date'
  | 'datetime'
  | 'password'
  | 'url'

// Validation rules
export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: RegExp
  custom?: (value: any) => string | null
}

// Field configuration
export interface FieldConfig {
  name: string
  label: string
  type: FieldType
  placeholder?: string
  description?: string
  validation?: ValidationRule
  options?: Array<{ value: string; label: string }>
  disabled?: boolean
  hidden?: boolean
  defaultValue?: any
  accept?: string // For file inputs
  multiple?: boolean // For file/multiselect inputs
  rows?: number // For textarea
  step?: number // For number inputs
  group?: string // For grouping fields
}

// Form section for grouping
export interface FormSection {
  title: string
  description?: string
  fields: string[] // Field names in this section
}

// Main configuration interface
export interface DataEditorConfig {
  title: string
  subtitle?: string
  fields: FieldConfig[]
  sections?: FormSection[]
  
  // Data operations
  fetchData?: (id: string) => Promise<Record<string, any>>
  saveData: (data: Record<string, any>, isEdit: boolean) => Promise<void>
  
  // Navigation
  backRoute: string
  successRoute?: string
  
  // Customization
  submitButtonText?: string
  cancelButtonText?: string
  showImagePreview?: boolean
  maxFileSize?: number // in MB
  allowedFileTypes?: string[]
}

interface DataEditorPageProps {
  config: DataEditorConfig
  id?: string // If provided, it's edit mode
}

export default function DataEditorPage({ config, id }: DataEditorPageProps) {
  const { t } = useLocalization("crud-page", locales)
  const router = useRouter()
  const isEditMode = !!id
  
  // Form state
  const [formData, setFormData] = useState<Record<string, any>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [imagePreview, setImagePreview] = useState<Record<string, string>>({})
  const [loadError, setLoadError] = useState<string | null>(null)

  // Initialize form data
  useEffect(() => {
    const initializeForm = async () => {
      setIsLoading(true)
      try {
        let initialData: Record<string, any> = {}
        
        if (isEditMode && config.fetchData) {
          // Fetch existing data for edit mode
          initialData = await config.fetchData(id!)
        } else {
          // Set default values for create mode
          config.fields.forEach(field => {
            if (field.defaultValue !== undefined) {
              initialData[field.name] = field.defaultValue
            } else {
              // Set appropriate default values based on field type
              switch (field.type) {
                case 'multiselect':
                  initialData[field.name] = []
                  break
                case 'number':
                  initialData[field.name] = ''
                  break
                default:
                  initialData[field.name] = ''
              }
            }
          })
        }
        
        setFormData(initialData)
        setLoadError(null)
      } catch (error) {
        console.error('Error initializing form:', error)
        const errorMessage = error instanceof Error ? error.message : 'Failed to load data'
        setLoadError(errorMessage)
        toast({
          title: t('error_title'),
          description: errorMessage,
          variant: 'destructive'
        })
      } finally {
        setIsLoading(false)
      }
    }

    initializeForm()
  }, [id, isEditMode, config])

  // Validation function
  const validateField = (field: FieldConfig, value: any): string | null => {
    const { validation } = field
    if (!validation) return null

    // Required validation
    if (validation.required && (!value || (Array.isArray(value) && value.length === 0))) {
      return `${field.label} is required`
    }

    // Skip other validations if field is empty and not required
    if (!value && !validation.required) return null

    // String length validations
    if (typeof value === 'string') {
      if (validation.minLength && value.length < validation.minLength) {
        return `${field.label} must be at least ${validation.minLength} characters`
      }
      if (validation.maxLength && value.length > validation.maxLength) {
        return `${field.label} must not exceed ${validation.maxLength} characters`
      }
    }

    // Number validations
    if (field.type === 'number' && value !== '') {
      const numValue = Number(value)
      if (isNaN(numValue)) {
        return `${field.label} must be a valid number`
      }
      if (validation.min !== undefined && numValue < validation.min) {
        return `${field.label} must be at least ${validation.min}`
      }
      if (validation.max !== undefined && numValue > validation.max) {
        return `${field.label} must not exceed ${validation.max}`
      }
    }

    // Pattern validation
    if (validation.pattern && typeof value === 'string' && !validation.pattern.test(value)) {
      if (field.type === 'email') {
        return `${field.label} must be a valid email address`
      }
      if (field.type === 'phone') {
        return `${field.label} must be a valid phone number`
      }
      if (field.type === 'url') {
        return `${field.label} must be a valid URL`
      }
      return `${field.label} format is invalid`
    }

    // Custom validation
    if (validation.custom) {
      return validation.custom(value)
    }

    return null
  }

  // Handle input change
  const handleInputChange = (fieldName: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldName]: value }))
    
    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[fieldName]
        return newErrors
      })
    }
  }

  // Handle file upload
  const handleFileUpload = (fieldName: string, files: FileList | null) => {
    if (!files || files.length === 0) return

    const field = config.fields.find(f => f.name === fieldName)
    if (!field) return

    const file = files[0]
    
    // Validate file size
    if (config.maxFileSize && file.size > config.maxFileSize * 1024 * 1024) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: `File size must not exceed ${config.maxFileSize}MB`
      }))
      return
    }

    // Validate file type
    if (config.allowedFileTypes && !config.allowedFileTypes.includes(file.type)) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: `File type not allowed. Allowed types: ${config.allowedFileTypes?.join(', ')}`
      }))
      return
    }

    // Handle image preview
    if (field.type === 'image' && config.showImagePreview) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(prev => ({
          ...prev,
          [fieldName]: e.target?.result as string
        }))
      }
      reader.readAsDataURL(file)
    }

    handleInputChange(fieldName, file)
  }

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    
    config.fields.forEach(field => {
      if (field.hidden) return
      
      const error = validateField(field, formData[field.name])
      if (error) {
        newErrors[field.name] = error
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast({
        title: t('error_title'),
        description: 'Please fix the errors before submitting',
        variant: 'destructive'
      })
      return
    }

    setIsSaving(true)
    try {
      await config.saveData(formData, isEditMode)
      
      toast({
        title: t('success_title'),
        description: isEditMode ? 'Data updated successfully' : 'Data created successfully',
        variant: 'default'
      })

      // Navigate to success route or back
      if (config.successRoute) {
        router.push(config.successRoute)
      } else {
        router.push(config.backRoute)
      }
    } catch (error) {
      console.error('Error saving data:', error)
      toast({
        title: t('error_title'),
        description: 'Failed to save data',
        variant: 'destructive'
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Handle cancel
  const handleCancel = () => {
    router.push(config.backRoute)
  }



  if (isLoading) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="outline" size="icon" onClick={handleCancel} disabled>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {isEditMode ? `Edit ${config.title}` : `Create ${config.title}`}
            </h1>
            {config.subtitle && (
              <p className="text-gray-600">{config.subtitle}</p>
            )}
          </div>
        </div>

        <div className="flex flex-col items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">Loading data...</p>
        </div>
      </div>
    )
  }

  // Show error state if data loading failed
  if (loadError) {
    return (
      <div className="container mx-auto p-6 max-w-4xl">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="outline" size="icon" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {isEditMode ? `Edit ${config.title}` : `Create ${config.title}`}
            </h1>
            {config.subtitle && (
              <p className="text-gray-600">{config.subtitle}</p>
            )}
          </div>
        </div>

        <div className="flex flex-col items-center justify-center py-12">
          <div className="text-red-500 mb-4">
            <X className="h-12 w-12" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Data</h2>
          <p className="text-gray-600 mb-4 text-center">{loadError}</p>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              Go Back
            </Button>
            <Button onClick={() => {
              setLoadError(null)
              setIsLoading(true)
              // Retry loading
              const initializeForm = async () => {
                try {
                  let initialData: Record<string, any> = {}

                  if (isEditMode && config.fetchData) {
                    initialData = await config.fetchData(id!)
                  } else {
                    config.fields.forEach(field => {
                      if (field.defaultValue !== undefined) {
                        initialData[field.name] = field.defaultValue
                      } else {
                        switch (field.type) {
                          case 'multiselect':
                            initialData[field.name] = []
                            break
                          case 'number':
                            initialData[field.name] = ''
                            break
                          default:
                            initialData[field.name] = ''
                        }
                      }
                    })
                  }

                  setFormData(initialData)
                  setLoadError(null)
                } catch (error) {
                  console.error('Error initializing form:', error)
                  const errorMessage = error instanceof Error ? error.message : 'Failed to load data'
                  setLoadError(errorMessage)
                } finally {
                  setIsLoading(false)
                }
              }
              initializeForm()
            }}>
              Retry
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="icon" onClick={handleCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">
            {isEditMode ? `Edit ${config.title}` : `Create ${config.title}`}
          </h1>
          {config.subtitle && (
            <p className="text-gray-600">{config.subtitle}</p>
          )}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Render fields by sections or all together */}
        {config.sections ? (
          config.sections.map((section, sectionIndex) => (
            <Card key={sectionIndex}>
              <CardHeader>
                <CardTitle>{section.title}</CardTitle>
                {section.description && (
                  <p className="text-sm text-gray-600">{section.description}</p>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                {section.fields.map(fieldName => {
                  const field = config.fields.find(f => f.name === fieldName)
                  if (!field || field.hidden) return null
                  return renderField(field)
                })}
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="space-y-4 pt-6">
              {config.fields.map(field => {
                if (field.hidden) return null
                return renderField(field)
              })}
            </CardContent>
          </Card>
        )}

        {/* Action buttons */}
        <div className="flex justify-end gap-4 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isSaving}
          >
            {config.cancelButtonText || 'Cancel'}
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {config.submitButtonText || (isEditMode ? 'Update' : 'Create')}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )

  // Render individual field
  function renderField(field: FieldConfig) {
    const value = formData[field.name] || ''
    const error = errors[field.name]

    return (
      <div key={field.name} className="space-y-2">
        <Label htmlFor={field.name} className={field.validation?.required ? "after:content-['*'] after:text-red-500" : ""}>
          {field.label}
        </Label>
        
        {field.description && (
          <p className="text-sm text-gray-500">{field.description}</p>
        )}

        {/* Render different input types */}
        {field.type === 'text' && (
          <Input
            id={field.name}
            type="text"
            value={value}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled}
            className={error ? "border-red-500" : ""}
          />
        )}

        {field.type === 'email' && (
          <Input
            id={field.name}
            type="email"
            value={value}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled}
            className={error ? "border-red-500" : ""}
          />
        )}

        {field.type === 'phone' && (
          <Input
            id={field.name}
            type="tel"
            value={value}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            placeholder={field.placeholder || "+1234567890"}
            disabled={field.disabled}
            className={error ? "border-red-500" : ""}
          />
        )}

        {field.type === 'number' && (
          <Input
            id={field.name}
            type="number"
            value={value}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled}
            min={field.validation?.min}
            max={field.validation?.max}
            step={field.step}
            className={error ? "border-red-500" : ""}
          />
        )}

        {field.type === 'password' && (
          <Input
            id={field.name}
            type="password"
            value={value}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled}
            className={error ? "border-red-500" : ""}
          />
        )}

        {field.type === 'url' && (
          <Input
            id={field.name}
            type="url"
            value={value}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            placeholder={field.placeholder || "https://example.com"}
            disabled={field.disabled}
            className={error ? "border-red-500" : ""}
          />
        )}

        {field.type === 'date' && (
          <Input
            id={field.name}
            type="date"
            value={value}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            disabled={field.disabled}
            className={error ? "border-red-500" : ""}
          />
        )}

        {field.type === 'datetime' && (
          <Input
            id={field.name}
            type="datetime-local"
            value={value}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            disabled={field.disabled}
            className={error ? "border-red-500" : ""}
          />
        )}

        {field.type === 'textarea' && (
          <Textarea
            id={field.name}
            value={value}
            onChange={(e) => handleInputChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={field.disabled}
            rows={field.rows || 3}
            className={error ? "border-red-500" : ""}
          />
        )}

        {field.type === 'select' && (
          <Select
            value={value}
            onValueChange={(newValue) => handleInputChange(field.name, newValue)}
            disabled={field.disabled}
          >
            <SelectTrigger className={error ? "border-red-500" : ""}>
              <SelectValue placeholder={field.placeholder || "Select an option"} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {(field.type === 'image' || field.type === 'file') && (
          <div className="space-y-2">
            <Input
              id={field.name}
              type="file"
              onChange={(e) => handleFileUpload(field.name, e.target.files)}
              accept={field.accept}
              multiple={field.multiple}
              disabled={field.disabled}
              className={error ? "border-red-500" : ""}
            />
            
            {/* Image preview */}
            {field.type === 'image' && config.showImagePreview && imagePreview[field.name] && (
              <div className="relative inline-block">
                <img
                  src={imagePreview[field.name]}
                  alt="Preview"
                  className="max-w-xs max-h-48 rounded-lg border"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="absolute -top-2 -right-2 h-6 w-6"
                  onClick={() => {
                    setImagePreview(prev => {
                      const newPreview = { ...prev }
                      delete newPreview[field.name]
                      return newPreview
                    })
                    handleInputChange(field.name, '')
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        )}

        {/* Error message */}
        {error && (
          <p className="text-sm text-red-500">{error}</p>
        )}
      </div>
    )
  }
}


