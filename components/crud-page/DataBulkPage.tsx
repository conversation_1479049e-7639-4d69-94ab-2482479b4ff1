'use client'

import { useState, useRef } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft, 
  Upload, 
  Download, 
  FileText, 
  FileSpreadsheet, 
  Database,
  Trash2,
  Edit,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react'
import { useLocalization } from '@/hooks/useLocalization/client'
import { locales } from './locales'

// Supported file formats
export type SupportedFormat = 'csv' | 'json' | 'xlsx' | 'xml'

// Bulk operation types
export type BulkOperationType = 'import' | 'update' | 'delete'

// Validation result for each row
export interface ValidationResult {
  rowIndex: number
  isValid: boolean
  errors: string[]
  warnings: string[]
  data: Record<string, any>
}

// Bulk operation result
export interface BulkOperationResult {
  total: number
  successful: number
  failed: number
  errors: Array<{
    rowIndex: number
    error: string
    data?: Record<string, any>
  }>
}

// Field mapping for import
export interface FieldMapping {
  sourceField: string
  targetField: string
  required: boolean
  transform?: (value: any) => any
  validate?: (value: any) => string | null
}

// Configuration interface
export interface DataBulkConfig {
  title: string
  subtitle?: string
  
  // Supported operations
  supportedOperations: BulkOperationType[]
  supportedFormats: SupportedFormat[]
  
  // Field configuration
  fields: Array<{
    name: string
    label: string
    required: boolean
    type: 'string' | 'number' | 'date' | 'boolean' | 'email' | 'phone'
    example?: string
  }>
  
  // Data operations
  bulkImport?: (data: Record<string, any>[]) => Promise<BulkOperationResult>
  bulkUpdate?: (data: Record<string, any>[]) => Promise<BulkOperationResult>
  bulkDelete?: (ids: string[]) => Promise<BulkOperationResult>
  
  // Template and validation
  generateTemplate?: () => Record<string, any>[]
  validateData?: (data: Record<string, any>[]) => ValidationResult[]
  
  // Navigation
  backRoute: string
  
  // Customization
  maxFileSize?: number // in MB
  maxRecords?: number
  allowDuplicates?: boolean
  requireIdForUpdate?: boolean
  requireIdForDelete?: boolean
}

interface DataBulkPageProps {
  config: DataBulkConfig
}

export default function DataBulkPage({ config }: DataBulkPageProps) {
  const { t } = useLocalization("crud-page", locales)
  const router = useRouter()
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // State management
  const [activeOperation, setActiveOperation] = useState<BulkOperationType>('import')
  const [uploadedData, setUploadedData] = useState<Record<string, any>[]>([])
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [operationResult, setOperationResult] = useState<BulkOperationResult | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file size
    if (config.maxFileSize && file.size > config.maxFileSize * 1024 * 1024) {
      toast({
        title: t('error_title'),
        description: `File size must not exceed ${config.maxFileSize}MB`,
        variant: 'destructive'
      })
      return
    }

    // Validate file format
    const fileExtension = file.name.split('.').pop()?.toLowerCase() as SupportedFormat
    if (!config.supportedFormats.includes(fileExtension)) {
      toast({
        title: t('error_title'),
        description: `Unsupported file format. Supported formats: ${config.supportedFormats.join(', ')}`,
        variant: 'destructive'
      })
      return
    }

    setSelectedFile(file)
    setIsProcessing(true)
    setProgress(10)

    try {
      const data = await parseFile(file, fileExtension)
      setProgress(50)
      
      // Validate data if validation function is provided
      if (config.validateData) {
        const validationResults = config.validateData(data)
        setValidationResults(validationResults)
        setProgress(80)
      }
      
      setUploadedData(data)
      setProgress(100)
      
      toast({
        title: t('success_title'),
        description: `File uploaded successfully. ${data.length} records found.`,
        variant: 'default'
      })
    } catch (error) {
      console.error('File parsing error:', error)
      toast({
        title: t('error_title'),
        description: 'Failed to parse file. Please check the file format.',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  // Parse different file formats
  const parseFile = async (file: File, format: SupportedFormat): Promise<Record<string, any>[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string
          let data: Record<string, any>[] = []
          
          switch (format) {
            case 'csv':
              data = parseCSV(content)
              break
            case 'json':
              data = JSON.parse(content)
              if (!Array.isArray(data)) {
                throw new Error('JSON file must contain an array of objects')
              }
              break
            case 'xlsx':
              // For now, we'll handle XLSX as CSV (would need a library like xlsx for full support)
              data = parseCSV(content)
              break
            case 'xml':
              data = parseXML(content)
              break
            default:
              throw new Error(`Unsupported format: ${format}`)
          }
          
          // Validate record count
          if (config.maxRecords && data.length > config.maxRecords) {
            throw new Error(`Too many records. Maximum allowed: ${config.maxRecords}`)
          }
          
          resolve(data)
        } catch (error) {
          reject(error)
        }
      }
      
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsText(file)
    })
  }

  // Simple CSV parser
  const parseCSV = (content: string): Record<string, any>[] => {
    const lines = content.trim().split('\n')
    if (lines.length < 2) throw new Error('CSV file must have at least a header and one data row')
    
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
    const data: Record<string, any>[] = []
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
      const row: Record<string, any> = {}
      
      headers.forEach((header, index) => {
        row[header] = values[index] || ''
      })
      
      data.push(row)
    }
    
    return data
  }

  // Simple XML parser (basic implementation)
  const parseXML = (content: string): Record<string, any>[] => {
    // This is a very basic XML parser - in production, use a proper XML parsing library
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(content, 'text/xml')
    const items = xmlDoc.getElementsByTagName('item')
    
    const data: Record<string, any>[] = []
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      const row: Record<string, any> = {}
      
      for (let j = 0; j < item.children.length; j++) {
        const child = item.children[j]
        row[child.tagName] = child.textContent || ''
      }
      
      data.push(row)
    }
    
    return data
  }

  // Execute bulk operation
  const executeBulkOperation = async () => {
    if (uploadedData.length === 0) {
      toast({
        title: t('error_title'),
        description: 'No data to process',
        variant: 'destructive'
      })
      return
    }

    setIsProcessing(true)
    setProgress(0)
    setOperationResult(null)

    try {
      let result: BulkOperationResult

      switch (activeOperation) {
        case 'import':
          if (!config.bulkImport) throw new Error('Bulk import not supported')
          result = await config.bulkImport(uploadedData)
          break
        case 'update':
          if (!config.bulkUpdate) throw new Error('Bulk update not supported')
          result = await config.bulkUpdate(uploadedData)
          break
        case 'delete':
          if (!config.bulkDelete) throw new Error('Bulk delete not supported')
          const ids = uploadedData.map(item => item.id).filter(Boolean)
          result = await config.bulkDelete(ids)
          break
        default:
          throw new Error(`Unsupported operation: ${activeOperation}`)
      }

      setOperationResult(result)
      setProgress(100)

      toast({
        title: t('success_title'),
        description: `Operation completed. ${result.successful}/${result.total} records processed successfully.`,
        variant: result.failed > 0 ? 'destructive' : 'default'
      })
    } catch (error) {
      console.error('Bulk operation error:', error)
      toast({
        title: t('error_title'),
        description: error instanceof Error ? error.message : 'Bulk operation failed',
        variant: 'destructive'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Download template
  const downloadTemplate = () => {
    if (!config.generateTemplate) {
      toast({
        title: t('error_title'),
        description: 'Template generation not available',
        variant: 'destructive'
      })
      return
    }

    const templateData = config.generateTemplate()
    const csv = convertToCSV(templateData)
    downloadFile(csv, `${config.title.toLowerCase()}_template.csv`, 'text/csv')
  }

  // Convert data to CSV
  const convertToCSV = (data: Record<string, any>[]): string => {
    if (data.length === 0) return ''
    
    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n')
    
    return csvContent
  }

  // Download file
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Reset state
  const resetState = () => {
    setUploadedData([])
    setValidationResults([])
    setOperationResult(null)
    setSelectedFile(null)
    setProgress(0)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Handle back navigation
  const handleBack = () => {
    router.push(config.backRoute)
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" size="icon" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{config.title} - Bulk Operations</h1>
          {config.subtitle && (
            <p className="text-gray-600">{config.subtitle}</p>
          )}
        </div>
      </div>

      {/* Operation Tabs */}
      <Tabs value={activeOperation} onValueChange={(value) => {
        setActiveOperation(value as BulkOperationType)
        resetState()
      }} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          {config.supportedOperations.includes('import') && (
            <TabsTrigger value="import" className="flex items-center gap-2">
              <Upload className="w-4 h-4" />
              Import
            </TabsTrigger>
          )}
          {config.supportedOperations.includes('update') && (
            <TabsTrigger value="update" className="flex items-center gap-2">
              <Edit className="w-4 h-4" />
              Update
            </TabsTrigger>
          )}
          {config.supportedOperations.includes('delete') && (
            <TabsTrigger value="delete" className="flex items-center gap-2">
              <Trash2 className="w-4 h-4" />
              Delete
            </TabsTrigger>
          )}
        </TabsList>

        {/* Import Tab */}
        <TabsContent value="import" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="w-5 h-5" />
                Import Data
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* File Upload */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={config.supportedFormats.map(f => `.${f}`).join(',')}
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <div className="space-y-2">
                  <Upload className="w-12 h-12 mx-auto text-gray-400" />
                  <div>
                    <Button onClick={() => fileInputRef.current?.click()}>
                      Choose File
                    </Button>
                    <p className="text-sm text-gray-500 mt-2">
                      Supported formats: {config.supportedFormats.join(', ').toUpperCase()}
                    </p>
                    {config.maxFileSize && (
                      <p className="text-xs text-gray-400">
                        Maximum file size: {config.maxFileSize}MB
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Template Download */}
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">Need a template?</h3>
                  <p className="text-sm text-gray-600">Download a template file with the correct format</p>
                </div>
                <Button variant="outline" onClick={downloadTemplate}>
                  <Download className="w-4 h-4 mr-2" />
                  Download Template
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Update Tab */}
        <TabsContent value="update" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Edit className="w-5 h-5" />
                Update Data
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900">Update Requirements</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      {config.requireIdForUpdate 
                        ? "Each record must include an ID field to identify which record to update."
                        : "Records will be matched based on unique fields defined in the configuration."
                      }
                    </p>
                  </div>
                </div>
              </div>
              
              {/* File Upload for Update */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={config.supportedFormats.map(f => `.${f}`).join(',')}
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <div className="space-y-2">
                  <Edit className="w-12 h-12 mx-auto text-gray-400" />
                  <div>
                    <Button onClick={() => fileInputRef.current?.click()}>
                      Choose File to Update
                    </Button>
                    <p className="text-sm text-gray-500 mt-2">
                      Upload file with records to update
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Delete Tab */}
        <TabsContent value="delete" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Trash2 className="w-5 h-5" />
                Delete Data
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertCircle className="w-5 h-5 text-red-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-red-900">Delete Warning</h4>
                    <p className="text-sm text-red-700 mt-1">
                      This operation will permanently delete records. 
                      {config.requireIdForDelete 
                        ? " Each record must include an ID field."
                        : " Records will be identified by unique fields."
                      }
                    </p>
                  </div>
                </div>
              </div>
              
              {/* File Upload for Delete */}
              <div className="border-2 border-dashed border-red-300 rounded-lg p-6 text-center">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={config.supportedFormats.map(f => `.${f}`).join(',')}
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <div className="space-y-2">
                  <Trash2 className="w-12 h-12 mx-auto text-red-400" />
                  <div>
                    <Button variant="destructive" onClick={() => fileInputRef.current?.click()}>
                      Choose File with Records to Delete
                    </Button>
                    <p className="text-sm text-gray-500 mt-2">
                      Upload file with IDs or records to delete
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Progress Bar */}
      {isProcessing && (
        <Card className="mt-6">
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Preview and Validation */}
      {uploadedData.length > 0 && !isProcessing && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Data Preview ({uploadedData.length} records)</span>
              <div className="flex gap-2">
                <Button variant="outline" onClick={resetState}>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reset
                </Button>
                <Button onClick={executeBulkOperation} disabled={isProcessing}>
                  Execute {activeOperation.charAt(0).toUpperCase() + activeOperation.slice(1)}
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Validation Summary */}
            {validationResults.length > 0 && (
              <div className="mb-4 space-y-2">
                <h4 className="font-medium">Validation Summary</h4>
                <div className="flex gap-4">
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Valid: {validationResults.filter(r => r.isValid).length}
                  </Badge>
                  <Badge variant="destructive">
                    <XCircle className="w-3 h-3 mr-1" />
                    Invalid: {validationResults.filter(r => !r.isValid).length}
                  </Badge>
                  <Badge variant="secondary">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    Warnings: {validationResults.filter(r => r.warnings.length > 0).length}
                  </Badge>
                </div>
              </div>
            )}

            {/* Data Table Preview */}
            <div className="border rounded-lg overflow-hidden">
              <div className="max-h-96 overflow-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th className="px-3 py-2 text-left font-medium text-gray-900">#</th>
                      {Object.keys(uploadedData[0] || {}).map((key) => (
                        <th key={key} className="px-3 py-2 text-left font-medium text-gray-900">
                          {key}
                        </th>
                      ))}
                      {validationResults.length > 0 && (
                        <th className="px-3 py-2 text-left font-medium text-gray-900">Status</th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {uploadedData.slice(0, 100).map((row, index) => {
                      const validation = validationResults.find(v => v.rowIndex === index)
                      return (
                        <tr key={index} className={`border-t ${validation && !validation.isValid ? 'bg-red-50' : ''}`}>
                          <td className="px-3 py-2 text-gray-500">{index + 1}</td>
                          {Object.values(row).map((value, cellIndex) => (
                            <td key={cellIndex} className="px-3 py-2">
                              {String(value)}
                            </td>
                          ))}
                          {validationResults.length > 0 && (
                            <td className="px-3 py-2">
                              {validation && (
                                <div className="flex items-center gap-1">
                                  {validation.isValid ? (
                                    <CheckCircle className="w-4 h-4 text-green-600" />
                                  ) : (
                                    <XCircle className="w-4 h-4 text-red-600" />
                                  )}
                                  {validation.warnings.length > 0 && (
                                    <AlertCircle className="w-4 h-4 text-yellow-600" />
                                  )}
                                </div>
                              )}
                            </td>
                          )}
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
              {uploadedData.length > 100 && (
                <div className="bg-gray-50 px-3 py-2 text-sm text-gray-600 text-center">
                  Showing first 100 records of {uploadedData.length} total
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Operation Results */}
      {operationResult && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {operationResult.failed === 0 ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <AlertCircle className="w-5 h-5 text-yellow-600" />
              )}
              Operation Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{operationResult.total}</div>
                <div className="text-sm text-gray-600">Total</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{operationResult.successful}</div>
                <div className="text-sm text-gray-600">Successful</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{operationResult.failed}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
            </div>

            {/* Error Details */}
            {operationResult.errors.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Error Details</h4>
                <div className="max-h-48 overflow-auto border rounded-lg">
                  {operationResult.errors.map((error, index) => (
                    <div key={index} className="p-3 border-b last:border-b-0 bg-red-50">
                      <div className="flex items-start gap-2">
                        <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                        <div>
                          <div className="font-medium text-red-900">Row {error.rowIndex + 1}</div>
                          <div className="text-sm text-red-700">{error.error}</div>
                          {error.data && (
                            <div className="text-xs text-red-600 mt-1">
                              Data: {JSON.stringify(error.data)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Export types for external use
// export type { DataBulkConfig, SupportedFormat, BulkOperationType, ValidationResult, BulkOperationResult, FieldMapping }
