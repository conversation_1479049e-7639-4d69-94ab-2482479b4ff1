'use client'

import { useState } from 'react'
import { ChevronDown, TrendingUp, TrendingDown, Minus } from 'lucide-react'
import { StatItem, StatBreakdown, StatsResponse } from './DataPageEnhanced'

// Icon mapping for common icons
const iconMap: Record<string, React.ReactNode> = {
  'trending-up': <TrendingUp className="w-6 h-6" />,
  'trending-down': <TrendingDown className="w-6 h-6" />,
  'minus': <Minus className="w-6 h-6" />,
  // Add more icons as needed
}

// Generic stat card component
const StatCard: React.FC<{ stat: StatItem }> = ({ stat }) => {
  const cardColorClass = stat.cardColor || 'bg-gray-50'
  const textColorClass = stat.textColor || 'text-gray-900'
  
  return (
    <div className={`${cardColorClass} rounded-xl p-6 border border-gray-200`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {stat.icon && (
            <div className="mb-2">
              {iconMap[stat.icon] || <div className="w-6 h-6 bg-gray-300 rounded" />}
            </div>
          )}
          <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
          <p className={`text-2xl font-bold ${textColorClass} mb-1`}>{stat.value}</p>
          {stat.label && (
            <p className="text-sm text-gray-500">{stat.label}</p>
          )}
          {stat.description && (
            <p className="text-xs text-gray-400 mt-1">{stat.description}</p>
          )}
          {stat.trend && (
            <div className="flex items-center mt-2">
              {stat.trend.direction === 'up' && <TrendingUp className="w-4 h-4 text-green-600 mr-1" />}
              {stat.trend.direction === 'down' && <TrendingDown className="w-4 h-4 text-red-600 mr-1" />}
              {stat.trend.direction === 'neutral' && <Minus className="w-4 h-4 text-gray-600 mr-1" />}
              <span className={`text-sm ${
                stat.trend.direction === 'up' ? 'text-green-600' : 
                stat.trend.direction === 'down' ? 'text-red-600' : 'text-gray-600'
              }`}>
                {stat.trend.value} {stat.trend.label && `(${stat.trend.label})`}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Breakdown section component
const BreakdownSection: React.FC<{ breakdown: StatBreakdown }> = ({ breakdown }) => (
  <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
    <h3 className="text-lg font-medium text-gray-900 mb-4">{breakdown.title}</h3>
    <div className="space-y-3">
      {breakdown.items.map((item, index) => (
        <div key={index} className="flex items-center justify-between">
          <div className="flex items-center">
            {item.color && (
              <div 
                className="w-3 h-3 rounded-full mr-3" 
                style={{ backgroundColor: item.color }}
              />
            )}
            <span className="text-sm text-gray-700">{item.label}</span>
          </div>
          <div className="text-right">
            <span className="text-sm font-medium text-gray-900">{item.value}</span>
            {item.percentage !== undefined && (
              <span className="text-xs text-gray-500 ml-2">({item.percentage}%)</span>
            )}
          </div>
        </div>
      ))}
    </div>
  </div>
)

// Shimmer loading components
const ShimmerCard = () => (
  <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
    <div className="animate-pulse">
      <div className="w-6 h-6 bg-gray-200 rounded mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-20 mb-1"></div>
      <div className="h-8 bg-gray-200 rounded w-16 mb-1"></div>
      <div className="h-3 bg-gray-200 rounded w-24"></div>
    </div>
  </div>
)

// Props interface
interface DataStatsProps {
  data: StatsResponse
  isLoading?: boolean
  title?: string
}

// Main DataStats component
export default function DataStats({ 
  data, 
  isLoading = false,
  title = "Statistics"
}: DataStatsProps) {
  // Show shimmer loading state
  if (isLoading) {
    return (
      <div className="mb-6">
        <div className="mb-6">
          <div className="animate-pulse bg-gray-200 h-6 w-48 rounded mb-2"></div>
          <div className="animate-pulse bg-gray-200 h-4 w-32 rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {Array.from({ length: 8 }, (_, index) => (
            <ShimmerCard key={`shimmer-${index}`} />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-6">
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        {data.summary && (
          <p className="text-sm text-gray-600">
            {data.summary.total} total items
            {data.summary.period && ` for ${data.summary.period}`}
            {data.summary.lastUpdated && ` • Last updated: ${data.summary.lastUpdated}`}
          </p>
        )}
      </div>

      {/* Main Stats Grid */}
      {data.stats && data.stats.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {data.stats.map((stat) => (
            <StatCard key={stat.id} stat={stat} />
          ))}
        </div>
      )}

      {/* Breakdown Sections */}
      {data.breakdowns && data.breakdowns.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {data.breakdowns.map((breakdown) => (
            <BreakdownSection key={breakdown.id} breakdown={breakdown} />
          ))}
        </div>
      )}

      {/* Empty State */}
      {(!data.stats || data.stats.length === 0) && (!data.breakdowns || data.breakdowns.length === 0) && (
        <div className="text-center py-12">
          <p className="text-gray-500">No statistics available</p>
        </div>
      )}
    </div>
  )
}
