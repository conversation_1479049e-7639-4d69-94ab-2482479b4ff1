'use client'

import { useMemo, useState, useEffect, useRef } from 'react'
import { Calendar, Users, Activity, TrendingUp, Clock, CheckCircle, XCircle, AlertCircle, ChevronDown, CalendarDays } from 'lucide-react'

// Generic Types
interface BaseDataItem {
  id: string
  date: string // Main date field for filtering
  status: string
  amount?: string // For revenue calculations
  category?: string // For grouping (e.g., procedure type, product category)
  assignedTo?: string // For tracking who handled it (doctor, staff, etc.)
  clientId?: string // For unique client/patient counting
  [key: string]: any // Allow additional fields
}

interface DataStatsProps {
  data: BaseDataItem[]
  title?: string
  dateLabel?: string
  statusOptions?: string[]
  categoryLabel?: string
  assignedToLabel?: string
  clientLabel?: string
  amountLabel?: string
  amountPrefix?: string
}

type TimePeriod = 'today' | 'yesterday' | 'this_week' | 'last_week' | 'this_month' | 'last_month' | 'this_year' | 'last_year' | 'custom' | 'all'

interface TimePeriodOption {
  value: TimePeriod
  label: string
  description: string
}

interface StatCard {
  title: string
  value: string | number
  subtitle?: string
  icon: React.ReactNode
  color: string
  bgColor: string
  trend?: {
    value: string
    isPositive: boolean
  }
}

export default function DataStats({ 
  data, 
  title = "Data Statistics",
  dateLabel = "Date",
  statusOptions = ['Completed', 'In Progress', 'Scheduled', 'Cancelled'],
  categoryLabel = "Category",
  assignedToLabel = "Assigned To",
  clientLabel = "Client",
  amountLabel = "Amount",
  amountPrefix = "$"
}: DataStatsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('this_month')
  const [showDropdown, setShowDropdown] = useState(false)
  const [customStartDate, setCustomStartDate] = useState('')
  const [customEndDate, setCustomEndDate] = useState('')
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const timePeriodOptions: TimePeriodOption[] = [
    { value: 'today', label: 'Today', description: 'Today\'s data' },
    { value: 'yesterday', label: 'Yesterday', description: 'Yesterday\'s data' },
    { value: 'this_week', label: 'This Week', description: 'Current week' },
    { value: 'last_week', label: 'Last Week', description: 'Previous week' },
    { value: 'this_month', label: 'This Month', description: 'Current month' },
    { value: 'last_month', label: 'Last Month', description: 'Previous month' },
    { value: 'this_year', label: 'This Year', description: 'Current year' },
    { value: 'last_year', label: 'Last Year', description: 'Previous year' },
    { value: 'custom', label: 'Custom Range', description: 'Select custom date range' },
    { value: 'all', label: 'All Data', description: 'All time periods' }
  ]

  const getFilteredData = (period: TimePeriod, startDate?: string, endDate?: string) => {
    if (!data || data.length === 0) return []

    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    switch (period) {
      case 'today':
        return data.filter(item => {
          const itemDate = new Date(item.date)
          return itemDate >= today && itemDate < new Date(today.getTime() + 24 * 60 * 60 * 1000)
        })

      case 'yesterday':
        const yesterday = new Date(today)
        yesterday.setDate(yesterday.getDate() - 1)
        return data.filter(item => {
          const itemDate = new Date(item.date)
          return itemDate >= yesterday && itemDate < today
        })

      case 'this_week':
        const thisWeekStart = new Date(today)
        thisWeekStart.setDate(today.getDate() - today.getDay())
        return data.filter(item => {
          const itemDate = new Date(item.date)
          return itemDate >= thisWeekStart
        })

      case 'last_week':
        const lastWeekEnd = new Date(today)
        lastWeekEnd.setDate(today.getDate() - today.getDay())
        const lastWeekStart = new Date(lastWeekEnd)
        lastWeekStart.setDate(lastWeekEnd.getDate() - 7)
        return data.filter(item => {
          const itemDate = new Date(item.date)
          return itemDate >= lastWeekStart && itemDate < lastWeekEnd
        })

      case 'this_month':
        const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
        return data.filter(item => {
          const itemDate = new Date(item.date)
          return itemDate >= thisMonthStart
        })

      case 'last_month':
        const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 1)
        return data.filter(item => {
          const itemDate = new Date(item.date)
          return itemDate >= lastMonthStart && itemDate < lastMonthEnd
        })

      case 'this_year':
        const thisYearStart = new Date(now.getFullYear(), 0, 1)
        return data.filter(item => {
          const itemDate = new Date(item.date)
          return itemDate >= thisYearStart
        })

      case 'last_year':
        const lastYearStart = new Date(now.getFullYear() - 1, 0, 1)
        const lastYearEnd = new Date(now.getFullYear(), 0, 1)
        return data.filter(item => {
          const itemDate = new Date(item.date)
          return itemDate >= lastYearStart && itemDate < lastYearEnd
        })

      case 'custom':
        if (!startDate || !endDate) return data
        const customStart = new Date(startDate)
        const customEnd = new Date(endDate)
        customEnd.setHours(23, 59, 59, 999) // Include the end date
        return data.filter(item => {
          const itemDate = new Date(item.date)
          return itemDate >= customStart && itemDate <= customEnd
        })

      case 'all':
      default:
        return data
    }
  }

  const filteredData = getFilteredData(selectedPeriod, customStartDate, customEndDate)

  const stats = useMemo(() => {
    if (!filteredData || filteredData.length === 0) return null

    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const thisWeekStart = new Date(today)
    thisWeekStart.setDate(today.getDate() - today.getDay())
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)

    // Use filtered data for calculations
    const currentData = filteredData

    // Additional time-based calculations for context
    const todayData = currentData.filter(item => {
      const itemDate = new Date(item.date)
      return itemDate >= today && itemDate < new Date(today.getTime() + 24 * 60 * 60 * 1000)
    })

    const thisWeekData = currentData.filter(item => {
      const itemDate = new Date(item.date)
      return itemDate >= thisWeekStart
    })

    const thisMonthData = currentData.filter(item => {
      const itemDate = new Date(item.date)
      return itemDate >= thisMonthStart
    })

    // Status counts
    const statusCounts = currentData.reduce((acc, item) => {
      acc[item.status] = (acc[item.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Most common categories
    const categoryCounts = currentData.reduce((acc, item) => {
      if (item.category) {
        acc[item.category] = (acc[item.category] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    const topCategory = Object.entries(categoryCounts)
      .sort(([,a], [,b]) => b - a)[0]

    // Most active assignee
    const assigneeCounts = currentData.reduce((acc, item) => {
      if (item.assignedTo) {
        acc[item.assignedTo] = (acc[item.assignedTo] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    const topAssignee = Object.entries(assigneeCounts)
      .sort(([,a], [,b]) => b - a)[0]

    // Calculate total revenue
    const totalRevenue = currentData.reduce((sum, item) => {
      if (item.amount) {
        const amount = parseInt(item.amount.replace(/[^\d]/g, ''))
        return sum + amount
      }
      return sum
    }, 0)

    const thisWeekRevenue = thisWeekData.reduce((sum, item) => {
      if (item.amount) {
        const amount = parseInt(item.amount.replace(/[^\d]/g, ''))
        return sum + amount
      }
      return sum
    }, 0)

    return {
      total: currentData.length,
      today: todayData.length,
      thisWeek: thisWeekData.length,
      thisMonth: thisMonthData.length,
      completed: statusCounts[statusOptions[0]] || 0,
      inProgress: statusCounts[statusOptions[1]] || 0,
      scheduled: statusCounts[statusOptions[2]] || 0,
      cancelled: statusCounts[statusOptions[3]] || 0,
      topCategory: topCategory ? { name: topCategory[0], count: topCategory[1] } : null,
      topAssignee: topAssignee ? { name: topAssignee[0], count: topAssignee[1] } : null,
      totalRevenue,
      thisWeekRevenue,
      uniqueClients: new Set(currentData.map(item => item.clientId).filter(Boolean)).size
    }
  }, [filteredData])

  if (!stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-100 animate-pulse rounded-xl p-6 h-32"></div>
        <div className="bg-gray-100 animate-pulse rounded-xl p-6 h-32"></div>
        <div className="bg-gray-100 animate-pulse rounded-xl p-6 h-32"></div>
        <div className="bg-gray-100 animate-pulse rounded-xl p-6 h-32"></div>
      </div>
    )
  }

  const statCards: StatCard[] = [
    {
      title: 'Total Items',
      value: stats.total,
      subtitle: `${stats.thisMonth} this month`,
      icon: <Activity className="w-6 h-6" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      trend: {
        value: `+${stats.thisWeek} this week`,
        isPositive: true
      }
    },
    {
      title: 'Today',
      value: stats.today,
      subtitle: 'Items for today',
      icon: <Calendar className="w-6 h-6" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: statusOptions[0] || 'Completed',
      value: stats.completed,
      subtitle: `${((stats.completed / stats.total) * 100).toFixed(1)}% of total`,
      icon: <CheckCircle className="w-6 h-6" />,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50'
    },
    {
      title: statusOptions[1] || 'In Progress',
      value: stats.inProgress,
      subtitle: 'Currently active',
      icon: <Clock className="w-6 h-6" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50'
    },
    {
      title: statusOptions[2] || 'Scheduled',
      value: stats.scheduled,
      subtitle: 'Waiting to start',
      icon: <AlertCircle className="w-6 h-6" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: statusOptions[3] || 'Cancelled',
      value: stats.cancelled,
      subtitle: `${((stats.cancelled / stats.total) * 100).toFixed(1)}% of total`,
      icon: <XCircle className="w-6 h-6" />,
      color: 'text-red-600',
      bgColor: 'bg-red-50'
    },
    {
      title: `Total ${amountLabel}`,
      value: `${amountPrefix}${stats.totalRevenue.toLocaleString()}`,
      subtitle: `${amountPrefix}${stats.thisWeekRevenue.toLocaleString()} this week`,
      icon: <TrendingUp className="w-6 h-6" />,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      trend: {
        value: `${((stats.thisWeekRevenue / stats.totalRevenue) * 100).toFixed(1)}%`,
        isPositive: true
      }
    },
    {
      title: `Unique ${clientLabel}s`,
      value: stats.uniqueClients,
      subtitle: `Total different ${clientLabel.toLowerCase()}s`,
      icon: <Users className="w-6 h-6" />,
      color: 'text-cyan-600',
      bgColor: 'bg-cyan-50'
    }
  ]

  const selectedOption = timePeriodOptions.find(option => option.value === selectedPeriod)

  return (
    <div className="mb-6">
      {/* Time Period Filter */}
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-1">
            {title}
          </h3>
          <p className="text-sm text-gray-600">
            {selectedOption?.description} • {stats?.total || 0} items
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Time Period Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[200px]"
            >
              <CalendarDays className="w-4 h-4 text-gray-500" />
              <span className="flex-1 text-left">{selectedOption?.label}</span>
              <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${showDropdown ? 'rotate-180' : ''}`} />
            </button>
            
            {showDropdown && (
              <div className="absolute top-full left-0 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
                {timePeriodOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => {
                      setSelectedPeriod(option.value)
                      setShowDropdown(false)
                    }}
                    className={`w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 ${
                      selectedPeriod === option.value ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                    }`}
                  >
                    <div className="font-medium">{option.label}</div>
                    <div className="text-xs text-gray-500 mt-1">{option.description}</div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Custom Date Range Inputs */}
          {selectedPeriod === 'custom' && (
            <div className="flex gap-2">
              <input
                type="date"
                value={customStartDate}
                onChange={(e) => setCustomStartDate(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Start Date"
              />
              <input
                type="date"
                value={customEndDate}
                onChange={(e) => setCustomEndDate(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="End Date"
              />
            </div>
          )}
        </div>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {statCards.map((card, index) => (
          <div
            key={index}
            className={`${card.bgColor} rounded-xl p-6 border border-opacity-20 hover:shadow-lg transition-all duration-200 hover:scale-105`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className={`${card.color} mb-2`}>
                  {card.icon}
                </div>
                <h3 className="text-sm font-medium text-gray-600 mb-1">
                  {card.title}
                </h3>
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  {card.value}
                </p>
                {card.subtitle && (
                  <p className="text-xs text-gray-500">
                    {card.subtitle}
                  </p>
                )}
                {card.trend && (
                  <div className={`flex items-center mt-2 text-xs ${
                    card.trend.isPositive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <span className="mr-1">
                      {card.trend.isPositive ? '↗' : '↘'}
                    </span>
                    {card.trend.value}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Additional Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Top Category Card */}
        {stats.topCategory && (
          <div className="bg-gradient-to-r from-violet-50 to-purple-50 rounded-xl p-6 border border-violet-200">
            <div className="flex items-center mb-3">
              <div className="bg-violet-100 p-2 rounded-lg mr-3">
                <Activity className="w-5 h-5 text-violet-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800">
                Top {categoryLabel}
              </h3>
            </div>
            <p className="text-xl font-bold text-violet-700 mb-1">
              {stats.topCategory.name}
            </p>
            <p className="text-sm text-gray-600">
              {stats.topCategory.count} items ({((stats.topCategory.count / stats.total) * 100).toFixed(1)}%)
            </p>
          </div>
        )}

        {/* Top Assignee Card */}
        {stats.topAssignee && (
          <div className="bg-gradient-to-r from-amber-50 to-yellow-50 rounded-xl p-6 border border-amber-200">
            <div className="flex items-center mb-3">
              <div className="bg-amber-100 p-2 rounded-lg mr-3">
                <Users className="w-5 h-5 text-amber-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800">
                Most Active {assignedToLabel}
              </h3>
            </div>
            <p className="text-xl font-bold text-amber-700 mb-1">
              {stats.topAssignee.name}
            </p>
            <p className="text-sm text-gray-600">
              {stats.topAssignee.count} items ({((stats.topAssignee.count / stats.total) * 100).toFixed(1)}%)
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
