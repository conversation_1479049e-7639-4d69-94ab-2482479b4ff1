import clsx from 'clsx';
import { format } from 'date-fns';
import { Message } from '@/hooks/useReceiveMessages';
import { CheckCheck, Clock, Info } from 'lucide-react';

export function ChatMessage({ message }: { message: Message }) {

  return (
    <>
      <div
        className={`flex gap-3 ${!message.fromMe ? 'justify-start' : 'justify-end'
          }`}
      >
        {message.fromMe && (
          <div className='flex flex-col items-center gap-1 w-fit'>
            <div
              className={`w-8 h-8 bg-sp-primary rounded-full flex items-center justify-center`}
            >
              <span className='text-sm font-medium text-white'>
                {message._data.pushName?.charAt(0) || 'CS'}
              </span>
            </div>
          </div>
        )}
        <div
          className={clsx(
            'max-w-[50%] ',
            !message.fromMe ? '' : ''
          )}
        >
          <div
            className={clsx(
              'rounded-lg p-3 break-words',
              message.fromMe
                ? 'bg-primary text-primary-foreground'
                : message.ack == 2 ? 'bg-gray-300' : 'bg-muted text-foreground'
            )}
          >
            <p className="text-sm max-w-xs">{message.body}</p>
          </div>
          <div className={clsx("flex items-center gap-2 mt-1 text-xs", message.fromMe ? '' : 'flex-row-reverse')}>
            {<span>{message._data.pushName}</span>}
            <div className={message.fromMe ? 'text-right w-full' : ''}>
              <span >{format(message._data.messageTimestamp, 'hh:mm')}</span>
            </div>
            <div>
              {message.ack >= 2 ?
                <CheckCheck className={clsx('h-4 w-4', message.ack == 2 ? '' : 'text-sp-primary')} />
                : message.ack == 0 ? <Clock className={clsx('h-4 w-4')} /> : message.ack == -1 ? <Info className={clsx('h-4 w-4 text-destructive')} /> : null}
            </div>
          </div>
        </div>
        {!message.fromMe && (
          <div className='w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center'>
            <span className='text-sm font-medium'>
              {message._data.pushName?.charAt(0) || 'C'}
            </span>
          </div>
        )}
      </div>
    </>
  );
}