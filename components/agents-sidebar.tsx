"use client"

import { useState } from "react"
import { Users, Eye, MessageCircle, Clock, Circle } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"

const agents = [
  {
    id: "agent-1",
    name: "<PERSON>",
    role: "Senior Support Agent",
    status: "active",
    isViewing: true,
    isTyping: false,
    avatar: "AT",
    lastSeen: "now",
  },
  {
    id: "agent-2",
    name: "<PERSON>",
    role: "Support Agent",
    status: "active",
    isViewing: true,
    isTyping: true,
    avatar: "MC",
    lastSeen: "now",
  },
  {
    id: "agent-3",
    name: "<PERSON>",
    role: "Team Lead",
    status: "away",
    isViewing: false,
    isTyping: false,
    avatar: "ED",
    lastSeen: "5 min ago",
  },
  {
    id: "agent-4",
    name: "<PERSON>",
    role: "Support Agent",
    status: "offline",
    isViewing: false,
    isTyping: false,
    avatar: "JW",
    lastSeen: "2 hours ago",
  },
]

interface AgentsSidebarProps {
  conversationId: string
}

export function AgentsSidebar({ conversationId }: AgentsSidebarProps) {
  const [showAllAgents, setShowAllAgents] = useState(false)

  const activeAgents = agents.filter((agent) => agent.isViewing)
  const otherAgents = agents.filter((agent) => !agent.isViewing)

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-500"
      case "away":
        return "text-yellow-500"
      case "offline":
        return "text-gray-400"
      default:
        return "text-gray-400"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Online"
      case "away":
        return "Away"
      case "offline":
        return "Offline"
      default:
        return "Unknown"
    }
  }

  return (
    <Card className="rounded-none border-0 bg-background flex-1 overflow-hidden">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Users className="h-5 w-5" />
          Team Collaboration
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4 overflow-y-auto flex-1">
        {/* Currently Viewing */}
        <div>
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Currently Viewing ({activeAgents.length})
          </h4>
          <div className="space-y-2">
            {activeAgents.map((agent) => (
              <div
                key={agent.id}
                className="flex items-center gap-3 p-2 bg-green-50 rounded-lg border border-green-200"
              >
                <div className="relative">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="text-xs bg-primary/10">{agent.avatar}</AvatarFallback>
                  </Avatar>
                  <Circle
                    className={`absolute -bottom-1 -right-1 h-3 w-3 fill-current ${getStatusColor(agent.status)}`}
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-sm truncate">{agent.name}</span>
                    {agent.isTyping && (
                      <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700">
                        <MessageCircle className="h-3 w-3 mr-1" />
                        Typing
                      </Badge>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">{agent.role}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Other Team Members */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Team Members
            </h4>
            <Button variant="ghost" size="sm" onClick={() => setShowAllAgents(!showAllAgents)} className="text-xs">
              {showAllAgents ? "Show Less" : "Show All"}
            </Button>
          </div>

          <div className="space-y-2">
            {(showAllAgents ? otherAgents : otherAgents.slice(0, 2)).map((agent) => (
              <div
                key={agent.id}
                className="flex items-center gap-3 p-2 hover:bg-muted/50 rounded-lg transition-colors"
              >
                <div className="relative">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="text-xs bg-muted">{agent.avatar}</AvatarFallback>
                  </Avatar>
                  <Circle
                    className={`absolute -bottom-1 -right-1 h-3 w-3 fill-current ${getStatusColor(agent.status)}`}
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-sm truncate">{agent.name}</span>
                    <span className={`text-xs ${getStatusColor(agent.status)}`}>{getStatusText(agent.status)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground truncate">{agent.role}</span>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {agent.lastSeen}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="pt-2 border-t">
          <div className="grid grid-cols-2 gap-2">
            <Button variant="outline" size="sm" className="text-xs">
              Invite Agent
            </Button>
            <Button variant="outline" size="sm" className="text-xs">
              Transfer Chat
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
