"use client";

import { Plus, QrCode } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import Image from "next/image";
import { useQrSession } from "@/hooks/useQrSession";
import { useEffect, useState } from "react";
import { getCookie } from "@/lib/utils";
import { toast } from "sonner";
import { Device } from "@/hooks/useDevices";

export default function AddDevices({ devices, loading, fetchDevices }: { devices: Device[], loading?: boolean, fetchDevices: () => void }) {
  const [open, setOpen] = useState<boolean>(false);
  const [cookieStore, setCookieStore] = useState<string>("waha");

  useEffect(() => {
    const stored = getCookie("preferred_provider");
    if (!stored || !cookieStore) return
    setCookieStore(stored);
  }, [cookieStore.length]);

  const { startSession, qrCode, timeLeft } = useQrSession(
    devices,
    () => {
      fetchDevices();
    },
    () => {
      setOpen(false)
    });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button onClick={startSession}>
          {cookieStore == 'waha' ? (
            <>
              <Plus className="h-4 w-4" />
              Add Device
            </>
          ) : (
            <>
              <QrCode className="h-4 w-4" />
              Link Device
            </>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Link New Device</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            To link a new device, open WhatsApp on your phone, go to{" "}
            <strong>Settings &gt; Linked Devices</strong>, and scan the QR code below.
          </p>
          <div className="flex justify-center flex-col items-center gap-2">
            <div className="p-6 border rounded bg-muted/50">
              {!qrCode ? (
                <QrCode className="w-32 h-32 text-muted-foreground" />
              ) : (
                <Image
                  src={qrCode}
                  alt="qr-code"
                  width={240}
                  height={240}
                />
              )}
            </div>
            {qrCode && (
              <p className="text-xs text-gray-300 text-center">
                Available: {timeLeft} seconds
              </p>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
