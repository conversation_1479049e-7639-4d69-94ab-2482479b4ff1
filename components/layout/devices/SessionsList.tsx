"use client";

import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { Trash2 } from "lucide-react";
import { Device, useDevices } from "@/hooks/useDevices";
import { api } from "@/lib/axios";
import { getCookie } from "@/lib/utils";

export default function SessionsList({devices = [], loading = false}:{devices:Device[], loading?:boolean}) {
  const [search, setSearch] = useState("");
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  useEffect(() => {
    const stored = getCookie("waha_session_id");
    console.log(stored);
    
    if (stored) setActiveSessionId(stored);
  }, []);  

  const filteredDevices = devices.filter((device) =>
    device.name.toLowerCase().includes(search.toLowerCase())
  );

  async function logoutDevice(id: string) {
    setDeletingId(id);
    try {
      await api.post(`/auth/logout?session=${id}`, {});
      window.location.reload(); // or refetch your data
    } catch (err) {
      console.error("Error logout:", err);
    } finally {
      setDeletingId(null);
    }
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform?.toLowerCase()) {
      case "whatsapp": return "🟢";
      case "whatsapp-web": return "💻";
      default: return "📱";
    }
  };
  
  return (
    <>
      <Input
        placeholder="Search devices..."
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        className="max-w-sm"
      />

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {loading ? (
          <p>Loading devices...</p>
        ) : filteredDevices.length === 0 ? (
          <p>No devices found.</p>
        ) : (
          filteredDevices.map((device, idx) => (
            <Card key={idx}>
              <CardHeader className="flex flex-row items-center py-3 justify-between">
                <CardTitle className="text-base flex items-center gap-2">
                  {getPlatformIcon(device.platform)}
                  <span>{device?.me?.pushName || device.name}</span>
                </CardTitle>
                <div className="flex flex-row gap-2 items-center">
                  <Switch
                    id={`switch-${device.id}`}
                    checked={device.id === activeSessionId}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        document.cookie = `waha_session_id=${device.id}; path=/; samesite=lax;`;
                        setActiveSessionId(device.id);
                      } else {
                        document.cookie = `waha_session_id=; path=/; max-age=0;`;
                        setActiveSessionId(null);
                      }
                    }}
                  />

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        size="icon"
                        variant="destructive"
                        title="Remove device"
                      >
                        <Trash2 />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Remove Device</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to remove device "{device?.me?.pushName || device.name}"?
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => logoutDevice(device.id)}
                          disabled={deletingId === device.id}
                          className="bg-red-600 hover:bg-red-700 text-white"
                        >
                          {deletingId === device.id ? "Removing..." : "Remove"}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardHeader>
              <CardContent className="flex flex-col justify-between text-sm text-muted-foreground space-y-2">
                <div className="flex items-center gap-1">
                  <span>Platform:</span>
                  <Badge variant="outline" className="capitalize text-xs">
                    {device.platform}
                  </Badge>
                </div>
                <div>
                  Status:{" "}
                  <Badge
                    variant="outline"
                    className={`text-xs ${device.status === "WORKING"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                      }`}
                  >
                    {device.status}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {!loading && (
        <p className="text-sm text-muted-foreground">
          Showing {filteredDevices.length} of {devices.length} devices
        </p>
      )}
    </>
  );
}
