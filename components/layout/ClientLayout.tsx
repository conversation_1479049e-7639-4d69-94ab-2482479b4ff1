'use client';

import { useState, Fragment } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  Menu,
  X,
  ChevronsLeft,
  ChevronsRight,
  Brain,
  Database,
  FileText,
  LayoutTemplate,
  MessageSquare,
  User,
  Smartphone,
} from 'lucide-react';
import { cn } from '@/lib/utils';

const navItems = [
  { name: 'Cha<PERSON>', href: '/chats', icon: MessageSquare, group: 'CRM' },
  { name: 'Contacts', href: '/contacts', icon: User, group: 'CRM' },
  { name: 'Message Templates', href: '/templates', icon: LayoutTemplate, group: 'CRM' },
  { name: 'AI Rules', href: '/ai/rules', icon: Brain, group: 'AI' },
  { name: 'AI Message Templates', href: '/ai/templates', icon: FileText, group: 'AI' },
  { name: 'AI Data Sources', href: '/datasources', icon: Database, group: 'System' },
  { name: 'Devices', href: '/devices', icon: Smartphone, group: 'System' },
];

function Sidebar({
  isOpen,
  onClose,
  isCompact,
  toggleCompact,
}: {
  isOpen: boolean;
  onClose: () => void;
  isCompact: boolean;
  toggleCompact: () => void;
}) {
  const pathname = usePathname();
  const groupedNav = navItems.reduce<Record<string, typeof navItems>>((acc, item) => {
    acc[item.group] = acc[item.group] || [];
    acc[item.group].push(item);
    return acc;
  }, {});

  return (
    <aside
      className={cn(
        'fixed md:static z-40 bg-gray-50 border-r transition-all duration-300 ease-in-out h-screen overflow-y-auto p-4',
        isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0',
        isCompact ? 'w-16' : 'w-64'
      )}
    >
      <div className='flex items-center justify-between mb-4'>
        <h1 className={cn('text-lg font-semibold transition-all', isCompact && 'hidden')}>
          CS CRM
        </h1>
        <button onClick={toggleCompact} className='text-gray-600 hover:text-black hidden md:block'>
          {isCompact ? <ChevronsRight className='h-5 w-5' /> : <ChevronsLeft className='h-5 w-5' />}
        </button>
        <button onClick={onClose} className='md:hidden text-gray-600 hover:text-black'>
          <X className='h-5 w-5' />
        </button>
      </div>
      <nav className='space-y-4'>
        {Object.entries(groupedNav).map(([group, items]) => (
          <Fragment key={group}>
            {!isCompact && (
              <div className='text-xs font-semibold text-gray-500 px-2 uppercase tracking-wide'>
                {group}
              </div>
            )}
            <div className='space-y-1'>
              {items.map((item) => {
                const isActive = pathname.startsWith(item.href);
                return (
                  <div key={item.href} title={isCompact ? item.name : undefined}>
                    <Link
                      href={item.href}
                      className={cn(
                        'flex items-center gap-2 rounded px-3 py-2 text-sm font-medium transition-all',
                        isActive
                          ? 'bg-gray-200 text-black'
                          : 'text-gray-600 hover:bg-gray-100 hover:text-black',
                        isCompact && 'justify-center px-2'
                      )}
                    >
                      <item.icon className='w-5 h-5' />
                      {!isCompact && <span>{item.name}</span>}
                    </Link>
                  </div>
                );
              })}
            </div>
          </Fragment>
        ))}
      </nav>
    </aside>
  );
}

export default function ClientLayout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isCompact, setIsCompact] = useState(false);

  return (
    <div className='flex flex-col md:flex-row min-h-screen'>
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        isCompact={isCompact}
        toggleCompact={() => setIsCompact((prev) => !prev)}
      />
      <main className='flex-1 p-4 md:ml-0 ml-0 md:pl-4'>
        <button
          onClick={() => setSidebarOpen(true)}
          className='md:hidden mb-4 inline-flex items-center gap-2 text-gray-600 hover:text-black'
        >
          <Menu className='h-5 w-5' />
          Open Menu
        </button>
        {children}
      </main>
    </div>
  );
}
