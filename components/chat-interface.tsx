'use client';

import { useRef, useEffect, useState } from 'react';
import { ChatHeader } from '@/components/chat-header';
import { ChatMessage } from '@/components/chat-message';
import { ChatInput } from '@/components/chat-input';
import { twMerge } from 'tailwind-merge';
import { Message, useReceiveMessages } from '@/hooks/useReceiveMessages';
import { realtime } from '@/lib/realtime';
import { Chat } from '@/hooks/useChats';
import { useSendMessage } from '@/hooks/useSendMessage';

interface ChatInterfaceProps {
  conversation: Chat;
  onToggleTemplates: () => void;
  showTemplates: boolean;
  isProfileCollapsed: boolean;
}

export function ChatInterface({
  conversation,
  onToggleTemplates,
  showTemplates,
  isProfileCollapsed,
}: ChatInterfaceProps) {
  const { sendMessage, loading: loadingSendMSG, error: errorSendMSG, response } = useSendMessage();
  const { messages, loading, error, isLastChats, page, setPage, refetch } = useReceiveMessages({
    chatId: conversation.id
  });
  const containerRef = useRef<HTMLDivElement | null>(null);
  const listRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [prevScrollHeight, setPrevScrollHeight] = useState(0);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView();
  };

  useEffect(() => {
    if (conversation.status_presence == 'typing') {
      messages?.map((message) => {
        if (message.id === conversation.id) message.ack = 3
        return message;
      })
    }
  }, [conversation.status_presence])

  useEffect(() => {
    const channel = realtime.client.subscribe("chat-channel");

    channel.bind("new-message", (data: any) => {
      console.log("New message:", data);
      if (conversation.id !== data.from) return;
      refetch();
    });

    return () => {
      channel.unbind_all();
      channel.unsubscribe();
    };
  }, [refetch]);

  useEffect(() => {
    setPrevScrollHeight(0);
    setPage(0);
  }, [conversation.id]);

  useEffect(() => {
    const listEl = listRef.current;
    if (!listEl || isLastChats || loading) return;

    const handleScroll = () => {
      if (listEl.scrollTop <= 20 && !loading && !isLastChats) {
        setPrevScrollHeight(listEl.scrollHeight); // simpan tinggi sebelum load
        setPage((prev) => prev + 1); // load pesan lama
      }
    };

    listEl.addEventListener('scroll', handleScroll);
    return () => listEl.removeEventListener('scroll', handleScroll);
  }, [loading, isLastChats, conversation.id]);

  useEffect(() => {
    const listEl = listRef.current;
    if (!listEl || prevScrollHeight === 0) return;

    const newScrollHeight = listEl.scrollHeight;
    const scrollDiff = newScrollHeight - prevScrollHeight;

    listEl.scrollTop = scrollDiff;

    setPrevScrollHeight(0); // reset
  }, [messages]);

  useEffect(() => {
    if (!messages) return;
    if (messages.length > 0 && page == 0) return scrollToBottom();
    return;
  }, [messages, page]);

  const handleSendMessage = async (message: string, phone?: string) => {
    console.log('Sending message:', message);
    // Here you would typically send the message to your backend
    await sendMessage({
      chatId: conversation.id,
      text: message,
      session: phone || '',
    })
    refetch();
    scrollToBottom();
  };

  return (
    <div
      ref={containerRef}
      className='flex flex-col h-full'
    >
      <ChatHeader
        customerName={conversation.name || 'Customer Support'}
        customerEmail={conversation.id}
        orderNumber='~.~'
        status={conversation.status_presence || 'offline'}
        tags={[]}
      />

      <div className='flex-1 overflow-y-auto p-4 space-y-4' ref={listRef}>
        {loading && <div className='text-xs text-muted-foreground'>Loading messages...</div>}
        {error && <div className='text-xs text-muted-foreground'>Error: {error}</div>}
        {(!loading && !error) && (messages || []).map((message, idx) => (
          <ChatMessage key={idx} message={message} />
        ))}
        {
          conversation.status_presence == 'typing' && (
            <div className='flex items-center gap-2 text-sm text-muted-foreground'>
              <span className='italic'>sedang mengetik...</span>
            </div>
          )
        }
        <div ref={messagesEndRef} />
      </div>

      <ChatInput
        onSendMessage={handleSendMessage}
        onToggleTemplates={onToggleTemplates}
        showTemplates={showTemplates}
        conversation={conversation}
      />
    </div>
  );
}
