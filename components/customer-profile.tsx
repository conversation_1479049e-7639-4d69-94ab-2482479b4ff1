'use client';

import { useState } from 'react';
import {
  Mail,
  Phone,
  MapPin,
  Calendar,
  ShoppingBag,
  StickyNote,
  Trash2,
  Pencil,
  Save,
  PanelRightClose,
  PanelLeftClose,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import customerProfileFromJson from '@/mocks/custom-profile.json';
import { Input } from '@/components/ui/input';
import { twMerge } from 'tailwind-merge';

const customerData = customerProfileFromJson;

interface Note {
  id: string;
  content: string;
  isEditing?: boolean;
}

interface CustomerProfileProps {
  conversationId: string;
  isCollapsed: boolean;
  onToggle: () => void;
}

export function CustomerProfile({
  conversationId,
  isCollapsed,
  onToggle,
}: CustomerProfileProps) {
  const [notes, setNotes] = useState<Note[]>([]);
  const [newNote, setNewNote] = useState('');

  const addNote = () => {
    if (!newNote.trim()) return;
    const note: Note = {
      id: Date.now().toString(),
      content: newNote,
    };
    setNotes([note, ...notes]);
    setNewNote('');
  };

  const deleteNote = (id: string) => {
    setNotes(notes.filter((n) => n.id !== id));
  };

  const startEditNote = (id: string) => {
    setNotes(
      notes.map((n) =>
        n.id === id ? { ...n, isEditing: true } : { ...n, isEditing: false }
      )
    );
  };

  const saveEditedNote = (id: string, updatedContent: string) => {
    setNotes(
      notes.map((n) =>
        n.id === id ? { ...n, content: updatedContent, isEditing: false } : n
      )
    );
  };

  return (
    <Card
      className={twMerge(
        'rounded-none border-0 border-b bg-background h-full overflow-y-auto transition-all duration-300 ease-in-out',
        isCollapsed ? 'w-[56px]' : 'w-80'
      )}
    >
      <CardHeader className='pb-3 sticky top-0 bg-background z-10'>
        <div className='flex items-center justify-between'>
          {!isCollapsed && (
            <CardTitle className='text-lg flex items-center gap-2'>
              <div className='w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center'>
                <span className='font-semibold text-primary'>SJ</span>
              </div>
              Customer Profile
            </CardTitle>
          )}
          <Button
            size='icon'
            variant='ghost'
            onClick={onToggle}
            title={isCollapsed ? 'Expand profile' : 'Collapse profile'}
            className={twMerge('hover:bg-muted/50', isCollapsed && 'mx-auto')}
          >
            {isCollapsed ? (
              <PanelLeftClose className='h-4 w-4' />
            ) : (
              <PanelRightClose className='h-4 w-4' />
            )}
          </Button>
        </div>
      </CardHeader>

      <CardContent className='space-y-4 pb-4'>
        {!isCollapsed ? (
          <>
            {/* Basic Info */}
            <div className='space-y-3'>
              <div>
                <h3 className='font-semibold text-base'>{customerData.name}</h3>
                <div className='flex items-center gap-2 mt-1'>
                  <Badge
                    variant='secondary'
                    className='bg-yellow-100 text-yellow-800'
                  >
                    {customerData.loyaltyTier}
                  </Badge>
                  <Badge variant='outline' className='text-xs'>
                    {customerData.totalOrders} orders
                  </Badge>
                </div>
              </div>

              <div className='space-y-2 text-sm'>
                <div className='flex items-center gap-2 text-muted-foreground'>
                  <Mail className='h-4 w-4' />
                  <span>{customerData.email}</span>
                </div>
                <div className='flex items-center gap-2 text-muted-foreground'>
                  <Phone className='h-4 w-4' />
                  <span>{customerData.phone}</span>
                </div>
                <div className='flex items-center gap-2 text-muted-foreground'>
                  <MapPin className='h-4 w-4' />
                  <span>{customerData.location}</span>
                </div>
                <div className='flex items-center gap-2 text-muted-foreground'>
                  <Calendar className='h-4 w-4' />
                  <span>Customer since {customerData.joinDate}</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Stats */}
            <div className='grid grid-cols-2 gap-4'>
              <div className='text-center p-3 bg-muted/50 rounded-lg'>
                <div className='text-lg font-semibold'>
                  {customerData.totalOrders}
                </div>
                <div className='text-xs text-muted-foreground'>
                  Total Orders
                </div>
              </div>
              <div className='text-center p-3 bg-muted/50 rounded-lg'>
                <div className='text-lg font-semibold'>
                  {customerData.totalSpent}
                </div>
                <div className='text-xs text-muted-foreground'>Total Spent</div>
              </div>
            </div>

            <Separator />

            {/* Recent Orders */}
            <div>
              <h4 className='font-medium mb-3 flex items-center gap-2'>
                <ShoppingBag className='h-4 w-4' />
                Recent Orders
              </h4>
              <div className='space-y-2'>
                {customerData.recentOrders.map((order) => (
                  <div
                    key={order.id}
                    className='flex items-center justify-between p-2 bg-muted/30 rounded text-sm'
                  >
                    <div>
                      <div className='font-medium'>{order.id}</div>
                      <div className='text-xs text-muted-foreground'>
                        {order.date}
                      </div>
                    </div>
                    <div className='text-right'>
                      <div className='font-medium'>{order.amount}</div>
                      <Badge
                        variant={
                          order.status === 'Delivered' ? 'default' : 'secondary'
                        }
                        className='text-xs'
                      >
                        {order.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Tags */}
            <div>
              <h4 className='font-medium mb-2'>Customer Tags</h4>
              <div className='flex flex-wrap gap-1'>
                {customerData.tags.map((tag) => (
                  <Badge key={tag} variant='outline' className='text-xs'>
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>

            <Separator />

            {/* Quick Notes */}
            <div>
              <h4 className='font-medium mb-2 flex items-center gap-2'>
                <StickyNote className='h-4 w-4' />
                Quick Notes
              </h4>

              <Textarea
                placeholder='Add a quick note about this customer...'
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
                rows={3}
              />
              <div className='flex justify-end mt-2'>
                <Button size='sm' onClick={addNote}>
                  Add Note
                </Button>
              </div>

              <div className='space-y-2 mt-4'>
                {notes.map((note) => (
                  <Card key={note.id} className='bg-muted/30'>
                    <CardContent className='p-3 space-y-2'>
                      {note.isEditing ? (
                        <>
                          <Textarea
                            value={note.content}
                            onChange={(e) =>
                              setNotes((prev) =>
                                prev.map((n) =>
                                  n.id === note.id
                                    ? { ...n, content: e.target.value }
                                    : n
                                )
                              )
                            }
                            rows={3}
                          />
                          <div className='flex justify-end gap-2'>
                            <Button
                              variant='secondary'
                              size='sm'
                              onClick={() =>
                                saveEditedNote(note.id, note.content.trim())
                              }
                            >
                              <Save className='h-4 w-4 mr-1' />
                              Save
                            </Button>
                          </div>
                        </>
                      ) : (
                        <div className='flex justify-between items-start'>
                          <div className='text-sm'>{note.content}</div>
                          <div className='flex gap-2'>
                            <Button
                              variant='ghost'
                              size='icon'
                              onClick={() => startEditNote(note.id)}
                            >
                              <Pencil className='w-4 h-4' />
                            </Button>
                            <Button
                              variant='ghost'
                              size='icon'
                              onClick={() => deleteNote(note.id)}
                            >
                              <Trash2 className='w-4 h-4 text-red-500' />
                            </Button>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <div className='text-xs text-muted-foreground pt-2 border-t'>
              Last activity: {customerData.lastActivity}
            </div>
          </>
        ) : (
          <div className='flex flex-col items-center gap-4 pt-4'>
            <div className='w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center'>
              <span className='font-semibold text-primary'>SJ</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
