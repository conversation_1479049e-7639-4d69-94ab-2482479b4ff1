import { User, Clock, CheckCheck, Info } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import clsx from 'clsx';
import { Chat } from '@/hooks/useChats';
import { Message } from '@/hooks/useReceiveMessages';

interface ChatListItemProps {
  conversation: Chat;
  isSelected: boolean;
  isCompact: boolean;
  onSelect: (props: Chat) => void;
}

export function ChatListItem({
  conversation,
  isSelected,
  isCompact,
  onSelect,
}: ChatListItemProps) {
  const tags = [
    'billing',
    'technical',
    'urgent',
    'feature-request',
    'onboarding',
    'resolved',
  ]
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'typing':
        return 'bg-green-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'resolved':
      default:
        return 'bg-gray-500';
    }
  };
  return !conversation.lastMessage ? null : (
    <button
      onClick={() => onSelect(conversation)}
      className={`w-full text-left rounded-md transition ${isSelected ? 'bg-muted' : 'hover:bg-accent'
        } ${isCompact ? 'p-3' : 'p-3'}`}
    >
      <div className='flex items-center gap-3 w-full'>
        <div className='relative'>
          <div className='w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center'>
            <User className='h-4 w-4' />
          </div>
          <div
            className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background ${getStatusColor(
              conversation.status_presence
            )}`}
          />
        </div>

        {!isCompact && (
          <div className='flex-1 min-w-0'>
            <div className='flex items-center justify-between mb-1'>
              <span className='font-medium text-sm truncate'>
                {conversation.name || 'You'}
              </span>
              {!conversation.lastMessage.fromMe ? null : (
                <span className='text-xs'>{!format(new Date(conversation.lastMessage?._data?.messageTimestamp), 'hh:mm')}</span>
              )}
              {(!conversation.lastMessage.fromMe && conversation.lastMessage.ack == 2) && (
                <Badge variant='destructive' className='text-xs px-1.5 py-1.5' />
              )}
            </div>
            {conversation.status_presence == 'typing' ? (
              <p className='text-xs text-muted-foreground mb-2 italic'>mengetik...</p>
            ) : (
              <p className='text-xs text-muted-foreground truncate mb-2'>
                {conversation.lastMessage?.body}
              </p>
            )}
            <div className='flex items-center justify-between'>
              <div className='flex gap-1'>
                {tags.slice(0, 2).map((tag) => (
                  <Badge
                    key={tag}
                    variant='outline'
                    className='text-xs px-1.5 py-0'
                  >
                    {tag}
                  </Badge>
                ))}
                {tags.length > 2 && (
                  <Badge variant='outline' className='text-xs px-1.5 py-0'>
                    +{tags.length - 2}
                  </Badge>
                )}
              </div>
              <div className='flex items-center gap-1 text-xs text-muted-foreground'>
                {conversation.lastMessage.ack >= 2 ?
                  <CheckCheck className={clsx('h-4 w-4', conversation.lastMessage.ack == 2 ? '' : 'text-sp-primary')} />
                  : conversation.lastMessage.ack == 0 ? <Clock className={clsx('h-4 w-4')} /> : conversation.lastMessage.ack == -1 ? <Info className={clsx('h-4 w-4 text-destructive')} /> : null}
                {conversation.timestamp}
              </div>
            </div>
          </div>
        )}
      </div>
    </button>
  );
}
