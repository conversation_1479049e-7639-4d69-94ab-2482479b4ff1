import { Badge } from '@/components/ui/badge';

interface ChatHeaderProps {
  customerName: string;
  customerEmail: string;
  orderNumber: string;
  status: string;
  tags: string[];
}

export function ChatHeader({
  customerName,
  customerEmail,
  orderNumber,
  status,
  tags,
}: ChatHeaderProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'typing':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'pending':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'resolved':
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };
  return (
    <div className='border-b p-4 bg-background'>
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-lg font-semibold'>{customerName}</h2>
          <p className='text-sm text-muted-foreground'>
            {customerEmail} • {orderNumber}
          </p>
        </div>
        <div className='flex gap-2'>
          <Badge
            variant='outline'
            className={`${getStatusColor(status)}`}
          >
            {status}
          </Badge>
          {tags.map((tag) => (
            <Badge key={tag} variant='outline'>
              {tag}
            </Badge>
          ))}
        </div>
      </div>
    </div>
  );
}
