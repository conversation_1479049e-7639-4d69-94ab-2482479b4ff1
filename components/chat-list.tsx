'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Search,
  Filter,
  MessageSquare,
  PanelRightClose,
  PanelLeftClose,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChatListItem } from '@/components/chat-list-item';
import { twMerge } from 'tailwind-merge';
import { Chat, useChats } from '@/hooks/useChats';
import { realtime } from '@/lib/realtime';
import { Message } from '@/hooks/useReceiveMessages';

const availableTags = [
  'billing',
  'technical',
  'urgent',
  'feature-request',
  'onboarding',
  'resolved',
];

interface ChatListProps {
  selectedConversation: Message;
  onSelectConversation: (props: Message) => void;
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
}

export function ChatList({
  selectedConversation,
  onSelectConversation,
  selectedTags,
  onTagsChange,
}: ChatListProps) {
  const { data, setData, loading, error, provider, session, refetch, page, setPage, isLastChats, } = useChats({});

  const [searchQuery, setSearchQuery] = useState('');
  const [autoCompact, setAutoCompact] = useState(false);
  const [manualCompact, setManualCompact] = useState<boolean | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const listRef = useRef<HTMLDivElement | null>(null);

  // Determine if compact mode is active
  const isCompact = manualCompact !== null ? manualCompact : autoCompact;

  // Realtime new-message
  useEffect(() => {
    if (!refetch) return;
    const channel = realtime.client.subscribe("chat-channel");

    channel.bind("new-message", (value: any) => {
      console.log("New messages:", value);
      console.log("data:", data);
      if (!data || !Array.isArray(data)) return;

      const newData = data.map((a: Chat) => {
        return a.id == value.from ? {
          ...a,
          lastMessage: {
            ...a.lastMessage,
            ...value,
            ack: a.id === selectedConversation?.id ? 3 : value.ack || 0,
          },
        } : a
      })
      setData(newData)
      onSelectConversation(newData.filter(a => a.id === selectedConversation?.id)[0] || newData[0]);
    });

    return () => {
      channel.unbind_all();
      channel.unsubscribe();
    };
  }, [refetch, selectedConversation?.id, data?.length, onSelectConversation]);


  // Realtime update-presence
  useEffect(() => {
    if (!refetch) return;

    const channel = realtime.client.subscribe("message-channel");

    channel.bind("update-presence", (value: any) => {
      console.log("update-presence:", value);
      if (!data || !Array.isArray(data)) return;

      const newData = data.map((a: Chat) => {
        return {
          ...a,
          status_presence: value.presences.find((p: any) => p.participant === a.id)?.lastKnownPresence || 'offline',
        }
      })
      setData(newData)
      onSelectConversation(newData.filter(a => a.id === selectedConversation?.id)[0] || newData[0]);
    });

    return () => {
      channel.unbind_all();
      channel.unsubscribe();
    };
  }, [refetch, selectedConversation?.id, data?.length, onSelectConversation]);

  // Auto compact mode based on container width
  useEffect(() => {
    const observer = new ResizeObserver(([entry]) => {
      if (manualCompact === null) {
        setAutoCompact(entry.contentRect.width < 160);
      }
    });

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [manualCompact]);

  // Handle compact mode auto toggle on resize
  useEffect(() => {
    const observer = new ResizeObserver(([entry]) => {
      if (manualCompact === null) {
        setAutoCompact(entry.contentRect.width < 160);
      }
    });

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [manualCompact]);

  useEffect(() => {
    const listEl = listRef.current;
    if (!listEl || isLastChats || loading) return;

    const handleScroll = () => {
      if (
        listEl.scrollTop + listEl.clientHeight >= listEl.scrollHeight - 50 &&
        !loading &&
        !isLastChats
      ) {
        setPage((prev) => prev + 1);
      }
    };

    listEl.addEventListener('scroll', handleScroll);

    return () => {
      listEl.removeEventListener('scroll', handleScroll);
    };
  }, [loading, isLastChats]);

  // Filter chats by search query and selected tags
  const filteredConversations = (data || []).filter((conv) => {
    const matchesSearch =
      conv?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conv?.lastMessage?.body?.toLowerCase().includes(searchQuery.toLowerCase());

    // Tags filtering is already applied in useChats filters, but double-check here:
    const matchesTags =
      selectedTags.length === 0 ||
      selectedTags.some((tag) => conv?.tags?.includes(tag));

    return matchesSearch && matchesTags;
  });

  return (
    <div
      ref={containerRef}
      className={twMerge(
        'h-full flex-shrink-0 border-r flex flex-col',
        isCompact ? 'w-[56px]' : 'w-80'
      )}
    >
      <Button
        size="icon"
        variant="ghost"
        onClick={() => setManualCompact((prev) => !prev)}
        title="Toggle compact mode"
      >
        {isCompact ? (
          <PanelRightClose className="h-4 w-4" />
        ) : (
          <PanelLeftClose className="h-4 w-4" />
        )}
      </Button>

      {!isCompact && (
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-6 w-6" />
              <h1 className="text-xl font-semibold">Customer Support</h1>
            </div>
          </div>

          <div className="space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Filter by tags
                  {selectedTags.length > 0 && (
                    <Badge variant="secondary" className="ml-auto">
                      {selectedTags.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                {availableTags.map((tag) => (
                  <DropdownMenuCheckboxItem
                    key={tag}
                    checked={selectedTags.includes(tag)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        onTagsChange([...selectedTags, tag]);
                      } else {
                        onTagsChange(selectedTags.filter((t) => t !== tag));
                      }
                    }}
                  >
                    {tag}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      )}

      <div className="flex-1 overflow-auto p-2" ref={listRef}>
        {!isCompact && (
          <div className="text-xs font-semibold text-muted-foreground mb-2">
            Conversations ({filteredConversations.length})
          </div>
        )}

        <div className="space-y-1">
          {(loading && page <= 0) && <div className='text-xs text-muted-foreground'>Loading chats...</div>}
          {error && <div className='text-xs text-muted-foreground'>Error: {error}</div>}
          {(!(loading && page <= 0) &&
            !error) && (
              <>
                {
                  filteredConversations.map((conversation) => (
                    <ChatListItem
                      key={conversation.id}
                      conversation={conversation}
                      isSelected={selectedConversation === conversation.id}
                      isCompact={isCompact}
                      onSelect={(val) => {
                        onSelectConversation(val)
                        if (!val?.id || !data || !Array.isArray(data)) return;
                        const newData = data.map((a: Chat) => {
                          return {
                            ...a,
                            lastMessage: {
                              ...a.lastMessage,
                              ack: a.id === val?.id ? 3 : a?.lastMessage?.ack || 0,
                            },
                          }
                        })
                        setData(newData)
                      }}
                    />
                  ))
                }
                {(loading && page > 0) && <p className='text-xs text-muted-foreground'>load more</p>}
              </>
            )
          }
        </div>
      </div>
    </div>
  );
}
