'use client';

import { useEffect, useState } from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import { ChatList } from '@/components/chat-list';
import { ChatInterface } from '@/components/chat-interface';
import { CustomerProfile } from '@/components/customer-profile';
import { AgentsSidebar } from '@/components/agents-sidebar';
import { TemplatesPanel } from '@/components/templates-panel';
import { twMerge } from 'tailwind-merge';
import { Message } from '@/hooks/useReceiveMessages';
import { Chat } from '@/hooks/useChats';
import { useSendPresence } from '@/hooks/useSendPresence';

export function CRMDashboard() {
  const { sendPresence } = useSendPresence();

  const [selectedConversation, setSelectedConversation] = useState<Chat>();
  const [showTemplates, setShowTemplates] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isProfileCollapsed, setIsProfileCollapsed] = useState(false);

  useEffect(() => {
    // Kirim presence ONLINE saat user membuka halaman
    sendPresence({ presence: "online" });

    const handleBeforeUnload = () => {
      // Kirim presence OFFLINE saat user menutup tab atau refresh
      navigator.sendBeacon("/api/chats/send-presence", JSON.stringify({
        presence: "offline",
      }));
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  return (
    <div className='flex h-screen bg-background overflow-hidden'>
      <div className='flex-shrink-0'>
        <ChatList
          selectedConversation={selectedConversation}
          onSelectConversation={setSelectedConversation}
          selectedTags={selectedTags}
          onTagsChange={setSelectedTags}
        />
      </div>

      <div className='flex-1 min-w-0 flex flex-col border-l border-r'>
        {selectedConversation && (
          <ChatInterface
            conversation={selectedConversation}
            onToggleTemplates={() => setShowTemplates(!showTemplates)}
            showTemplates={showTemplates}
            isProfileCollapsed={isProfileCollapsed}
          />
        )}
      </div>

      <div
        className={twMerge(
          'flex-shrink-0 bg-muted/30 flex flex-col overflow-hidden transition-all duration-300 ease-in-out',
          isProfileCollapsed ? 'w-[56px]' : 'w-80'
        )}
      >
        <CustomerProfile
          conversationId={selectedConversation}
          isCollapsed={isProfileCollapsed}
          onToggle={() => setIsProfileCollapsed((prev) => !prev)}
        />
        <AgentsSidebar conversationId={selectedConversation} />
      </div>

      {showTemplates && (
        <div className='absolute right-80 top-0 bottom-0 z-10 shadow-lg'>
          <TemplatesPanel onClose={() => setShowTemplates(false)} />
        </div>
      )}
    </div>
  );
}
