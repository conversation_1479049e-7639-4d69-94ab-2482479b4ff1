'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogFooter,
  DialogHeader,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogTrigger,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { Trash, Plus, Trash2, Pencil } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Label } from 'recharts';

type Rule = {
  id: string;
  name: string;
  description?: string;
  tags?: string[];
};

export default function CSRulesTablePage() {
  const { toast } = useToast();
  const [search, setSearch] = useState('');
  const [rules, setRules] = useState<Rule[]>([]);
  const [open, setOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [form, setForm] = useState({
    name: '',
    description: '',
    tags: [] as string[],
  });
  const [editingRule, setEditingRule] = useState<Rule | null>(null);

  // Load rules from API
  useEffect(() => {
    const fetchRules = async () => {
      try {
        setIsLoading(true);
        const res = await fetch('/api/v1/ai-rules');
        const data = await res.json();
        setRules(data);
      } catch (err) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Gagal memuat daftar rules',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchRules();
  }, [toast]);

  const handleEdit = (rule: Rule) => {
    setEditingRule(rule);
    setForm({
      name: rule.name,
      description: rule.description || '',
      tags: rule.tags || [],
    });
    setOpen(true);
  };

  const handleSave = async () => {
    if (!form.name) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Nama rule harus diisi',
      });
      return;
    }

    try {
      if (editingRule) {
        const res = await fetch(`/api/ai-rules/${editingRule.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(form),
        });
        if (res.ok) {
          const updated = await res.json();
          setRules((prev) =>
            prev.map((r) => (r.id === editingRule.id ? updated : r))
          );
          toast({
            title: 'Berhasil',
            description: 'Rule berhasil diperbarui',
          });
        }
      } else {
        const res = await fetch('/api/v1/ai-rules', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(form),
        });
        if (res.ok) {
          const created = await res.json();
          setRules((prev) => [...prev, created]);
          toast({
            title: 'Berhasil',
            description: 'Rule baru berhasil dibuat',
          });
        }
      }
      setForm({ name: '', description: '', tags: [] });
      setEditingRule(null);
      setOpen(false);
    } catch (err) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Gagal menyimpan rule',
      });
    }
  };

  const handleDelete = async (id: string) => {
    setIsDeleting(true);
    try {
      const res = await fetch(`/api/ai-rules/${id}`, { method: 'DELETE' });
      if (res.ok) {
        setRules((prev) => prev.filter((r) => r.id !== id));
        toast({
          title: 'Berhasil',
          description: 'Rule berhasil dihapus',
        });
      }
    } catch (err) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Gagal menghapus rule',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const filteredRules = rules.filter((rule) => {
    const q = search.toLowerCase();
    return (
      rule.name.toLowerCase().includes(q) ||
      (rule.description?.toLowerCase().includes(q) ?? false) ||
      (rule.tags?.some((tag) => tag.toLowerCase().includes(q)) ?? false)
    );
  });

  return (
    <div className='space-y-4'>
      <div className='flex justify-between items-center'>
        <h2 className='text-xl font-semibold'>AI Rules</h2>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => {
                setEditingRule(null);
                setForm({ name: '', description: '', tags: [] });
              }}
            >
              <Plus className='h-4 w-4' />
              Add Rule
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingRule ? 'Edit AI Rule' : 'Add New Rule'}
              </DialogTitle>
            </DialogHeader>
            <div className='space-y-4'>
              <div className='space-y-2'>
                <Label>Name</Label>
                <Input
                  value={form.name}
                  onChange={(e) => setForm({ ...form, name: e.target.value })}
                  placeholder='Enter rule name'
                />
              </div>
              <div className='space-y-2'>
                <Label>Description</Label>
                <Input
                  value={form.description}
                  onChange={(e) =>
                    setForm({ ...form, description: e.target.value })
                  }
                  placeholder='Enter rule description'
                />
              </div>
              <div className='space-y-2'>
                <Label>Tags</Label>
                <Input
                  value={form.tags.join(', ')}
                  onChange={(e) =>
                    setForm({
                      ...form,
                      tags: e.target.value
                        .split(',')
                        .map((t) => t.trim())
                        .filter(Boolean),
                    })
                  }
                  placeholder='Enter tags (comma separated)'
                />
              </div>
              <Button onClick={handleSave} className='w-full'>
                {editingRule ? 'Update Rule' : 'Save Rule'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Input
        placeholder='Search AI Rules...'
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />

      <div className='border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='w-[60px]'>ID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Tags</TableHead>
              <TableHead className='w-[100px] text-right'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRules.map((rule) => (
              <TableRow key={rule.id}>
                <TableCell className='font-mono text-xs'>{rule.id}</TableCell>
                <TableCell>{rule.name}</TableCell>
                <TableCell className='text-sm text-muted-foreground'>
                  {rule.description}
                </TableCell>
                <TableCell>
                  {rule.tags?.length ? (
                    <div className='flex flex-wrap gap-1'>
                      {rule.tags.map((tag, i) => (
                        <span
                          key={i}
                          className='text-xs bg-muted px-2 py-0.5 rounded'
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <span className='text-muted-foreground text-sm'>—</span>
                  )}
                </TableCell>
                <TableCell className='text-right'>
                  <div className='flex justify-end gap-2'>
                    <Button
                      variant='outline'
                      size='icon'
                      onClick={() => handleEdit(rule)}
                    >
                      <Pencil className='h-4 w-4' />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant='destructive' size='icon'>
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Rule</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete rule "{rule.name}"?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(rule.id)}
                            className='bg-red-600 hover:bg-red-700'
                            disabled={isDeleting}
                          >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {filteredRules.length === 0 && !isLoading && (
              <TableRow>
                <TableCell
                  colSpan={5}
                  className='text-center text-muted-foreground py-6'
                >
                  No rules found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <p className='text-sm text-muted-foreground'>
        Showing {filteredRules.length} of {rules.length} AI Rules
      </p>
    </div>
  );
}
