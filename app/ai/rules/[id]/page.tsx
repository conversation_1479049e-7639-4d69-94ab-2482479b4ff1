'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogTitle,
  AlertDialogFooter,
} from '@/components/ui/alert-dialog'
import { Label } from '@/components/ui/label'
import { Trash } from 'lucide-react'
import { Switch } from '@/components/ui/switch'
import { format } from 'date-fns'

type Rule = {
  id: string
  name: string
  description?: string
  conditions?: string[]
  actions?: string[]
  tags?: string[]
  isActive: boolean
  createdAt?: string
  updatedAt?: string
}

export default function CSRuleEditorPage() {
  const { id } = useParams()
  const router = useRouter()
  const [rule, setRule] = useState<Rule | null>(null)
  const [notFound, setNotFound] = useState(false)

  useEffect(() => {
    if (!id) return
    const fetchRule = async () => {
      try {
        const res = await fetch(`/api/ai-rules/${id}`)
        if (!res.ok) throw new Error('Not found')
        const data = await res.json()
        setRule(data)
      } catch (err) {
        setNotFound(true)
      }
    }
    fetchRule()
  }, [id])

  const handleUpdate = (field: keyof Rule, value: any) => {
    setRule((prev) => (prev ? { ...prev, [field]: value } : prev))
  }

  const handleSave = async () => {
    if (!rule) return
    try {
      const res = await fetch(`/api/ai-rules/${rule.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(rule),
      })
      if (res.ok) {
        const updated = await res.json()
        setRule(updated)
      }
    } catch (err) {
      console.error('Failed to save rule', err)
    }
  }

  const handleDelete = async () => {
    if (!rule) return
    try {
      const res = await fetch(`/api/ai-rules/${rule.id}`, {
        method: 'DELETE',
      })
      if (res.ok) {
        router.push('/cs-rules')
      }
    } catch (err) {
      console.error('Failed to delete rule', err)
    }
  }

  if (notFound) {
    return (
      <div className="p-10 text-center text-red-500">
        <h1 className="text-xl font-semibold">Rule not found</h1>
        <Button onClick={() => router.push('/cs-rules')} className="mt-4">
          Back to List
        </Button>
      </div>
    )
  }

  if (!rule) return <div className="p-10">Loading...</div>

  return (
    <div className="max-w-3xl mx-auto py-10 px-4 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Edit Rule: {rule.name}</h2>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button size="icon" variant="ghost">
              <Trash className="w-5 h-5 text-destructive" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogTitle>Are you sure you want to delete this rule?</AlertDialogTitle>
            <AlertDialogFooter>
              <Button variant="outline">Cancel</Button>
              <Button variant="destructive" onClick={handleDelete}>
                Delete
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <div className="space-y-4">
        <div>
          <Label>Rule Name</Label>
          <Input value={rule.name} onChange={(e) => handleUpdate('name', e.target.value)} />
        </div>

        <div>
          <Label>Description</Label>
          <Textarea
            value={rule.description || ''}
            onChange={(e) => handleUpdate('description', e.target.value)}
          />
        </div>

        <div>
          <Label>Conditions (one per line)</Label>
          <Textarea
            value={(rule.conditions || []).join('\n')}
            onChange={(e) =>
              handleUpdate(
                'conditions',
                e.target.value.split('\n').map((s) => s.trim()).filter(Boolean)
              )
            }
          />
        </div>

        <div>
          <Label>Actions (one per line)</Label>
          <Textarea
            value={(rule.actions || []).join('\n')}
            onChange={(e) =>
              handleUpdate(
                'actions',
                e.target.value.split('\n').map((s) => s.trim()).filter(Boolean)
              )
            }
          />
        </div>

        <div>
          <Label>Tags (comma separated)</Label>
          <Input
            value={(rule.tags || []).join(', ')}
            onChange={(e) =>
              handleUpdate(
                'tags',
                e.target.value.split(',').map((tag) => tag.trim())
              )
            }
          />
        </div>

        <div className="flex items-center gap-4">
          <Label htmlFor="isActive">Is Active</Label>
          <Switch
            id="isActive"
            checked={rule.isActive}
            onCheckedChange={(v) => handleUpdate('isActive', v)}
          />
        </div>

        <div className="text-sm text-muted-foreground">
          <div>
            Created At:{' '}
            {rule.createdAt ? format(new Date(rule.createdAt), 'PPpp') : '—'}
          </div>
          <div>
            Updated At:{' '}
            {rule.updatedAt ? format(new Date(rule.updatedAt), 'PPpp') : '—'}
          </div>
        </div>

        <div className="pt-4">
          <Button onClick={handleSave}>Save Changes</Button>
        </div>
      </div>
    </div>
  )
}
