'use client';

import { useEffect, useState } from 'react';
import { Plus, Trash2, Pencil } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogTrigger,
  AlertDialogTitle,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { rule } from 'postcss';
import { toast, useToast } from '@/components/ui/use-toast';

interface Template {
  id: string;
  title: string;
  tags: string[];
  content: string;
}

export default function AITemplatesPage() {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [search, setSearch] = useState('');
  const [open, setOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [form, setForm] = useState({
    title: '',
    tags: '',
    content: '',
  });
  const [editing, setEditing] = useState<Template | null>(null);

  const fetchTemplates = async () => {
    const res = await fetch('/api/v1/ai-templates');
    const data = await res.json();
    setTemplates(data);
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  const handleSave = async () => {
    const payload = {
      title: form.title,
      content: form.content,
      tags: form.tags
        .split(',')
        .map((t) => t.trim())
        .filter(Boolean),
    };

    if (editing) {
      await fetch(`/api/ai-templates/${editing.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
    } else {
      await fetch('/api/v1/ai-templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
    }

    setForm({ title: '', tags: '', content: '' });
    setEditing(null);
    setOpen(false);
    fetchTemplates();
  };

  const handleEdit = (template: Template) => {
    setEditing(template);
    setForm({
      title: template.title,
      tags: template.tags.join(', '),
      content: template.content,
    });
    setOpen(true);
  };

  const handleDelete = async (id: string) => {
    setIsDeleting(true);
    try {
      const res = await fetch(`/api/ai-templates/${id}`, { method: 'DELETE' });
      if (res.ok) {
        setTemplates((prev: Template[]) =>
          prev.filter((t: Template) => t.id !== id)
        );
        toast({
          title: 'Success',
          description: 'Template deleted successfully',
        });
      }
    } catch (err) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to delete template',
      });
    } finally {
      setIsDeleting(false);
    }
    fetchTemplates();
  };

  const filteredTemplates = templates.filter(
    (t) =>
      t.title.toLowerCase().includes(search.toLowerCase()) ||
      t.tags.join(', ').toLowerCase().includes(search.toLowerCase()) ||
      t.content.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className='space-y-4'>
      <div className='flex justify-between items-center'>
        <h2 className='text-xl font-semibold'>AI Message Templates</h2>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => {
                setEditing(null);
                setForm({ title: '', tags: '', content: '' });
              }}
            >
              <Plus className='h-4 w-4' />
              Add Template
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editing ? 'Edit Template' : 'Add New Template'}
              </DialogTitle>
            </DialogHeader>
            <div className='space-y-4'>
              <div className='space-y-2'>
                <Label>Title</Label>
                <Input
                  value={form.title}
                  onChange={(e) => setForm({ ...form, title: e.target.value })}
                  placeholder='Enter template title'
                />
              </div>
              <div className='space-y-2'>
                <Label>Tags</Label>
                <Input
                  value={form.tags}
                  onChange={(e) => setForm({ ...form, tags: e.target.value })}
                  placeholder='Enter tags (comma separated)'
                />
              </div>
              <div className='space-y-2'>
                <Label>Content</Label>
                <Textarea
                  value={form.content}
                  onChange={(e) =>
                    setForm({ ...form, content: e.target.value })
                  }
                  placeholder='Enter template content'
                  rows={5}
                />
              </div>
              <Button onClick={handleSave} className='w-full'>
                {editing ? 'Update Template' : 'Save Template'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Input
        placeholder='Search AI Message Templates...'
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />

      <div className='border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Tags</TableHead>
              <TableHead>Content</TableHead>
              <TableHead className='w-[100px] text-right'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTemplates.map((template) => (
              <TableRow key={template.id}>
                <TableCell>{template.title}</TableCell>
                <TableCell>
                  <div className='flex flex-wrap gap-1'>
                    {template.tags.map((tag, i) => (
                      <span
                        key={i}
                        className='text-xs bg-muted px-2 py-0.5 rounded'
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </TableCell>
                <TableCell className='whitespace-pre-wrap text-sm text-muted-foreground'>
                  {template.content.length > 100
                    ? template.content.slice(0, 100) + '...'
                    : template.content}
                </TableCell>
                <TableCell className='text-right'>
                  <div className='flex justify-end gap-2'>
                    <Button
                      variant='outline'
                      size='icon'
                      onClick={() => handleEdit(template)}
                    >
                      <Pencil className='h-4 w-4' />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant='destructive' size='icon'>
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Template</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete template "
                            {template.title}"?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(template.id)}
                            className='bg-red-600 hover:bg-red-700'
                            disabled={isDeleting}
                          >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <p className='text-sm text-muted-foreground'>
        Showing {filteredTemplates.length} of {templates.length} AI Message
        Templates
      </p>
    </div>
  );
}
