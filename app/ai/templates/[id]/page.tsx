'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'

// Sample in-memory template store (replace with real data or fetch logic)
const templateStore = [
  {
    id: 'welcome',
    name: 'Welcome Message',
    description: 'Sent when a user first signs up',
    content: 'Hi {{name}}, welcome to our platform! Let us know if you need help.',
  },
  {
    id: 'reminder',
    name: 'Reminder Message',
    description: 'Used for reminding users of upcoming deadlines',
    content: 'Hey {{name}}, just a reminder about {{event}} on {{date}}.',
  },
]

export default function TemplateEditorPage() {
  const { id } = useParams()
  const router = useRouter()
  const template = templateStore.find((t) => t.id === id)

  const [name, setName] = useState(template?.name ?? '')
  const [description, setDescription] = useState(template?.description ?? '')
  const [content, setContent] = useState(template?.content ?? '')

  const handleSave = () => {
    console.log('Saving...', { id, name, description, content })
    // Save logic here (to backend or local store)
    alert('Template saved!')
    router.push('/templates')
  }

  if (!template) {
    return (
      <div className="p-8">
        <p className="text-red-500">Template not found</p>
        <Button onClick={() => router.back()} className="mt-4">Go Back</Button>
      </div>
    )
  }

  return (
    <div className="max-w-3xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>Edit Template: {template.name}</CardTitle>
        </CardHeader>
        <Separator />
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Name</Label>
            <Input value={name} onChange={(e) => setName(e.target.value)} />
          </div>

          <div className="space-y-2">
            <Label>Description</Label>
            <Input value={description} onChange={(e) => setDescription(e.target.value)} />
          </div>

          <div className="space-y-2">
            <Label>Message Body</Label>
            <Textarea
              rows={6}
              placeholder="Hi {{name}}, welcome to our app!"
              value={content}
              onChange={(e) => setContent(e.target.value)}
            />
            <p className="text-sm text-muted-foreground">
              You can use placeholders like ..., etc.
            </p>
          </div>

          <Button onClick={handleSave} className="w-full">Save Template</Button>
        </CardContent>
      </Card>
    </div>
  )
}
