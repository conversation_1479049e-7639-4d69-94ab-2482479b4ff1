'use client'

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface Template {
  id: string
  name: string
  description: string
  content: string
}

const dummyTemplates: Template[] = [
  {
    id: 'welcome',
    name: 'Welcome Message',
    description: 'Sent when user signs up.',
    content: 'Hi {{name}}, welcome to our platform!',
  },
  {
    id: 'reminder',
    name: 'Reminder Message',
    description: 'For events and appointments.',
    content: 'Hello {{name}}, your event {{event}} is on {{date}}.',
  },
]

export default function TemplateEditorPage() {
  const { id } = useParams()
  const router = useRouter()

  const [form, setForm] = useState<Template>({
    id: '',
    name: '',
    description: '',
    content: '',
  })

  useEffect(() => {
    const template = dummyTemplates.find((t) => t.id === id)
    if (template) {
      setForm(template)
    } else {
      // New template
      setForm({ id: id as string, name: '', description: '', content: '' })
    }
  }, [id])

  const handleSave = () => {
    // Replace this with API logic
    alert(`Saved:\n${JSON.stringify(form, null, 2)}`)
    router.push('/templates')
  }

  const renderPreview = () => {
    return form.content
      .replace(/{{name}}/g, 'Alex')
      .replace(/{{event}}/g, 'Demo Day')
      .replace(/{{date}}/g, 'June 30')
      .replace(/{{order_id}}/g, '#12345')
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">{form.name ? 'Edit' : 'Create'} Template</h1>

      <div className="space-y-4">
        <div>
          <Label>Name</Label>
          <Input
            value={form.name}
            onChange={(e) => setForm({ ...form, name: e.target.value })}
          />
        </div>

        <div>
          <Label>Description</Label>
          <Input
            value={form.description}
            onChange={(e) => setForm({ ...form, description: e.target.value })}
          />
        </div>

        <div>
          <Label>Message Content</Label>
          <Textarea
            rows={6}
            placeholder="Hi {{name}}, your order {{order_id}} has shipped!"
            value={form.content}
            onChange={(e) => setForm({ ...form, content: e.target.value })}
          />
          <p className="text-sm text-muted-foreground mt-1">
            You can use placeholders like ...
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Live Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{renderPreview()}</p>
          </CardContent>
        </Card>

        <Button onClick={handleSave}>Save Template</Button>
      </div>
    </div>
  )
}
