'use client';

import { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Pencil, Plus, Trash2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';

interface Template {
  id: string;
  title: string;
  description?: string;
  content: string;
}

export default function TemplatesPage() {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [search, setSearch] = useState('');
  const [open, setOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [form, setForm] = useState({
    title: '',
    description: '',
    content: '',
  });
  const [editing, setEditing] = useState<Template | null>(null);

  // Load templates from API
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        const res = await fetch('/api/v1/message-templates');
        if (res.ok) {
          const data = await res.json();
          setTemplates(data);
        } else {
          throw new Error('Failed to load templates');
        }
      } catch (err) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load templates',
        });
      } finally {
        setIsLoading(false);
      }
    };
    loadTemplates();
  }, [toast]);

  const validateForm = () => {
    if (!form.title.trim()) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Title is required',
      });
      return false;
    }
    if (!form.content.trim()) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Content is required',
      });
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      if (editing) {
        const res = await fetch(`/api/message-templates/${editing.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(form),
        });
        if (res.ok) {
          const updated = await res.json();
          setTemplates((prev) =>
            prev.map((t) => (t.id === editing.id ? updated : t))
          );
          toast({
            title: 'Success',
            description: 'Template updated successfully',
          });
        } else {
          throw new Error('Failed to update template');
        }
      } else {
        const res = await fetch('/api/v1/message-templates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(form),
        });
        if (res.ok) {
          const created = await res.json();
          setTemplates((prev) => [created, ...prev]);
          toast({
            title: 'Success',
            description: 'Template created successfully',
          });
        } else {
          throw new Error('Failed to create template');
        }
      }

      setForm({ title: '', description: '', content: '' });
      setEditing(null);
      setOpen(false);
    } catch (err) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err instanceof Error ? err.message : 'An error occurred',
      });
    }
  };

  const handleEdit = (template: Template) => {
    setForm({
      title: template.title,
      description: template.description || '',
      content: template.content,
    });
    setEditing(template);
    setOpen(true);
  };

  const handleDelete = async (id: string) => {
    setIsDeleting(true);
    try {
      const res = await fetch(`/api/message-templates/${id}`, {
        method: 'DELETE',
      });
      if (res.ok) {
        setTemplates((prev) => prev.filter((t) => t.id !== id));
        toast({
          title: 'Success',
          description: 'Template deleted successfully',
        });
      } else {
        throw new Error('Failed to delete template');
      }
    } catch (err) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          err instanceof Error ? err.message : 'Failed to delete template',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const filteredTemplates = templates.filter(
    (t) =>
      t.title.toLowerCase().includes(search.toLowerCase()) ||
      t.description?.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className='space-y-4'>
      <div className='flex justify-between items-center'>
        <h2 className='text-xl font-semibold'>Message Templates</h2>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => {
                setEditing(null);
                setForm({ title: '', description: '', content: '' });
              }}
            >
              <Plus className='h-4 w-4' />
              Add Template
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editing ? 'Edit Template' : 'Add New Template'}
              </DialogTitle>
            </DialogHeader>
            <div className='space-y-4'>
              <div className='space-y-2'>
                <Label>Title</Label>
                <Input
                  value={form.title}
                  onChange={(e) => setForm({ ...form, title: e.target.value })}
                  placeholder='Enter template title'
                />
              </div>
              <div className='space-y-2'>
                <Label>Description</Label>
                <Input
                  value={form.description}
                  onChange={(e) =>
                    setForm({ ...form, description: e.target.value })
                  }
                  placeholder='Enter template description (optional)'
                />
              </div>
              <div className='space-y-2'>
                <Label>Content</Label>
                <Textarea
                  value={form.content}
                  onChange={(e) =>
                    setForm({ ...form, content: e.target.value })
                  }
                  placeholder='Enter template content'
                  rows={5}
                />
              </div>
              <Button onClick={handleSave} className='w-full'>
                {editing ? 'Update Template' : 'Save Template'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Input
        placeholder='Search Message Templates...'
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />

      <div className='border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Content</TableHead>
              <TableHead className='w-[100px] text-right'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTemplates.map((template) => (
              <TableRow key={template.id}>
                <TableCell>{template.title}</TableCell>
                <TableCell>{template.description}</TableCell>
                <TableCell className='whitespace-pre-wrap text-sm text-muted-foreground'>
                  {template.content.length > 100
                    ? template.content.slice(0, 100) + '...'
                    : template.content}
                </TableCell>
                <TableCell className='text-right'>
                  <div className='flex justify-end gap-2'>
                    <Button
                      variant='outline'
                      size='icon'
                      onClick={() => handleEdit(template)}
                    >
                      <Pencil className='h-4 w-4' />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant='destructive' size='icon'>
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Template</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete template "
                            {template.title}"?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(template.id)}
                            className='bg-red-600 hover:bg-red-700'
                            disabled={isDeleting}
                          >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
            {filteredTemplates.length === 0 && !isLoading && (
              <TableRow>
                <TableCell
                  colSpan={4}
                  className='text-center text-muted-foreground py-6'
                >
                  No templates found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <p className='text-sm text-muted-foreground'>
        Showing {filteredTemplates.length} of {templates.length} Message
        Templates
      </p>
    </div>
  );
}
