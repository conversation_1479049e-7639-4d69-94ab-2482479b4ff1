'use client';

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Trash2, PlusCircle, Plus, Pencil } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

type DataSource = {
  id: string;
  name: string;
  type: string;
  url: string;
  accessKey?: string;
};

const defaultTypes = ['GoogleSheet', 'REST API', 'CSV', 'JSON', 'Firebase'];

export default function DataSourcePage() {
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [open, setOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [search, setSearch] = useState('');
  const [form, setForm] = useState<Partial<DataSource>>({});
  const [editing, setEditing] = useState<DataSource | null>(null);

  const handleSave = () => {
    if (!form.name || !form.type || !form.url) return;
    if (form.id) {
      setDataSources((prev) =>
        prev.map((d) => (d.id === form.id ? (form as DataSource) : d))
      );
    } else {
      setDataSources((prev) => [
        ...prev,
        { ...(form as DataSource), id: uuidv4() },
      ]);
    }
    setForm({});
    setOpen(false);
  };

  const handleEdit = (ds: DataSource) => {
    setEditing(ds);
    setForm(ds);
    setOpen(true);
  };

  const handleDelete = (id: string) => {
    setIsDeleting(true);
    setDataSources((prev) => prev.filter((ds) => ds.id !== id));
    setIsDeleting(false);
  };

  const filteredDataSources = dataSources.filter(
    (ds) =>
      ds.name.toLowerCase().includes(search.toLowerCase()) ||
      ds.type.toLowerCase().includes(search.toLowerCase()) ||
      ds.url.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className='space-y-4'>
      <div className='flex justify-between items-center'>
        <h2 className='text-xl font-semibold'>AI Data Sources</h2>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => {
                setEditing(null);
                setForm({});
              }}
            >
              <Plus className='h-4 w-4' />
              Add Source
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editing ? 'Edit Data Source' : 'Add New Data Source'}
              </DialogTitle>
            </DialogHeader>
            <div className='space-y-4'>
              <div className='space-y-2'>
                <Label>Name</Label>
                <Input
                  value={form.name || ''}
                  onChange={(e) => setForm({ ...form, name: e.target.value })}
                  placeholder='Enter data source name'
                />
              </div>

              <div className='space-y-2'>
                <Label>Type</Label>
                <Select
                  value={form.type}
                  onValueChange={(val) => setForm({ ...form, type: val })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select type' />
                  </SelectTrigger>
                  <SelectContent>
                    {defaultTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label>URL / Identifier</Label>
                <Input
                  value={form.url || ''}
                  onChange={(e) => setForm({ ...form, url: e.target.value })}
                  placeholder='https://sheet.google.com/...'
                />
              </div>

              <div className='space-y-2'>
                <Label>Access Key (Optional)</Label>
                <Input
                  type='password'
                  value={form.accessKey || ''}
                  onChange={(e) =>
                    setForm({ ...form, accessKey: e.target.value })
                  }
                  placeholder='Enter access key'
                />
              </div>

              <Button onClick={handleSave} className='w-full'>
                {editing ? 'Update Source' : 'Save Source'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Input
        placeholder='Search AI Data Sources...'
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />

      <div className='border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>URL</TableHead>
              <TableHead className='w-[100px] text-right'>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredDataSources.map((ds) => (
              <TableRow key={ds.id}>
                <TableCell>{ds.name}</TableCell>
                <TableCell>{ds.type}</TableCell>
                <TableCell className='truncate max-w-xs'>{ds.url}</TableCell>
                <TableCell className='text-right'>
                  <div className='flex justify-end gap-2'>
                    <Button
                      variant='outline'
                      size='icon'
                      onClick={() => handleEdit(ds)}
                    >
                      <Pencil className='h-4 w-4' />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant='destructive' size='icon'>
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Delete Data Source
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete data source "
                            {ds.name}"?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(ds.id)}
                            className='bg-red-600 hover:bg-red-700'
                            disabled={isDeleting}
                          >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <p className='text-sm text-muted-foreground'>
        Showing {filteredDataSources.length} of {dataSources.length} AI Data
        Sources
      </p>
    </div>
  );
}
