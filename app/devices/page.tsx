'use client'
import SessionsList from "@/components/layout/devices/SessionsList";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import AddDevices from "@/components/layout/devices/add-devices";
import { useEffect, useState } from "react";
import { getCookie } from "@/lib/utils";
import { useDevices } from "@/hooks/useDevices";

export default function LinkedDevicesPage() {
  const [cookieStore, setCookieStore] = useState<string>("waha");
  const { devices, loading, fetchDevices } = useDevices();

  useEffect(() => {
    const stored = getCookie("preferred_provider");
    if (!stored || !cookieStore) return
    setCookieStore(stored);
  }, [cookieStore.length]);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Linked WhatsApp Devices</h2>

        {/* Form to set provider */}
        <div className="flex gap-2 items-center">
          <Select defaultValue={cookieStore} value={cookieStore} onValueChange={val => {
            document.cookie = `preferred_provider=${val}; path=/; samesite=lax;`;
            setCookieStore(val)
            window.location.reload(); // Refresh to apply new provider
          }} name="provider">
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Select provider" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="waha">WAHA</SelectItem>
              <SelectItem value="gowa">Gowa</SelectItem>
            </SelectContent>
          </Select>
          <AddDevices devices={devices} loading={loading} fetchDevices={fetchDevices}/>
        </div>
      </div>

      <SessionsList
        devices={
          cookieStore == 'gowa'
            ? devices?.map(a => {
              return {
                ...a,
                id: a.id || a.name,
                name: a.me?.pushName || a.name,
                platform: a.platform || 'whatsapp-web',
                status: a.status || 'WORKING',
              }
            })
            : devices?.filter(a => a?.status == 'WORKING').map(a => {
              return {
                ...a,
                id: a.id || a.name,
                name: a.me?.pushName || a.name,
                platform: a.platform || 'phone',
              }
            })
        }
        loading={loading}
      />
    </div>
  );
}
