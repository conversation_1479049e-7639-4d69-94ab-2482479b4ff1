'use client'

import { TableRowData } from "@/components/crud-page";
import DataPageEnhanced from '@/components/crud-page/DataPageEnhanced'
import type { DataPageEnhancedConfig } from '@/components/crud-page/DataPageEnhanced'
import { Contact } from "@/lib/repositories/contacts";
import { ContactsAPI } from "@/lib/services/contactsApi";
import { PagingAndSearch } from "@/lib/services/types";
import { contactStatsConfig } from "@/components/crud-page/stats/presets";

// API service functions using ContactsAPI
const contactsAPI = {
  async fetchAll(params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: Array<{ field: string; direction: 'asc' | 'desc' }>;
    filters?: Array<{ field: string; value: any }>;
  }): Promise<{ items: Contact[]; total: number; page?: number }> {
    try {
      // Convert our params to PagingAndSearch format
      const apiParams: PagingAndSearch<{
        read?: boolean;
        type?: string;
        search?: string;
      }> = {
        page: params?.page || 1,
        per_page: params?.limit || 10,
        search: params?.search,
        sorts: params?.sorts || [],
        filters: params?.filters?.map(f => ({
          field: f.field,
          value: f.value
        })) || []
      };

      // Add includeDeleted as a filter if specified
      if (params?.includeDeleted) {
        apiParams.filters = apiParams.filters || [];
        apiParams.filters.push({ field: 'includeDeleted', value: true });
      }

      const response = await ContactsAPI.All(apiParams).request();

      return {
        items: response.items || [],
        total: response.total || 0,
        page: response.page
      };
    } catch (error) {
      console.error('Error fetching contacts:', error);
      throw new Error('Failed to fetch contacts');
    }
  },

  async fetchStats(params?: {
    search?: string;
    includeDeleted?: boolean;
    filters?: Array<{ field: string; value: any }>;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<any> {
    try {
      const response = await ContactsAPI.Stats(params).request();
      return response;
    } catch (error) {
      console.error('Error fetching contact statistics:', error);
      throw new Error('Failed to fetch contact statistics');
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await ContactsAPI.Delete(id).request();
    } catch (error: any) {
      console.error('Error deleting contact:', error);

      // Extract error message from the response
      let errorMessage = 'Failed to delete contact';
      if (error?.response?.data?.messages && error.response.data.messages.length > 0) {
        errorMessage = error.response.data.messages[0];
      } else if (error?.message) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  }
};

// Contacts page configuration
const contactsConfig: DataPageEnhancedConfig<Contact> = {
  title: "Customer Contacts",
  subtitle: "Manage and monitor customer contacts for CS operations",
  headers: [
    "Name",
    "Phone",
    "Email",
    "Tags",
    "Notes Count",
    "Created Date",
    "Updated Date",
    "Created By"
  ],
  columnWidths: {
    "Name": "180px",
    "Phone": "150px",
    "Email": "200px",
    "Tags": "200px",
    "Notes Count": "120px",
    "Created Date": "140px",
    "Updated Date": "140px",
    "Created By": "150px"
  },
  defaultColumnWidth: "150px",
  pinnedColumns: ["Name", "Phone"],

  // Data transformation
  transformToTableRow: (item: Contact): TableRowData => {
    return {
      id: item.id,
      columns: [
        item.name,
        item.phone,
        item.email || '-',
        item.tags ? item.tags.join(', ') : '-',
        item.notes ? item.notes.length.toString() : '0',
        new Date(item.createdAt).toLocaleDateString('id-ID'),
        new Date(item.updatedAt).toLocaleDateString('id-ID'),
        item.createdBy || '-'
      ]
    }
  },

  // CRUD operations
  fetchData: async (params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: Array<{ field: string; direction: 'asc' | 'desc' }>;
    filters?: Array<{ field: string; value: any }>;
  }) => {
    const result = await contactsAPI.fetchAll(params);
    return result;
  },

  fetchStats: async (params?: {
    search?: string;
    includeDeleted?: boolean;
    filters?: Array<{ field: string; value: any }>;
    dateFrom?: string;
    dateTo?: string;
  }) => {
    const result = await contactsAPI.fetchStats(params);
    return result;
  },

  deleteItem: async (id: string): Promise<void> => {
    // This will be handled by DataPage with optimistic updates
    await contactsAPI.delete(id);
  },

  // Navigation
  addRoute: "/contacts/new",
  editRoute: (id: string) => `/contacts/${id}`,
  bulkRoute: "/contacts/bulk",

  // Filtering and sorting
  sortOptions: [
    { value: 'name', label: 'Name' },
    { value: 'phone', label: 'Phone' },
    { value: 'email', label: 'Email' },
    { value: 'createdAt', label: 'Created Date' },
    { value: 'updatedAt', label: 'Updated Date' }
  ],

  dateFilterOptions: [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'this_week', label: 'This Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'all', label: 'All Dates' }
  ],

  filters: [
    { id: 'active', name: 'Active' },
    { id: 'deleted', name: 'Deleted' },
    { id: 'has_email', name: 'Has Email' },
    { id: 'has_tags', name: 'Has Tags' }
  ],

  // Stats configuration for CS contacts (legacy)
  statsConfig: {
    title: "Contact Statistics",
    statusOptions: ['Active', 'Deleted', 'Pending', 'Archived'],
    categoryLabel: "Tag",
    assignedToLabel: "Created By",
    clientLabel: "Contact",
    amountLabel: "Interactions",
    amountPrefix: "",
    dateField: "createdAt",
    statusField: "status",
    categoryField: "category",
    assignedToField: "assignedTo",
    clientIdField: "clientId"
  },

  // ✨ New modular stats configuration
  useModularStats: true,
  modularStatsConfig: contactStatsConfig
}

export default function ContactsPage() {
  return <DataPageEnhanced config={contactsConfig} />
}
