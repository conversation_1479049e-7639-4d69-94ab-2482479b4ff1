'use client'

import { TableRowData } from "@/components/crud-page";
import DataPageEnhanced from '@/components/crud-page/DataPageEnhanced'
import type { DataPageEnhancedConfig, StatsResponse, StatItem, StatBreakdown } from '@/components/crud-page/DataPageEnhanced'
import { Contact } from "@/lib/repositories/contacts";
import { ContactsAPI } from "@/lib/services/contactsApi";
import { PagingAndSearch } from "@/lib/services/types";
import { useLocalization } from "@/hooks/useLocalization/client";
import { contactsLocales } from "./locales";

// ✨ Mapper function to convert backend response to generic StatsResponse
const mapContactStatsToGeneric = (backendData: any, t: (key: string, template?: Record<string, string | number>) => string): StatsResponse => {
  const data = backendData.data || backendData

  // Create main stat cards
  const stats: StatItem[] = [
    {
      id: 'total_contacts',
      title: t('stats.total_contacts'),
      value: data.totalContacts || 0,
      label: `${data.recentContacts || 0} ${t('stats.added_recently')}`,
      description: t('stats.all_contacts_description'),
      icon: 'users',
      cardColor: 'bg-blue-50',
      textColor: 'text-blue-900'
    },
    {
      id: 'active_contacts',
      title: t('stats.active_contacts'),
      value: data.activeContacts || 0,
      label: data.totalContacts > 0 ? `${((data.activeContacts / data.totalContacts) * 100).toFixed(1)}${t('stats.percentage_of_total')}` : `0${t('stats.percentage_of_total')}`,
      cardColor: 'bg-green-50',
      textColor: 'text-green-900'
    },
    {
      id: 'contacts_with_email',
      title: t('stats.contacts_with_email'),
      value: data.contactsWithEmail || 0,
      label: data.totalContacts > 0 ? `${((data.contactsWithEmail / data.totalContacts) * 100).toFixed(1)}${t('stats.percentage_of_total')}` : `0${t('stats.percentage_of_total')}`,
      cardColor: 'bg-purple-50',
      textColor: 'text-purple-900'
    },
    {
      id: 'contacts_with_tags',
      title: t('stats.contacts_with_tags'),
      value: data.contactsWithTags || 0,
      label: data.totalContacts > 0 ? `${((data.contactsWithTags / data.totalContacts) * 100).toFixed(1)}${t('stats.percentage_of_total')}` : `0${t('stats.percentage_of_total')}`,
      cardColor: 'bg-orange-50',
      textColor: 'text-orange-900'
    }
  ]

  // Create breakdown sections
  const breakdowns: StatBreakdown[] = []

  // Status breakdown
  if (data.statusBreakdown && data.statusBreakdown.length > 0) {
    breakdowns.push({
      id: 'status_breakdown',
      title: t('stats.contact_status'),
      items: data.statusBreakdown.map((item: any) => ({
        label: item.status,
        value: item.count,
        percentage: item.percentage,
        color: item.status === 'Active' ? '#10B981' :
               item.status === 'Deleted' ? '#EF4444' :
               item.status === 'Pending' ? '#F59E0B' : '#6B7280'
      }))
    })
  }

  // Tag breakdown
  if (data.tagBreakdown && data.tagBreakdown.length > 0) {
    breakdowns.push({
      id: 'tag_breakdown',
      title: t('stats.top_tags'),
      items: data.tagBreakdown.map((item: any, index: number) => ({
        label: item.tag,
        value: item.count,
        percentage: item.percentage,
        color: ['#3B82F6', '#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444'][index % 6]
      }))
    })
  }

  // Created by breakdown
  if (data.createdByBreakdown && data.createdByBreakdown.length > 0) {
    breakdowns.push({
      id: 'created_by_breakdown',
      title: t('stats.created_by'),
      items: data.createdByBreakdown.map((item: any, index: number) => ({
        label: item.createdBy,
        value: item.count,
        percentage: item.percentage,
        color: ['#6366F1', '#EC4899', '#14B8A6', '#F97316', '#84CC16'][index % 5]
      }))
    })
  }

  return {
    stats,
    breakdowns,
    summary: {
      total: data.totalContacts || 0,
      period: t('stats.all_time'),
      lastUpdated: new Date().toISOString()
    }
  }
}

// API service functions using ContactsAPI
const contactsAPI = {
  async fetchAll(params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: Array<{ field: string; direction: 'asc' | 'desc' }>;
    filters?: Array<{ field: string; value: any }>;
  }): Promise<{ items: Contact[]; total: number; page?: number }> {
    try {
      // Convert our params to PagingAndSearch format
      const apiParams: PagingAndSearch<{
        read?: boolean;
        type?: string;
        search?: string;
      }> = {
        page: params?.page || 1,
        per_page: params?.limit || 10,
        search: params?.search,
        sorts: params?.sorts || [],
        filters: params?.filters?.map(f => ({
          field: f.field,
          value: f.value
        })) || []
      };

      // Add includeDeleted as a filter if specified
      if (params?.includeDeleted) {
        apiParams.filters = apiParams.filters || [];
        apiParams.filters.push({ field: 'includeDeleted', value: true });
      }

      const response = await ContactsAPI.All(apiParams).request();

      return {
        items: response.items || [],
        total: response.total || 0,
        page: response.page
      };
    } catch (error) {
      console.error('Error fetching contacts:', error);
      throw new Error('Failed to fetch contacts');
    }
  },

  async fetchStats(params?: {
    search?: string;
    includeDeleted?: boolean;
    filters?: Array<{ field: string; value: any }>;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<any> {
    try {
      const response = await ContactsAPI.Stats(params).request();
      return response;
    } catch (error) {
      console.error('Error fetching contact statistics:', error);
      throw new Error('Failed to fetch contact statistics');
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await ContactsAPI.Delete(id).request();
    } catch (error: any) {
      console.error('Error deleting contact:', error);

      // Extract error message from the response
      let errorMessage = 'Failed to delete contact';
      if (error?.response?.data?.messages && error.response.data.messages.length > 0) {
        errorMessage = error.response.data.messages[0];
      } else if (error?.message) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  }
};

// Contacts page configuration
const contactsConfig: DataPageEnhancedConfig<Contact> = {
  title: "Customer Contacts",
  subtitle: "Manage and monitor customer contacts for CS operations",
  headers: [
    "Name",
    "Phone",
    "Email",
    "Tags",
    "Notes Count",
    "Created Date",
    "Updated Date",
    "Created By"
  ],
  columnWidths: {
    "Name": "180px",
    "Phone": "150px",
    "Email": "200px",
    "Tags": "200px",
    "Notes Count": "120px",
    "Created Date": "140px",
    "Updated Date": "140px",
    "Created By": "150px"
  },
  defaultColumnWidth: "150px",
  pinnedColumns: ["Name", "Phone"],

  // Data transformation
  transformToTableRow: (item: Contact): TableRowData => {
    return {
      id: item.id,
      columns: [
        item.name,
        item.phone,
        item.email || '-',
        item.tags ? item.tags.join(', ') : '-',
        item.notes ? item.notes.length.toString() : '0',
        new Date(item.createdAt).toLocaleDateString('id-ID'),
        new Date(item.updatedAt).toLocaleDateString('id-ID'),
        item.createdBy || '-'
      ]
    }
  },

  // CRUD operations
  fetchData: async (params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: Array<{ field: string; direction: 'asc' | 'desc' }>;
    filters?: Array<{ field: string; value: any }>;
  }) => {
    const result = await contactsAPI.fetchAll(params);
    return result;
  },



  deleteItem: async (id: string): Promise<void> => {
    // This will be handled by DataPage with optimistic updates
    await contactsAPI.delete(id);
  },

  // Navigation
  addRoute: "/contacts/new",
  editRoute: (id: string) => `/contacts/${id}`,
  bulkRoute: "/contacts/bulk",

  // Filtering and sorting
  sortOptions: [
    { value: 'name', label: 'Name' },
    { value: 'phone', label: 'Phone' },
    { value: 'email', label: 'Email' },
    { value: 'createdAt', label: 'Created Date' },
    { value: 'updatedAt', label: 'Updated Date' }
  ],

  dateFilterOptions: [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'this_week', label: 'This Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'all', label: 'All Dates' }
  ],

  filters: [
    { id: 'active', name: 'Active' },
    { id: 'deleted', name: 'Deleted' },
    { id: 'has_email', name: 'Has Email' },
    { id: 'has_tags', name: 'Has Tags' }
  ],

  // ✨ Enable generic stats system
  useGenericStats: true
}

export default function ContactsPage() {
  const { t } = useLocalization("contacts", contactsLocales)

  // Create localized config
  const localizedContactsConfig: DataPageEnhancedConfig<Contact> = {
    ...contactsConfig,
    title: t('page_title'),
    subtitle: t('page_subtitle'),
    headers: [
      t('headers.name'),
      t('headers.phone'),
      t('headers.email'),
      t('headers.tags'),
      t('headers.notes_count'),
      t('headers.created_date'),
      t('headers.updated_date'),
      t('headers.created_by')
    ],
    sortOptions: [
      { value: 'name', label: t('sort_options.name') },
      { value: 'phone', label: t('sort_options.phone') },
      { value: 'email', label: t('sort_options.email') },
      { value: 'createdAt', label: t('sort_options.created_at') },
      { value: 'updatedAt', label: t('sort_options.updated_at') }
    ],
    dateFilterOptions: [
      { value: 'today', label: t('date_filter_options.today') },
      { value: 'yesterday', label: t('date_filter_options.yesterday') },
      { value: 'this_week', label: t('date_filter_options.this_week') },
      { value: 'last_week', label: t('date_filter_options.last_week') },
      { value: 'this_month', label: t('date_filter_options.this_month') },
      { value: 'last_month', label: t('date_filter_options.last_month') },
      { value: 'all', label: t('date_filter_options.all') }
    ],
    filters: [
      { id: 'has_phone', name: t('filters.has_phone') },
      { id: 'has_email', name: t('filters.has_email') },
      { id: 'has_tags', name: t('filters.has_tags') }
    ],
    fetchStats: async (params?: {
      search?: string;
      includeDeleted?: boolean;
      filters?: Array<{ field: string; value: any }>;
      dateFrom?: string;
      dateTo?: string;
    }) => {
      const result = await contactsAPI.fetchStats(params);
      // ✨ Transform backend response to generic StatsResponse format with localization
      return mapContactStatsToGeneric(result, t);
    }
  }

  return <DataPageEnhanced config={localizedContactsConfig} />
}
