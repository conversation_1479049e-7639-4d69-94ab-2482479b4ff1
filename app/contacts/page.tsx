'use client'

import { BaseDataItem, TableRowData } from "@/components/table";
import DataPageEnhanced from '@/components/crud-page/DataPageEnhanced'
import type { DataPageEnhancedConfig } from '@/components/crud-page/DataPageEnhanced'
import { ContactsAPI } from "@/lib/services/contactsApi";
import { PagingAndSearch } from "@/lib/services/types";

// Contact-specific data interface extending the API Contact interface
interface ContactData extends BaseDataItem {
  name: string;
  phone: string;
  email?: string;
  tags?: string[];
  notes?: { text: string; createdAt: string }[];
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
}

// API service functions using ContactsAPI
const contactsAPI = {
  async fetchAll(params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: Array<{ field: string; direction: 'asc' | 'desc' }>;
    filters?: Array<{ field: string; value: any }>;
  }): Promise<{ items: ContactData[]; total: number; page?: number }> {
    try {
      // Convert our params to PagingAndSearch format
      const apiParams: PagingAndSearch<{
        read?: boolean;
        type?: string;
        search?: string;
      }> = {
        page: params?.page || 1,
        per_page: params?.limit || 10,
        search: params?.search,
        sorts: params?.sorts || [],
        filters: params?.filters?.map(f => ({
          field: f.field,
          value: f.value
        })) || []
      };

      // Add includeDeleted as a filter if specified
      if (params?.includeDeleted) {
        apiParams.filters = apiParams.filters || [];
        apiParams.filters.push({ field: 'includeDeleted', value: true });
      }

      const response = await ContactsAPI.All(apiParams).request();

      return {
        items: response.items || [],
        total: response.total || 0,
        page: response.page
      };
    } catch (error) {
      console.error('Error fetching contacts:', error);
      throw new Error('Failed to fetch contacts');
    }
  },

  async fetchStats(params?: {
    search?: string;
    includeDeleted?: boolean;
    filters?: Array<{ field: string; value: any }>;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<any> {
    try {
      const response = await ContactsAPI.Stats(params).request();
      return response;
    } catch (error) {
      console.error('Error fetching contact statistics:', error);
      throw new Error('Failed to fetch contact statistics');
    }
  },

  async delete(id: string): Promise<void> {
    try {
      await ContactsAPI.Delete(id).request();
    } catch (error: any) {
      console.error('Error deleting contact:', error);

      // Extract error message from the response
      let errorMessage = 'Failed to delete contact';
      if (error?.response?.data?.messages && error.response.data.messages.length > 0) {
        errorMessage = error.response.data.messages[0];
      } else if (error?.message) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  }
};

// Contacts page configuration
const contactsConfig: DataPageEnhancedConfig = {
  title: "Customer Contacts",
  subtitle: "Manage and monitor customer contacts for CS operations",
  headers: [
    "Name",
    "Phone",
    "Email",
    "Tags",
    "Notes Count",
    "Created Date",
    "Updated Date",
    "Created By"
  ],
  columnWidths: {
    "Name": "180px",
    "Phone": "150px",
    "Email": "200px",
    "Tags": "200px",
    "Notes Count": "120px",
    "Created Date": "140px",
    "Updated Date": "140px",
    "Created By": "150px"
  },
  defaultColumnWidth: "150px",
  pinnedColumns: ["Name", "Phone"],

  // Data transformation
  transformToTableRow: (item: BaseDataItem): TableRowData => {
    const contactItem = item as ContactData
    return {
      id: contactItem.id,
      columns: [
        contactItem.name,
        contactItem.phone,
        contactItem.email || '-',
        contactItem.tags ? contactItem.tags.join(', ') : '-',
        contactItem.notes ? contactItem.notes.length.toString() : '0',
        new Date(contactItem.createdAt).toLocaleDateString('id-ID'),
        new Date(contactItem.updatedAt).toLocaleDateString('id-ID'),
        contactItem.createdBy || '-'
      ]
    }
  },

  transformToStatsData: (item: BaseDataItem) => {
    const contactItem = item as ContactData
    return {
      id: contactItem.id,
      date: contactItem.createdAt.toString(),
      status: contactItem.deletedAt ? 'Deleted' : 'Active',
      category: contactItem.tags && contactItem.tags.length > 0 ? contactItem.tags[0] : 'No Tag',
      assignedTo: contactItem.createdBy || 'System',
      clientId: contactItem.id,
      originalData: contactItem
    }
  },

  // CRUD operations
  fetchData: async (params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: Array<{ field: string; direction: 'asc' | 'desc' }>;
    filters?: Array<{ field: string; value: any }>;
  }) => {
    const result = await contactsAPI.fetchAll(params);
    return result;
  },

  deleteItem: async (id: string): Promise<void> => {
    // This will be handled by DataPage with optimistic updates
    await contactsAPI.delete(id);
  },

  // Navigation
  addRoute: "/contacts/new",
  editRoute: (id: string) => `/contacts/${id}`,
  bulkRoute: "/contacts/bulk",

  // Filtering and sorting
  sortOptions: [
    { value: 'name', label: 'Name' },
    { value: 'phone', label: 'Phone' },
    { value: 'email', label: 'Email' },
    { value: 'createdAt', label: 'Created Date' },
    { value: 'updatedAt', label: 'Updated Date' }
  ],

  dateFilterOptions: [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'this_week', label: 'This Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'all', label: 'All Dates' }
  ],

  filters: [
    { id: 'active', name: 'Active' },
    { id: 'deleted', name: 'Deleted' },
    { id: 'has_email', name: 'Has Email' },
    { id: 'has_tags', name: 'Has Tags' }
  ],

  // Stats configuration for CS contacts
  statsConfig: {
    title: "Contact Statistics",
    statusOptions: ['Active', 'Deleted', 'Pending', 'Archived'],
    categoryLabel: "Tag",
    assignedToLabel: "Created By",
    clientLabel: "Contact",
    amountLabel: "Interactions",
    amountPrefix: "",
    dateField: "createdAt",
    statusField: "status",
    categoryField: "category",
    assignedToField: "assignedTo",
    clientIdField: "clientId"
  }
}

export default function ContactsPage() {
  return <DataPageEnhanced config={contactsConfig} />
}
