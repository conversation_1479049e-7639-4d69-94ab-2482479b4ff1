'use client'

import { DataEditorPage, DataEditorConfig } from '@/components/crud-page'
import { ContactsAPI } from "@/lib/services/contactsApi";
import { Contact } from "@/lib/repositories/contacts/interface";

interface ClientPageProps {
  id: string;
}

// Contact editor configuration for editing existing contacts
const contactEditorConfig: DataEditorConfig = {
  title: "Contact",
  subtitle: "Edit customer contact information",

  fields: [
    // Basic Information
    {
      name: 'name',
      label: 'Full Name',
      type: 'text',
      placeholder: 'Enter full name',
      validation: {
        required: true,
        minLength: 2,
        maxLength: 100
      },
      group: 'basic'
    },
    {
      name: 'phone',
      label: 'Phone Number',
      type: 'phone',
      placeholder: '+628123456789',
      validation: {
        required: true,
        pattern: /^[\+]?[1-9][\d]{0,15}$/
      },
      description: 'WhatsApp number for customer communication',
      group: 'basic'
    },
    {
      name: 'email',
      label: 'Email Address',
      type: 'email',
      placeholder: '<EMAIL>',
      validation: {
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      },
      group: 'basic'
    },

    // Classification
    {
      name: 'tags',
      label: 'Tags',
      type: 'text',
      placeholder: 'VIP, Regular Customer, New Lead',
      description: 'Separate multiple tags with commas',
      group: 'classification'
    },

    // Additional Information
    {
      name: 'company',
      label: 'Company',
      type: 'text',
      placeholder: 'Company name',
      validation: {
        maxLength: 100
      },
      group: 'additional'
    },
    {
      name: 'position',
      label: 'Position',
      type: 'text',
      placeholder: 'Job title',
      validation: {
        maxLength: 100
      },
      group: 'additional'
    },
    {
      name: 'address',
      label: 'Address',
      type: 'textarea',
      placeholder: 'Enter full address',
      rows: 3,
      validation: {
        maxLength: 500
      },
      group: 'additional'
    },
    {
      name: 'birthday',
      label: 'Birthday',
      type: 'date',
      group: 'additional'
    },

    // Notes
    {
      name: 'notes',
      label: 'Notes',
      type: 'textarea',
      placeholder: 'Enter additional notes about this contact',
      rows: 4,
      validation: {
        maxLength: 1000
      },
      group: 'notes'
    },

    // System Information (read-only)
    {
      name: 'createdAt',
      label: 'Created Date',
      type: 'text',
      disabled: true,
      group: 'system'
    },
    {
      name: 'updatedAt',
      label: 'Last Updated',
      type: 'text',
      disabled: true,
      group: 'system'
    },
    {
      name: 'createdBy',
      label: 'Created By',
      type: 'text',
      disabled: true,
      group: 'system'
    }
  ],

  sections: [
    {
      title: 'Basic Information',
      description: 'Essential contact details',
      fields: ['name', 'phone', 'email']
    },
    {
      title: 'Classification',
      description: 'Tags and categories',
      fields: ['tags']
    },
    {
      title: 'Additional Information',
      description: 'Optional details',
      fields: ['company', 'position', 'address', 'birthday']
    },
    {
      title: 'Notes',
      description: 'Additional information and remarks',
      fields: ['notes']
    },
    {
      title: 'System Information',
      description: 'Audit trail and metadata',
      fields: ['createdAt', 'updatedAt', 'createdBy']
    }
  ],

  // Data operations
  fetchData: async (id: string) => {
    try {
      const contactData = await ContactsAPI.Detail(id).request()

      if (!contactData) {
        throw new Error('Contact not found')
      }

      // Transform data for the form
      // Note: Some fields (company, position, address, birthday) are not in the Contact interface
      // but are used in the form. We'll handle them gracefully.
      const extendedContactData = contactData as Contact & {
        company?: string;
        position?: string;
        address?: string;
        birthday?: string;
      }

      const transformedData = {
        id: contactData.id,
        name: contactData.name || '',
        phone: contactData.phone || '',
        email: contactData.email || '',
        tags: contactData.tags ? contactData.tags.join(', ') : '',
        company: extendedContactData.company || '',
        position: extendedContactData.position || '',
        address: extendedContactData.address || '',
        birthday: extendedContactData.birthday ? new Date(extendedContactData.birthday).toISOString().split('T')[0] : '',
        notes: contactData.notes && contactData.notes.length > 0 ? contactData.notes[0].text : '',
        createdAt: contactData.createdAt ? new Date(contactData.createdAt).toLocaleString('id-ID') : '',
        updatedAt: contactData.updatedAt ? new Date(contactData.updatedAt).toLocaleString('id-ID') : '',
        createdBy: contactData.createdBy || 'System'
      }

      return transformedData
    } catch (error: any) {
      console.error('Error fetching contact:', error)

      if (error?.response?.status === 404) {
        throw new Error('Contact not found')
      }

      throw new Error(error?.message || 'Failed to fetch contact data')
    }
  },

  saveData: async (data: Record<string, any>, _isEdit: boolean) => {
    try {
      // Transform tags from comma-separated string to array
      // Note: Some fields (company, position, address, birthday) are not in the Contact interface
      // but we'll include them in the payload for the API to handle
      const transformedData = {
        name: data.name,
        phone: data.phone,
        email: data.email || undefined,
        tags: data.tags ? data.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],
        // Handle notes - preserve existing notes and add new one if changed
        notes: data.notes ? [{ text: data.notes, createdAt: new Date().toISOString() }] : [],
        // Extended fields (not in Contact interface but used in form)
        ...(data.company && { company: data.company }),
        ...(data.position && { position: data.position }),
        ...(data.address && { address: data.address }),
        ...(data.birthday && { birthday: data.birthday })
      }

      await ContactsAPI.Update(data.id, transformedData).request()
    } catch (error: any) {
      console.error('Error updating contact:', error)

      // Extract error message from the response
      let errorMessage = 'Failed to update contact'
      if (error?.response?.data?.messages && error.response.data.messages.length > 0) {
        errorMessage = error.response.data.messages[0]
      } else if (error?.message) {
        errorMessage = error.message
      }

      if (error?.response?.status === 404) {
        throw new Error('Contact not found')
      }

      throw new Error(errorMessage)
    }
  },

  // Navigation
  backRoute: '/contacts',
  successRoute: '/contacts',

  // Customization
  submitButtonText: 'Update Contact',
  cancelButtonText: 'Cancel',
  showImagePreview: false,
  maxFileSize: 5, // 5MB
  allowedFileTypes: [
    'image/jpeg',
    'image/png',
    'image/gif'
  ]
}

export default function ClientPage({ id }: ClientPageProps) {
  return <DataEditorPage config={contactEditorConfig} id={id} />
}
