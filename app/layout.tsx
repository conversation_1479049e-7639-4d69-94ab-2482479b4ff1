// app/layout.tsx (Server Component)
import ClientLayout from '@/components/layout/ClientLayout';
import './globals.css';
import type { Metadata } from 'next';
import { Toaster } from "@/components/ui/sonner"
import { LocalizationProvider } from '@/hooks/useLocalization/localization-context';

export const metadata: Metadata = {
  title: 'CS CRM',
  description: 'A CRM with AI capabilities',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <ClientLayout>
          <LocalizationProvider initialLocale={'en'}>
            {children}
          </LocalizationProvider>
        </ClientLayout>
        <Toaster />
      </body>
    </html>
  );
}
