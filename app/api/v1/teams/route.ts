import { NextRequest, NextResponse } from "next/server";
import { teamsBusinessLogic } from "@/lib/repositories/businessLogics";
import { implHandleGetAllTeams, implHandleCreateTeam } from "./impl";
import { ERROR_CODES } from "@/app/api/error_codes";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);

    // Parse basic parameters
    const search = searchParams.get('search') || undefined;
    const includeDeleted = searchParams.get('includeDeleted') === 'true';
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : undefined;
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

    // Parse sorts parameter (expects JSON array or comma-separated field:direction pairs)
    let sorts: { field: string; direction: "asc" | "desc" }[] = [];
    const sortsParam = searchParams.get('sorts');
    if (sortsParam) {
      try {
        // Try parsing as JSON first
        sorts = JSON.parse(sortsParam);
      } catch {
        // Fallback: parse comma-separated field:direction pairs
        sorts = sortsParam.split(',').map(sort => {
          const [field, direction = 'asc'] = sort.trim().split(':');
          return {
            field: field.trim(),
            direction: (direction.trim().toLowerCase() === 'desc' ? 'desc' : 'asc') as "asc" | "desc"
          };
        }).filter(sort => sort.field);
      }
    }

    // Parse filters parameter (expects JSON array or field:value pairs)
    let filters: { field: string; value: any }[] = [];
    const filtersParam = searchParams.get('filters');
    if (filtersParam) {
      try {
        // Try parsing as JSON first
        filters = JSON.parse(filtersParam);
      } catch {
        // Fallback: parse comma-separated field:value pairs
        filters = filtersParam.split(',').map(filter => {
          const [field, ...valueParts] = filter.trim().split(':');
          const value = valueParts.join(':').trim();
          return field.trim() && value ? { field: field.trim(), value } : null;
        }).filter(Boolean) as { field: string; value: any }[];
      }
    }

    // Validate pagination parameters
    if (page !== undefined && (isNaN(page) || page < 1)) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: ["Page must be a positive integer"],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED]
        },
        { status: 400 }
      );
    }

    if (limit !== undefined && (isNaN(limit) || limit < 1 || limit > 1000)) {
      return NextResponse.json(
        {
          status: "failed",
          data: null,
          errors: ["Limit must be between 1 and 1000"],
          errorCodes: [ERROR_CODES.VALIDATION_FAILED]
        },
        { status: 400 }
      );
    }

    const params = {
      search,
      includeDeleted,
      page,
      limit,
      sorts,
      filters
    };

    const result = await implHandleGetAllTeams(teamsBusinessLogic, params);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("team GET route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const result = await implHandleCreateTeam(body, teamsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("team POST route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}
