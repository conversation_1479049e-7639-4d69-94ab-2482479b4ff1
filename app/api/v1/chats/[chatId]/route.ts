import { NextRequest, NextResponse } from "next/server";
import { chatsBusinessLogic } from "@/lib/repositories/businessLogics";
import { implHandleGetChat, implHandleUpdateChat, implHandleDeleteChat } from "../impl";
import { ERROR_CODES } from "@/app/api/error_codes";

export async function GET(
  _: NextRequest,
  context: { params: Promise<{ chatId: string }> }
) {
  try {
    const { chatId } = await context.params;
    const result = await implHandleGetChat(chatId, chatsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Chat GET route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ chatId: string }> }
) {
  try {
    const { chatId } = await context.params;
    const body = await req.json();
    const result = await implHandleUpdateChat(chatId, body, chatsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Chat PUT route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _: NextRequest,
  context: { params: Promise<{ chatId: string }> }
) {
  try {
    const { chatId } = await context.params;
    const result = await implHandleDeleteChat(chatId, chatsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Chat DELETE route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}
