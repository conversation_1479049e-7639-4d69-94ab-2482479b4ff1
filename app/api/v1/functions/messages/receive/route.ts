import { NextRequest, NextResponse } from "next/server";
import { implHandleReceiveMessages } from "./impl";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const result = await implHandleReceiveMessages(searchParams);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Receive messages route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: []
      },
      { status: 500 }
    );
  }
}
