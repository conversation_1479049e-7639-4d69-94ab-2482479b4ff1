import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { SendTypingSchema } from "@/lib/schemas/messages";
import { providers } from "@/lib/providers";
import { getAuthCookie } from "@/lib/cookies";
import { ERROR_CODES } from "@/app/api/error_codes";

export async function implHandleSendTyping(body: any): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate request body
    const validationResult = SendTypingSchema.safeParse(body);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => err.message);
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const { status: typingStatus, chatId } = validationResult.data;
    let { session, provider: providerKey } = validationResult.data;

    if (!providerKey) {
      providerKey = (await getAuthCookie("preferred_provider")) || "waha";
    }

    const provider = providers[providerKey];
    if (!provider) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [`Provider "${providerKey}" is not available.`],
          [ERROR_CODES.PROVIDER_NOT_FOUND]
        ),
      };
    }

    if (providerKey === "waha" && !session) {
      session = await getAuthCookie("waha_session_id");
    }

    if (!session) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Session ID is required for sending typing status."],
          [ERROR_CODES.SESSION_REQUIRED]
        ),
      };
    }

    const result = await provider.sendTyping(typingStatus, chatId, session);

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        provider: provider.name,
        session,
        result,
      }),
    };
  } catch (error: any) {
    console.error("Send typing error:", error);
    
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to send typing status. Please try again."],
        [ERROR_CODES.SEND_TYPING_FAILED]
      ),
    };
  }
}
