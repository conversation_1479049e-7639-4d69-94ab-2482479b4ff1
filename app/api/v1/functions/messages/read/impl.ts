import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ReadMessageSchema } from "@/lib/schemas/messages";
import { providers } from "@/lib/providers";
import { getAuthCookie, setAuthCookie } from "@/lib/cookies";
import { ERROR_CODES } from "@/app/api/error_codes";

export async function implHandleReadMessage(body: any): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate request body
    const validationResult = ReadMessageSchema.safeParse(body);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => err.message);
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const { chatId } = validationResult.data;
    let { session, provider: providerKey } = validationResult.data;

    if (!providerKey) {
      providerKey = (await getAuthCookie("preferred_provider")) || "waha";
    }

    const provider = providers[providerKey];
    if (!provider) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [`Provider "${providerKey}" is not available.`],
          [ERROR_CODES.PROVIDER_NOT_FOUND]
        ),
      };
    }

    if (!getAuthCookie("preferred_provider")) {
      setAuthCookie("preferred_provider", providerKey);
    }

    if (providerKey === "waha") {
      if (!session) {
        session = await getAuthCookie("waha_session_id");
      }
    }

    if (!session) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Session is required for reading messages."],
          [ERROR_CODES.SESSION_REQUIRED]
        ),
      };
    }

    const response = await provider.readMessage(chatId, session);

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        provider: provider.name,
        session,
        result: response,
      }),
    };
  } catch (error: any) {
    console.error("Read message error:", error);
    
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to read message. Please try again."],
        [ERROR_CODES.READ_FAILED]
      ),
    };
  }
}
