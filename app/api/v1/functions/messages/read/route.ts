import { NextRequest, NextResponse } from "next/server";
import { implHandleReadMessage } from "./impl";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json().catch(() => ({}));
    const result = await implHandleReadMessage(body);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Read message route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: []
      },
      { status: 500 }
    );
  }
}
