import { NextRequest, NextResponse } from "next/server";
import { providers } from "@/lib/providers";
import { getAuth<PERSON>ookie, setAuth<PERSON>ookie } from "@/lib/cookies";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json().catch(() => ({}));

    let providerKey = body.provider as string | undefined;
    let presence = body.presence as string | undefined;

    if (!presence) {
      presence = "available"; // Default presence status
    }

    if (!providerKey) {
      providerKey = (await getAuthCookie("preferred_provider")) || "waha";
    }

    const provider = providers[providerKey];
    if (!provider) {
      return NextResponse.json(
        { success: false, error: `Provider "${providerKey}" tidak tersedia.` },
        { status: 400 }
      );
    }

    if (!getAuthCookie("preferred_provider")) {
      setAuthCookie("preferred_provider", providerKey);
    }

    let sessionId: string | undefined;

    // misal ini dalam API route atau server action

    if (providerKey === "waha") {
      sessionId = await getAuthCookie("waha_session_id");
      if (!presence) presence = "online"; // Default presence status
    }

    console.log(presence, sessionId);

    const result = await provider.sendPresenceStatus(presence, sessionId);

    return NextResponse.json({
      success: true,
      provider: provider.name,
      session: sessionId,
      result,
    });
  } catch (error: any) {
    console.error("Send Presence Error:", error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
}
