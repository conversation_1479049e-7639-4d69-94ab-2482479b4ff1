import { v4 as uuid } from "uuid";
import { realtime } from "@/lib/realtime";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const body = await req.json();

  console.log("WAHA Webhook received chat-channel:", uuid());

  // Push to realtime
  if (body?.event === "message") {
    await realtime.server.trigger("chat-channel", "new-message", body?.payload);
  }

  return NextResponse.json({ success: true });
}
