import { AuthBusinessLogicInterface } from "@/lib/repositories/auth";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { RegisterSchema } from "@/lib/schemas/auth";
import { ERROR_CODES } from "@/app/api/error_codes";

export async function implHandleRegister(body: any, authBusinessLogic: AuthBusinessLogicInterface): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate request body
    const validationResult = RegisterSchema.safeParse(body);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => err.message);
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const { email, password, name } = validationResult.data;

    // Attempt registration
    const result = await authBusinessLogic.register({ email, password, name });
    
    return {
      status: 201,
      body: new ResponseWrapper("success", result),
    };
  } catch (error: any) {
    console.error("Registration error:", error);
    
    if (error.code === "USER_EXISTS") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["User with this email already exists"],
          [ERROR_CODES.USER_EXISTS]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Registration failed. Please try again."],
        [ERROR_CODES.REGISTRATION_FAILED]
      ),
    };
  }
}
