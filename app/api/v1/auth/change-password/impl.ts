import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface";
import { ChangePasswordSchema } from "@/lib/schemas/auth";
import { ResponseWrapper } from "@/lib/types/responseWrapper";

export async function implHandleChangePassword(
  body: any,
  authBusinessLogic: AuthBusinessLogicInterface,
  userId: string
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate request body
    const validation = ChangePasswordSchema.safeParse(body);
    if (!validation.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        )
      };
    }

    const { currentPassword, newPassword } = validation.data;

    // Change password
    const result = await authBusinessLogic.changePassword(userId, currentPassword, newPassword);

    if (!result.success) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          [result.message]
        )
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper(
        "success",
        { message: result.message }
      )
    };

  } catch (error) {
    console.error("Change password error:", error);
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Internal server error"]
      )
    };
  }
}
