import { NextRequest, NextResponse } from "next/server";
import { authBusinessLogic } from "@/lib/repositories/businessLogics";
import { implHandleDeleteAccount } from "./impl";
import { ResponseWrapper } from "@/lib/types/responseWrapper";

export async function DELETE(req: NextRequest) {
  try {
    // Extract user ID from token
    const authHeader = req.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          null,
          ["Authorization token required"]
        ),
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const user = await authBusinessLogic.validateToken(token);
    if (!user) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          null,
          ["Invalid or expired token"]
        ),
        { status: 401 }
      );
    }

    const body = await req.json();
    const result = await implHandleDeleteAccount(body, authBusinessLogic, user.id!);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Delete account route error:", error);
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        null,
        ["Internal server error"]
      ),
      { status: 500 }
    );
  }
}
