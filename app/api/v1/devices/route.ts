import { NextRequest, NextResponse } from "next/server";
import { implHandleGetDevices } from "./impl";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const result = await implHandleGetDevices(searchParams);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Devices route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        error: "Internal server error",
        errorCodes: []
      },
      { status: 500 }
    );
  }
}
