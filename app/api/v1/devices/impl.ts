import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { DeviceListSchema } from "@/lib/schemas/devices";
import { providers } from "@/lib/providers";
import { getAuthCookie } from "@/lib/cookies";
import { ERROR_CODES } from "../../error_codes";

export async function implHandleGetDevices(searchParams: URLSearchParams): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    let providerKey = searchParams.get("provider");

    // Validate query parameters
    const validationResult = DeviceListSchema.safeParse({ provider: providerKey });
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => err.message);
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (!providerKey) {
      providerKey = await getAuthCookie("preferred_provider") || "waha";
    }

    const provider = providers[providerKey];

    if (!provider) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [`Provider "${providerKey}" is not available.`],
          [ERROR_CODES.PROVIDER_NOT_FOUND]
        ),
      };
    }

    const devices = await provider.listDevices();

    return {
      status: 200,
      body: new ResponseWrapper("success", {
        provider: provider.name,
        devices,
      }),
    };
  } catch (error: any) {
    console.error("Get devices error:", error);
    
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch devices. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}
