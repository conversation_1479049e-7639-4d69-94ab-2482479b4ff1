import { contactsBusinessLogic } from "@/lib/repositories/businessLogics";
import { NextRequest, NextResponse } from "next/server";
import { implHandleGetContact, implHandleUpdateContact, implHandleDeleteContact } from "../impl";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";

export async function GET(
  _: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const result = await implHandleGetContact(id, contactsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Contact GET route error:", error);
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const body = await req.json();
    const result = await implHandleUpdateContact(id, body, contactsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Contact PUT route error:", error);
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
      { status: 500 }
    );
  }
}

export async function DELETE(
  _: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const result = await implHandleDeleteContact(id, contactsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("Contact DELETE route error:", error);
    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        undefined,
        ["Internal server error"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
      { status: 500 }
    );
  }
}
