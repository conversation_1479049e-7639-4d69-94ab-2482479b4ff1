import { ContactBusinessLogicInterface, Contact } from "@/lib/repositories/contacts/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ContactCreateSchema, ContactUpdateSchema } from "@/lib/validations/contact";
import { ERROR_CODES } from "@/app/api/error_codes";

// Create Contact Implementation
export async function implHandleCreateContact(
  data: any,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate input data
    const validationResult = ContactCreateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Transform notes to include createdAt if not present
    const createData = {
      ...validationResult.data,
      notes: validationResult.data.notes?.map(note => ({
        text: note.text,
        createdAt: new Date().toISOString()
      }))
    };

    const contacts = await businessLogic.create(createData);

    return {
      status: 201,
      body: new ResponseWrapper("success", contacts),
    };
  } catch (error: any) {
    console.error("Create contacts error:", error);

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create contacts. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Update Contact Implementation
export async function implHandleUpdateContact(
  id: string,
  data: any,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate input data
    const validationResult = ContactUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const contacts = await businessLogic.update(id, validationResult.data);

    if (!contacts) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Contact not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", contacts),
    };
  } catch (error: any) {
    console.error("Update contacts error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_UPDATE_DATA") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (error.code === "INVALID_EMAIL" || error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update contacts. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Get Contact by ID Implementation
export async function implHandleGetContact(
  id: string,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const contacts = await businessLogic.getById(id);

    if (!contacts) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Contact not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", contacts),
    };
  } catch (error: any) {
    console.error("Get contacts error:", error);

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch contacts. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

interface GetAllResultPaginated<T> {
  items: T[];
  page: number;
  total: number;
}

// Get All Contacts Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllContacts(
  businessLogic: ContactBusinessLogicInterface,
  params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: {
      field: keyof Contact | string;
      direction: "asc" | "desc";
    }[],
    filters?: {
      field: keyof Contact | string;
      value: Contact[keyof Contact] | any;
    }[];
  }
): Promise<{
  status: number;
  body: ResponseWrapper<GetAllResultPaginated<Contact>>;
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<Contact>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Contact>>(
              "failed",
              undefined,
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Validate sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      for (const sort of params.sorts) {
        if (!sort.field || sort.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Contact>>(
              "failed",
              undefined,
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
        if (!['asc', 'desc'].includes(sort.direction)) {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Contact>>(
              "failed",
              undefined,
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    };

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim();
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters;
    }

    // Add sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      queryParams.sorts = params.sorts;
    }

    const result = await businessLogic.getAll(queryParams);

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<Contact>>("success", { ...result, page: queryParams.page }),
    };
  } catch (error: any) {
    console.error("Get contacts error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<Contact>>(
        "failed",
        undefined,
        ["Failed to fetch contacts. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

// Delete Contact Implementation
export async function implHandleDeleteContact(
  id: string,
  businessLogic: ContactBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    await businessLogic.delete(id, hardDelete);

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: "Contact deleted successfully" }),
    };
  } catch (error: any) {
    console.error("Delete contacts error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete contacts. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// ============================================================================
// BULK OPERATIONS
// ============================================================================

// Types for bulk operations
export interface BulkImportRequest {
  operation: 'import';
  data: Array<{
    name: string;
    phone: string;
    email?: string;
    tags?: string[];
    notes?: Array<{ text: string; createdAt: string }>;
  }>;
}

export interface BulkUpdateRequest {
  operation: 'update';
  data: Array<{
    id: string;
    name: string;
    phone: string;
    email?: string;
    tags?: string[];
    notes?: Array<{ text: string; createdAt: string }>;
  }>;
}

export interface BulkDeleteRequest {
  operation: 'delete';
  ids: string[];
}

export interface BulkOperationResult {
  total: number;
  successful: number;
  failed: number;
  errors: Array<{
    row?: number;
    id?: string;
    field?: string;
    message: string;
  }>;
}

// Bulk import implementation
export async function implBulkImportContacts(
  request: BulkImportRequest,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<BulkOperationResult>;
}> {
  try {
    const { data } = request;

    if (!Array.isArray(data)) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["Invalid data format - expected array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["No data provided for import"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate all data first
    const validatedData: any[] = [];
    const validationErrors: Array<{ row: number; field: string; message: string }> = [];

    for (let i = 0; i < data.length; i++) {
      const contactData = data[i];

      // Validate using schema
      const validationResult = ContactCreateSchema.safeParse(contactData);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `${err.path.join('.')}: ${err.message}`
        );
        validationErrors.push({
          row: i,
          field: 'validation',
          message: errors.join(', ')
        });
      } else {
        // Transform notes to include createdAt
        const createData = {
          ...validationResult.data,
          notes: validationResult.data.notes?.map(note => ({
            text: note.text,
            createdAt: new Date().toISOString()
          }))
        };
        validatedData.push(createData);
      }
    }

    const results: BulkOperationResult = {
      total: data.length,
      successful: 0,
      failed: validationErrors.length,
      errors: validationErrors
    };

    // If we have valid data, perform bulk create
    if (validatedData.length > 0) {
      try {
        const createdContacts = await businessLogic.bulkCreate(validatedData);
        results.successful = createdContacts.length;
      } catch (error: any) {
        // If bulk operation fails, mark all remaining as failed
        results.failed = data.length;
        results.successful = 0;
        results.errors = [{
          row: 0,
          field: 'general',
          message: error instanceof Error ? error.message : 'Bulk create operation failed'
        }];
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", results),
    };
  } catch (error: any) {
    console.error("Bulk import contacts error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<BulkOperationResult>(
        "failed",
        undefined,
        ["Failed to import contacts. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Bulk update implementation
export async function implBulkUpdateContacts(
  request: BulkUpdateRequest,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<BulkOperationResult>;
}> {
  try {
    const { data } = request;

    if (!Array.isArray(data)) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["Invalid data format - expected array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate all data and prepare updates
    const validatedUpdates: Array<{ id: string; data: any }> = [];
    const validationErrors: Array<{ row: number; field: string; message: string }> = [];

    for (let i = 0; i < data.length; i++) {
      const contactData = data[i];

      // Validate required fields
      if (!contactData.id) {
        validationErrors.push({
          row: i,
          field: 'id',
          message: 'ID is required for updates'
        });
        continue;
      }

      // Prepare update data (only include fields that exist in ContactUpdateInput)
      const updateData = {
        name: contactData.name,
        phone: contactData.phone,
        email: contactData.email,
        tags: contactData.tags,
        notes: contactData.notes?.map(note => ({
          text: note.text,
          createdAt: note.createdAt || new Date().toISOString()
        }))
      };

      validatedUpdates.push({
        id: contactData.id,
        data: updateData
      });
    }

    const results: BulkOperationResult = {
      total: data.length,
      successful: 0,
      failed: validationErrors.length,
      errors: validationErrors
    };

    // If we have valid updates, perform bulk update
    if (validatedUpdates.length > 0) {
      try {
        const updatedCount = await businessLogic.bulkUpdate(validatedUpdates);
        results.successful = updatedCount;

        // If some updates failed (updatedCount < validatedUpdates.length)
        if (updatedCount < validatedUpdates.length) {
          const failedCount = validatedUpdates.length - updatedCount;
          results.failed += failedCount;
          results.errors.push({
            row: 0,
            field: 'general',
            message: `${failedCount} contacts could not be updated (may not exist or have validation errors)`
          });
        }
      } catch (error: any) {
        // If bulk operation fails, mark all remaining as failed
        results.failed = data.length;
        results.successful = 0;
        results.errors = [{
          row: 0,
          field: 'general',
          message: error instanceof Error ? error.message : 'Bulk update operation failed'
        }];
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", results),
    };
  } catch (error: any) {
    console.error("Bulk update contacts error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<BulkOperationResult>(
        "failed",
        undefined,
        ["Failed to update contacts. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Bulk delete implementation
export async function implBulkDeleteContacts(
  request: BulkDeleteRequest,
  businessLogic: ContactBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<BulkOperationResult>;
}> {
  try {
    const { ids } = request;

    if (!Array.isArray(ids)) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["Invalid IDs format - expected array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper<BulkOperationResult>(
          "failed",
          undefined,
          ["No IDs provided for deletion"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const results: BulkOperationResult = {
      total: ids.length,
      successful: 0,
      failed: 0,
      errors: []
    };

    // Perform bulk delete operation
    try {
      const deletedCount = await businessLogic.bulkDelete(ids, false); // soft delete by default
      results.successful = deletedCount;

      // If some deletes failed (deletedCount < ids.length)
      if (deletedCount < ids.length) {
        const failedCount = ids.length - deletedCount;
        results.failed = failedCount;
        results.errors.push({
          id: 'bulk',
          message: `${failedCount} contacts could not be deleted (may not exist or already deleted)`
        });
      }
    } catch (error: any) {
      // If bulk operation fails, mark all as failed
      results.failed = ids.length;
      results.successful = 0;
      results.errors = [{
        id: 'bulk',
        message: error instanceof Error ? error.message : 'Bulk delete operation failed'
      }];
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", results),
    };
  } catch (error: any) {
    console.error("Bulk delete contacts error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<BulkOperationResult>(
        "failed",
        undefined,
        ["Failed to delete contacts. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Validation helpers
export function implValidateBulkImportRequest(body: any): body is BulkImportRequest {
  return (
    body &&
    body.operation === 'import' &&
    Array.isArray(body.data) &&
    body.data.length > 0 &&
    body.data.every((item: any) =>
      typeof item === 'object' &&
      typeof item.name === 'string' &&
      typeof item.phone === 'string'
    )
  );
}

export function implValidateBulkUpdateRequest(body: any): body is BulkUpdateRequest {
  return (
    body &&
    body.operation === 'update' &&
    Array.isArray(body.data) &&
    body.data.length > 0 &&
    body.data.every((item: any) =>
      typeof item === 'object' &&
      typeof item.id === 'string' &&
      typeof item.name === 'string' &&
      typeof item.phone === 'string'
    )
  );
}

export function implValidateBulkDeleteRequest(body: any): body is BulkDeleteRequest {
  return (
    body &&
    body.operation === 'delete' &&
    Array.isArray(body.ids) &&
    body.ids.length > 0 &&
    body.ids.every((id: any) => typeof id === 'string')
  );
}

// ============================================================================
// STATS OPERATIONS
// ============================================================================

export interface ContactsStatsData {
  totalContacts: number;
  activeContacts: number;
  deletedContacts: number;
  contactsWithEmail: number;
  contactsWithTags: number;
  recentContacts: number; // last 7 days
  statusBreakdown: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  tagBreakdown: Array<{
    tag: string;
    count: number;
    percentage: number;
  }>;
  createdByBreakdown: Array<{
    createdBy: string;
    count: number;
    percentage: number;
  }>;
  dailyStats: Array<{
    date: string;
    created: number;
    deleted: number;
  }>;
}

// Get Contacts Stats Implementation
export async function implHandleGetContactsStats(
  businessLogic: ContactBusinessLogicInterface,
  params?: {
    search?: string;
    includeDeleted?: boolean;
    filters?: {
      field: string;
      value: any;
    }[];
    dateFrom?: string;
    dateTo?: string;
  }
): Promise<{
  status: number;
  body: ResponseWrapper<ContactsStatsData>;
}> {
  try {
    // Get all contacts with filters
    const allContactsResult = await businessLogic.getAll({
      search: params?.search,
      includeDeleted: params?.includeDeleted,
      filters: params?.filters,
      limit: 10000 // Get all for stats calculation
    });

    const contacts = allContactsResult.items;
    const totalContacts = contacts.length;

    // Calculate basic stats
    const activeContacts = contacts.filter(c => !c.deletedAt).length;
    const deletedContacts = contacts.filter(c => c.deletedAt).length;
    const contactsWithEmail = contacts.filter(c => c.email && c.email.trim() !== '').length;
    const contactsWithTags = contacts.filter(c => c.tags && c.tags.length > 0).length;

    // Recent contacts (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentContacts = contacts.filter(c => new Date(c.createdAt) >= sevenDaysAgo).length;

    // Status breakdown
    const statusBreakdown = [
      {
        status: 'Active',
        count: activeContacts,
        percentage: totalContacts > 0 ? Math.round((activeContacts / totalContacts) * 100) : 0
      },
      {
        status: 'Deleted',
        count: deletedContacts,
        percentage: totalContacts > 0 ? Math.round((deletedContacts / totalContacts) * 100) : 0
      }
    ];

    // Tag breakdown
    const tagCounts: Record<string, number> = {};
    contacts.forEach(contact => {
      if (contact.tags && contact.tags.length > 0) {
        contact.tags.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });
      } else {
        tagCounts['No Tag'] = (tagCounts['No Tag'] || 0) + 1;
      }
    });

    const tagBreakdown = Object.entries(tagCounts)
      .map(([tag, count]) => ({
        tag,
        count,
        percentage: totalContacts > 0 ? Math.round((count / totalContacts) * 100) : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 tags

    // Created by breakdown
    const createdByCounts: Record<string, number> = {};
    contacts.forEach(contact => {
      const createdBy = contact.createdBy || 'System';
      createdByCounts[createdBy] = (createdByCounts[createdBy] || 0) + 1;
    });

    const createdByBreakdown = Object.entries(createdByCounts)
      .map(([createdBy, count]) => ({
        createdBy,
        count,
        percentage: totalContacts > 0 ? Math.round((count / totalContacts) * 100) : 0
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 creators

    // Daily stats for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyStats: Array<{ date: string; created: number; deleted: number }> = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const created = contacts.filter(c =>
        new Date(c.createdAt).toISOString().split('T')[0] === dateStr
      ).length;

      const deleted = contacts.filter(c =>
        c.deletedAt && new Date(c.deletedAt).toISOString().split('T')[0] === dateStr
      ).length;

      dailyStats.push({ date: dateStr, created, deleted });
    }

    const statsData: ContactsStatsData = {
      totalContacts,
      activeContacts,
      deletedContacts,
      contactsWithEmail,
      contactsWithTags,
      recentContacts,
      statusBreakdown,
      tagBreakdown,
      createdByBreakdown,
      dailyStats
    };

    return {
      status: 200,
      body: new ResponseWrapper("success", statsData),
    };
  } catch (error: any) {
    console.error("Get contacts stats error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<ContactsStatsData>(
        "failed",
        undefined,
        ["Failed to fetch contacts statistics. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}
