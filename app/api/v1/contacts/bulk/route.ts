import { NextRequest, NextResponse } from 'next/server'
import { contactsBusinessLogic } from "../../../../../lib/repositories/businessLogics";
import {
  implBulkImportContacts,
  implBulkUpdateContacts,
  implBulkDeleteContacts,
  implValidateBulkImportRequest,
  implValidateBulkUpdateRequest,
  implValidateBulkDeleteRequest
} from '../impl'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request format
    if (!implValidateBulkImportRequest(body)) {
      return NextResponse.json({
        status: 400,
        body: {
          status: 'failed',
          data: null,
          messages: ['Invalid operation or data format']
        }
      }, { status: 400 })
    }

    // Execute bulk import
    const result = await implBulkImportContacts(body, contactsBusinessLogic)

    return NextResponse.json({
      status: result.status,
      body: result.body
    })

  } catch (error) {
    console.error('Bulk import error:', error)
    return NextResponse.json({
      status: 500,
      body: {
        status: 'failed',
        data: null,
        messages: ['Internal server error during bulk import']
      }
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request format
    if (!implValidateBulkUpdateRequest(body)) {
      return NextResponse.json({
        status: 400,
        body: {
          status: 'failed',
          data: null,
          messages: ['Invalid operation or data format']
        }
      }, { status: 400 })
    }

    // Execute bulk update
    const result = await implBulkUpdateContacts(body, contactsBusinessLogic)

    return NextResponse.json({
      status: result.status,
      body: result.body
    })

  } catch (error) {
    console.error('Bulk update error:', error)
    return NextResponse.json({
      status: 500,
      body: {
        status: 'failed',
        data: null,
        messages: ['Internal server error during bulk update']
      }
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request format
    if (!implValidateBulkDeleteRequest(body)) {
      return NextResponse.json({
        status: 400,
        body: {
          status: 'failed',
          data: null,
          messages: ['Invalid operation or IDs format']
        }
      }, { status: 400 })
    }

    // Execute bulk delete
    const result = await implBulkDeleteContacts(body, contactsBusinessLogic)

    return NextResponse.json({
      status: result.status,
      body: result.body
    })

  } catch (error) {
    console.error('Bulk delete error:', error)
    return NextResponse.json({
      status: 500,
      body: {
        status: 'failed',
        data: null,
        messages: ['Internal server error during bulk delete']
      }
    }, { status: 500 })
  }
}
