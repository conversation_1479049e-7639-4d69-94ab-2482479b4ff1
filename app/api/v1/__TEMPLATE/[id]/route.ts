import { NextRequest, NextResponse } from "next/server";
import { TEMPLATE_CAMELCASEDsBusinessLogic } from "@/lib/repositories/businessLogics";
import { implHandleGetTEMPLATE_CAPITALIZED, implHandleUpdateTEMPLATE_CAPITALIZED, implHandleDeleteTEMPLATE_CAPITALIZED } from "../impl";
import { ERROR_CODES } from "@/app/api/error_codes";

export async function GET(
  _: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const result = await implHandleGetTEMPLATE_CAPITALIZED(id, TEMPLATE_CAMELCASEDsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("TEMPLATE_CAMELCASED GET route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const body = await req.json();
    const result = await implHandleUpdateTEMPLATE_CAPITALIZED(id, body, TEMPLATE_CAMELCASEDsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("TEMPLATE_CAMELCASED PUT route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const result = await implHandleDeleteTEMPLATE_CAPITALIZED(id, TEMPLATE_CAMELCASEDsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("TEMPLATE_CAMELCASED DELETE route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}
