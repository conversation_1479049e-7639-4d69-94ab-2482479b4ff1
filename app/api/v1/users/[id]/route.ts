import { NextRequest, NextResponse } from "next/server";
import { usersBusinessLogic } from "@/lib/repositories/businessLogics";
import { implHandleGetUser, implHandleUpdateUser, implHandleDeleteUser } from "../impl";
import { ERROR_CODES } from "@/app/api/error_codes";

export async function GET(
  _: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const result = await implHandleGetUser(id, usersBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("user GET route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const body = await req.json();
    const result = await implHandleUpdateUser(id, body, usersBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("user PUT route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const result = await implHandleDeleteUser(id, usersBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("user DELETE route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}
