import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { ERROR_CODES } from "@/app/api/error_codes";
import { loadSearchConfig, saveSearchConfig, getAvailableEntities } from "./configLoader";

// ✨ Generic search configuration interfaces
export interface SortOption {
  value: string
  label: string
  field: string // Backend field name
  type?: 'string' | 'number' | 'date' | 'boolean'
}

export interface DateFilterOption {
  value: string
  label: string
  description?: string
}

export interface FilterOption {
  id: string
  name: string
  field: string // Backend field name
  type: 'boolean' | 'select' | 'multiselect' | 'range'
  options?: Array<{ value: string; label: string }> // For select/multiselect filters
}

export interface SearchConfig {
  entity: string
  sortOptions: SortOption[]
  dateFilterOptions: DateFilterOption[]
  filters: FilterOption[]
  defaultSort?: {
    field: string
    direction: 'asc' | 'desc'
  }
  searchableFields: string[] // Fields that can be searched
}

// ✨ Configurations are now loaded from separate JSON files

// ✨ Get search configuration implementation
export async function implHandleGetSearchConfig(
  entity: string
): Promise<{
  status: number;
  body: ResponseWrapper<SearchConfig | null>;
}> {
  try {
    if (!entity || entity.trim() === '') {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          ["Entity parameter is required"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const config = await loadSearchConfig(entity.toLowerCase());

    if (!config) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          null,
          [`Search configuration not found for entity: ${entity}`],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", config),
    };
  } catch (error: any) {
    console.error("Get search config error:", error);

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to fetch search configuration. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

// ✨ Update search configuration implementation (for admin use)
export async function implHandleUpdateSearchConfig(
  entity: string,
  config: Omit<SearchConfig, 'entity'>
): Promise<{
  status: number;
  body: ResponseWrapper<SearchConfig | null>;
}> {
  try {
    if (!entity || entity.trim() === '') {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          ["Entity parameter is required"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    if (!config) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          ["Configuration data is required"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate required fields
    const requiredFields = ['sortOptions', 'dateFilterOptions', 'filters', 'searchableFields'];
    const missingFields = requiredFields.filter(field => !config[field as keyof typeof config]);

    if (missingFields.length > 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          [`Missing required fields: ${missingFields.join(', ')}`],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Create the updated configuration
    const updatedConfig: SearchConfig = {
      entity: entity.toLowerCase(),
      ...config
    };

    // Save the configuration to file
    const saveSuccess = await saveSearchConfig(entity.toLowerCase(), updatedConfig);

    if (!saveSuccess) {
      return {
        status: 500,
        body: new ResponseWrapper(
          "failed",
          null,
          ["Failed to save search configuration to file"],
          [ERROR_CODES.UPDATE_FAILED]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", updatedConfig),
    };
  } catch (error: any) {
    console.error("Update search config error:", error);

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to update search configuration. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// ✨ Get available entities implementation
export async function implHandleGetAvailableEntities(): Promise<{
  status: number;
  body: ResponseWrapper<string[]>;
}> {
  try {
    const entities = await getAvailableEntities();

    return {
      status: 200,
      body: new ResponseWrapper("success", entities),
    };
  } catch (error: any) {
    console.error("Get available entities error:", error);

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        [],
        ["Failed to fetch available entities. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}
