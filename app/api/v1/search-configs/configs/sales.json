{"entity": "sales", "sortOptions": [{"value": "amount", "label": "Amount", "field": "amount", "type": "number"}, {"value": "customer", "label": "Customer", "field": "customerName", "type": "string"}, {"value": "product", "label": "Product", "field": "productName", "type": "string"}, {"value": "status", "label": "Status", "field": "status", "type": "string"}, {"value": "createdAt", "label": "Sale Date", "field": "createdAt", "type": "date"}, {"value": "salesRep", "label": "Sales Rep", "field": "salesRep", "type": "string"}], "dateFilterOptions": [{"value": "today", "label": "Today", "description": "Sales from today"}, {"value": "yesterday", "label": "Yesterday", "description": "Sales from yesterday"}, {"value": "this_week", "label": "This Week", "description": "Sales from this week"}, {"value": "last_week", "label": "Last Week", "description": "Sales from last week"}, {"value": "this_month", "label": "This Month", "description": "Sales from this month"}, {"value": "last_month", "label": "Last Month", "description": "Sales from last month"}, {"value": "this_quarter", "label": "This Quarter", "description": "Sales from this quarter"}, {"value": "last_quarter", "label": "Last Quarter", "description": "Sales from last quarter"}, {"value": "this_year", "label": "This Year", "description": "Sales from this year"}, {"value": "last_year", "label": "Last Year", "description": "Sales from last year"}, {"value": "custom", "label": "Custom Range", "description": "Select custom date range"}, {"value": "all", "label": "All Time", "description": "All sales data"}], "filters": [{"id": "status", "name": "Order Status", "field": "status", "type": "select", "options": [{"value": "pending", "label": "Pending"}, {"value": "processing", "label": "Processing"}, {"value": "shipped", "label": "Shipped"}, {"value": "delivered", "label": "Delivered"}, {"value": "cancelled", "label": "Cancelled"}, {"value": "refunded", "label": "Refunded"}]}, {"id": "amount_range", "name": "Amount Range", "field": "amount", "type": "range"}, {"id": "sales_rep", "name": "Sales Representative", "field": "salesRep", "type": "select", "options": [{"value": "john_doe", "label": "<PERSON>"}, {"value": "jane_smith", "label": "<PERSON>"}, {"value": "mike_johnson", "label": "<PERSON>"}]}, {"id": "product_category", "name": "Product Category", "field": "productCategory", "type": "multiselect", "options": [{"value": "electronics", "label": "Electronics"}, {"value": "clothing", "label": "Clothing"}, {"value": "books", "label": "Books"}, {"value": "home", "label": "Home & Garden"}]}], "defaultSort": {"field": "createdAt", "direction": "desc"}, "searchableFields": ["customerName", "productName", "salesRep"]}