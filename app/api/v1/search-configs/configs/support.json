{"entity": "support", "sortOptions": [{"value": "priority", "label": "Priority", "field": "priority", "type": "string"}, {"value": "status", "label": "Status", "field": "status", "type": "string"}, {"value": "subject", "label": "Subject", "field": "subject", "type": "string"}, {"value": "assignedTo", "label": "Assigned To", "field": "assignedTo", "type": "string"}, {"value": "createdAt", "label": "Created Date", "field": "createdAt", "type": "date"}, {"value": "updatedAt", "label": "Last Updated", "field": "updatedAt", "type": "date"}], "dateFilterOptions": [{"value": "today", "label": "Today", "description": "Tickets created today"}, {"value": "yesterday", "label": "Yesterday", "description": "Tickets created yesterday"}, {"value": "this_week", "label": "This Week", "description": "Tickets created this week"}, {"value": "last_week", "label": "Last Week", "description": "Tickets created last week"}, {"value": "this_month", "label": "This Month", "description": "Tickets created this month"}, {"value": "last_month", "label": "Last Month", "description": "Tickets created last month"}, {"value": "all", "label": "All Time", "description": "All tickets"}], "filters": [{"id": "status", "name": "Ticket Status", "field": "status", "type": "select", "options": [{"value": "open", "label": "Open"}, {"value": "in_progress", "label": "In Progress"}, {"value": "waiting", "label": "Waiting for Customer"}, {"value": "resolved", "label": "Resolved"}, {"value": "closed", "label": "Closed"}]}, {"id": "priority", "name": "Priority", "field": "priority", "type": "select", "options": [{"value": "low", "label": "Low"}, {"value": "medium", "label": "Medium"}, {"value": "high", "label": "High"}, {"value": "urgent", "label": "<PERSON><PERSON>"}]}, {"id": "assigned_to", "name": "Assigned Agent", "field": "assignedTo", "type": "select", "options": [{"value": "agent1", "label": "Agent 1"}, {"value": "agent2", "label": "Agent 2"}, {"value": "agent3", "label": "Agent 3"}]}, {"id": "category", "name": "Category", "field": "category", "type": "multiselect", "options": [{"value": "technical", "label": "Technical Issue"}, {"value": "billing", "label": "Billing"}, {"value": "general", "label": "General Inquiry"}, {"value": "feature", "label": "Feature Request"}]}], "defaultSort": {"field": "createdAt", "direction": "desc"}, "searchableFields": ["subject", "description", "customerEmail"]}