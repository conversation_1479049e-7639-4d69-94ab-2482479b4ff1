{"entity": "contacts", "sortOptions": [{"value": "name", "label": "Name", "field": "name", "type": "string"}, {"value": "phone", "label": "Phone", "field": "phone", "type": "string"}, {"value": "email", "label": "Email", "field": "email", "type": "string"}, {"value": "createdAt", "label": "Created Date", "field": "createdAt", "type": "date"}, {"value": "updatedAt", "label": "Updated Date", "field": "updatedAt", "type": "date"}, {"value": "status", "label": "Status", "field": "status", "type": "string"}], "dateFilterOptions": [{"value": "today", "label": "Today", "description": "Items created today"}, {"value": "yesterday", "label": "Yesterday", "description": "Items created yesterday"}, {"value": "this_week", "label": "This Week", "description": "Items created this week"}, {"value": "last_week", "label": "Last Week", "description": "Items created last week"}, {"value": "this_month", "label": "This Month", "description": "Items created this month"}, {"value": "last_month", "label": "Last Month", "description": "Items created last month"}, {"value": "this_year", "label": "This Year", "description": "Items created this year"}, {"value": "last_year", "label": "Last Year", "description": "Items created last year"}, {"value": "custom", "label": "Custom Range", "description": "Select custom date range"}, {"value": "all", "label": "All Time", "description": "All available data"}], "filters": [{"id": "has_phone", "name": "Has Phone", "field": "phone", "type": "boolean"}, {"id": "has_email", "name": "<PERSON>", "field": "email", "type": "boolean"}, {"id": "has_tags", "name": "Has Tags", "field": "tags", "type": "boolean"}, {"id": "status", "name": "Status", "field": "status", "type": "select", "options": [{"value": "active", "label": "Active"}, {"value": "inactive", "label": "Inactive"}, {"value": "pending", "label": "Pending"}, {"value": "archived", "label": "Archived"}]}, {"id": "created_by", "name": "Created By", "field": "created<PERSON>y", "type": "select", "options": [{"value": "system", "label": "System"}, {"value": "admin", "label": "Admin"}, {"value": "user", "label": "User"}]}], "defaultSort": {"field": "createdAt", "direction": "desc"}, "searchableFields": ["name", "phone", "email", "tags"]}