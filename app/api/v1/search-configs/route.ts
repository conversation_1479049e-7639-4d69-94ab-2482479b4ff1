import { NextRequest, NextResponse } from 'next/server'

// ✨ Generic search configuration interfaces
export interface SortOption {
  value: string
  label: string
  field: string // Backend field name
  type?: 'string' | 'number' | 'date' | 'boolean'
}

export interface DateFilterOption {
  value: string
  label: string
  description?: string
}

export interface FilterOption {
  id: string
  name: string
  field: string // Backend field name
  type: 'boolean' | 'select' | 'multiselect' | 'range'
  options?: Array<{ value: string; label: string }> // For select/multiselect filters
}

export interface SearchConfig {
  entity: string
  sortOptions: SortOption[]
  dateFilterOptions: DateFilterOption[]
  filters: FilterOption[]
  defaultSort?: {
    field: string
    direction: 'asc' | 'desc'
  }
  searchableFields: string[] // Fields that can be searched
}

// ✨ Configuration for different entities
const searchConfigs: Record<string, SearchConfig> = {
  contacts: {
    entity: 'contacts',
    sortOptions: [
      { value: 'name', label: 'Name', field: 'name', type: 'string' },
      { value: 'phone', label: 'Phone', field: 'phone', type: 'string' },
      { value: 'email', label: 'Email', field: 'email', type: 'string' },
      { value: 'createdAt', label: 'Created Date', field: 'createdAt', type: 'date' },
      { value: 'updatedAt', label: 'Updated Date', field: 'updatedAt', type: 'date' },
      { value: 'status', label: 'Status', field: 'status', type: 'string' }
    ],
    dateFilterOptions: [
      { value: 'today', label: 'Today', description: 'Items created today' },
      { value: 'yesterday', label: 'Yesterday', description: 'Items created yesterday' },
      { value: 'this_week', label: 'This Week', description: 'Items created this week' },
      { value: 'last_week', label: 'Last Week', description: 'Items created last week' },
      { value: 'this_month', label: 'This Month', description: 'Items created this month' },
      { value: 'last_month', label: 'Last Month', description: 'Items created last month' },
      { value: 'this_year', label: 'This Year', description: 'Items created this year' },
      { value: 'last_year', label: 'Last Year', description: 'Items created last year' },
      { value: 'custom', label: 'Custom Range', description: 'Select custom date range' },
      { value: 'all', label: 'All Time', description: 'All available data' }
    ],
    filters: [
      {
        id: 'has_phone',
        name: 'Has Phone',
        field: 'phone',
        type: 'boolean'
      },
      {
        id: 'has_email',
        name: 'Has Email',
        field: 'email',
        type: 'boolean'
      },
      {
        id: 'has_tags',
        name: 'Has Tags',
        field: 'tags',
        type: 'boolean'
      },
      {
        id: 'status',
        name: 'Status',
        field: 'status',
        type: 'select',
        options: [
          { value: 'active', label: 'Active' },
          { value: 'inactive', label: 'Inactive' },
          { value: 'pending', label: 'Pending' },
          { value: 'archived', label: 'Archived' }
        ]
      },
      {
        id: 'created_by',
        name: 'Created By',
        field: 'createdBy',
        type: 'select',
        options: [
          { value: 'system', label: 'System' },
          { value: 'admin', label: 'Admin' },
          { value: 'user', label: 'User' }
        ]
      }
    ],
    defaultSort: {
      field: 'createdAt',
      direction: 'desc'
    },
    searchableFields: ['name', 'phone', 'email', 'tags']
  },

  sales: {
    entity: 'sales',
    sortOptions: [
      { value: 'amount', label: 'Amount', field: 'amount', type: 'number' },
      { value: 'customer', label: 'Customer', field: 'customerName', type: 'string' },
      { value: 'product', label: 'Product', field: 'productName', type: 'string' },
      { value: 'status', label: 'Status', field: 'status', type: 'string' },
      { value: 'createdAt', label: 'Sale Date', field: 'createdAt', type: 'date' },
      { value: 'salesRep', label: 'Sales Rep', field: 'salesRep', type: 'string' }
    ],
    dateFilterOptions: [
      { value: 'today', label: 'Today', description: 'Sales from today' },
      { value: 'yesterday', label: 'Yesterday', description: 'Sales from yesterday' },
      { value: 'this_week', label: 'This Week', description: 'Sales from this week' },
      { value: 'last_week', label: 'Last Week', description: 'Sales from last week' },
      { value: 'this_month', label: 'This Month', description: 'Sales from this month' },
      { value: 'last_month', label: 'Last Month', description: 'Sales from last month' },
      { value: 'this_quarter', label: 'This Quarter', description: 'Sales from this quarter' },
      { value: 'last_quarter', label: 'Last Quarter', description: 'Sales from last quarter' },
      { value: 'this_year', label: 'This Year', description: 'Sales from this year' },
      { value: 'last_year', label: 'Last Year', description: 'Sales from last year' },
      { value: 'custom', label: 'Custom Range', description: 'Select custom date range' },
      { value: 'all', label: 'All Time', description: 'All sales data' }
    ],
    filters: [
      {
        id: 'status',
        name: 'Order Status',
        field: 'status',
        type: 'select',
        options: [
          { value: 'pending', label: 'Pending' },
          { value: 'processing', label: 'Processing' },
          { value: 'shipped', label: 'Shipped' },
          { value: 'delivered', label: 'Delivered' },
          { value: 'cancelled', label: 'Cancelled' },
          { value: 'refunded', label: 'Refunded' }
        ]
      },
      {
        id: 'amount_range',
        name: 'Amount Range',
        field: 'amount',
        type: 'range'
      },
      {
        id: 'sales_rep',
        name: 'Sales Representative',
        field: 'salesRep',
        type: 'select',
        options: [
          { value: 'john_doe', label: 'John Doe' },
          { value: 'jane_smith', label: 'Jane Smith' },
          { value: 'mike_johnson', label: 'Mike Johnson' }
        ]
      },
      {
        id: 'product_category',
        name: 'Product Category',
        field: 'productCategory',
        type: 'multiselect',
        options: [
          { value: 'electronics', label: 'Electronics' },
          { value: 'clothing', label: 'Clothing' },
          { value: 'books', label: 'Books' },
          { value: 'home', label: 'Home & Garden' }
        ]
      }
    ],
    defaultSort: {
      field: 'createdAt',
      direction: 'desc'
    },
    searchableFields: ['customerName', 'productName', 'salesRep']
  },

  support: {
    entity: 'support',
    sortOptions: [
      { value: 'priority', label: 'Priority', field: 'priority', type: 'string' },
      { value: 'status', label: 'Status', field: 'status', type: 'string' },
      { value: 'subject', label: 'Subject', field: 'subject', type: 'string' },
      { value: 'assignedTo', label: 'Assigned To', field: 'assignedTo', type: 'string' },
      { value: 'createdAt', label: 'Created Date', field: 'createdAt', type: 'date' },
      { value: 'updatedAt', label: 'Last Updated', field: 'updatedAt', type: 'date' }
    ],
    dateFilterOptions: [
      { value: 'today', label: 'Today', description: 'Tickets created today' },
      { value: 'yesterday', label: 'Yesterday', description: 'Tickets created yesterday' },
      { value: 'this_week', label: 'This Week', description: 'Tickets created this week' },
      { value: 'last_week', label: 'Last Week', description: 'Tickets created last week' },
      { value: 'this_month', label: 'This Month', description: 'Tickets created this month' },
      { value: 'last_month', label: 'Last Month', description: 'Tickets created last month' },
      { value: 'all', label: 'All Time', description: 'All tickets' }
    ],
    filters: [
      {
        id: 'status',
        name: 'Ticket Status',
        field: 'status',
        type: 'select',
        options: [
          { value: 'open', label: 'Open' },
          { value: 'in_progress', label: 'In Progress' },
          { value: 'waiting', label: 'Waiting for Customer' },
          { value: 'resolved', label: 'Resolved' },
          { value: 'closed', label: 'Closed' }
        ]
      },
      {
        id: 'priority',
        name: 'Priority',
        field: 'priority',
        type: 'select',
        options: [
          { value: 'low', label: 'Low' },
          { value: 'medium', label: 'Medium' },
          { value: 'high', label: 'High' },
          { value: 'urgent', label: 'Urgent' }
        ]
      },
      {
        id: 'assigned_to',
        name: 'Assigned Agent',
        field: 'assignedTo',
        type: 'select',
        options: [
          { value: 'agent1', label: 'Agent 1' },
          { value: 'agent2', label: 'Agent 2' },
          { value: 'agent3', label: 'Agent 3' }
        ]
      },
      {
        id: 'category',
        name: 'Category',
        field: 'category',
        type: 'multiselect',
        options: [
          { value: 'technical', label: 'Technical Issue' },
          { value: 'billing', label: 'Billing' },
          { value: 'general', label: 'General Inquiry' },
          { value: 'feature', label: 'Feature Request' }
        ]
      }
    ],
    defaultSort: {
      field: 'createdAt',
      direction: 'desc'
    },
    searchableFields: ['subject', 'description', 'customerEmail']
  }
}

// ✨ GET endpoint to retrieve search configuration
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const entity = searchParams.get('entity')

    if (!entity) {
      return NextResponse.json(
        { 
          status: 'error', 
          message: 'Entity parameter is required',
          data: null 
        },
        { status: 400 }
      )
    }

    const config = searchConfigs[entity.toLowerCase()]

    if (!config) {
      return NextResponse.json(
        { 
          status: 'error', 
          message: `Search configuration not found for entity: ${entity}`,
          data: null,
          availableEntities: Object.keys(searchConfigs)
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      status: 'success',
      data: config,
      message: `Search configuration retrieved for ${entity}`
    })

  } catch (error) {
    console.error('Error fetching search config:', error)
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'Internal server error',
        data: null 
      },
      { status: 500 }
    )
  }
}

// ✨ POST endpoint to update search configuration (for admin use)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { entity, config } = body

    if (!entity || !config) {
      return NextResponse.json(
        { 
          status: 'error', 
          message: 'Entity and config are required',
          data: null 
        },
        { status: 400 }
      )
    }

    // In a real implementation, you would save this to a database
    // For now, we'll just validate the structure
    const requiredFields = ['sortOptions', 'dateFilterOptions', 'filters', 'searchableFields']
    const missingFields = requiredFields.filter(field => !config[field])

    if (missingFields.length > 0) {
      return NextResponse.json(
        { 
          status: 'error', 
          message: `Missing required fields: ${missingFields.join(', ')}`,
          data: null 
        },
        { status: 400 }
      )
    }

    // Update the configuration (in memory for this example)
    searchConfigs[entity.toLowerCase()] = {
      entity: entity.toLowerCase(),
      ...config
    }

    return NextResponse.json({
      status: 'success',
      data: searchConfigs[entity.toLowerCase()],
      message: `Search configuration updated for ${entity}`
    })

  } catch (error) {
    console.error('Error updating search config:', error)
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'Internal server error',
        data: null 
      },
      { status: 500 }
    )
  }
}
