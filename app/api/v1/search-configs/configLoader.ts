import { promises as fs } from 'fs'
import path from 'path'
import { SearchConfig } from './impl'

// ✨ Configuration cache to avoid repeated file reads
const configCache = new Map<string, SearchConfig>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
const cacheTimestamps = new Map<string, number>()

// ✨ Get the configs directory path
const getConfigsDir = () => {
  return path.join(process.cwd(), 'app/api/v1/search-configs/configs')
}

// ✨ Load configuration from JSON file
export async function loadSearchConfig(entity: string): Promise<SearchConfig | null> {
  try {
    const now = Date.now()
    const cacheKey = entity.toLowerCase()
    
    // Check if we have a valid cached version
    const cachedConfig = configCache.get(cacheKey)
    const cacheTimestamp = cacheTimestamps.get(cacheKey)
    
    if (cachedConfig && cacheTimestamp && (now - cacheTimestamp) < CACHE_DURATION) {
      return cachedConfig
    }

    // Load from file
    const configsDir = getConfigsDir()
    const configPath = path.join(configsDir, `${entity.toLowerCase()}.json`)
    
    // Check if file exists
    try {
      await fs.access(configPath)
    } catch {
      console.warn(`Search config file not found: ${configPath}`)
      return null
    }

    // Read and parse the configuration file
    const configContent = await fs.readFile(configPath, 'utf-8')
    const config: SearchConfig = JSON.parse(configContent)
    
    // Validate the configuration structure
    if (!isValidSearchConfig(config)) {
      console.error(`Invalid search config structure in file: ${configPath}`)
      return null
    }

    // Cache the configuration
    configCache.set(cacheKey, config)
    cacheTimestamps.set(cacheKey, now)
    
    return config
  } catch (error) {
    console.error(`Error loading search config for entity ${entity}:`, error)
    return null
  }
}

// ✨ Save configuration to JSON file
export async function saveSearchConfig(entity: string, config: SearchConfig): Promise<boolean> {
  try {
    // Validate the configuration structure
    if (!isValidSearchConfig(config)) {
      console.error('Invalid search config structure provided for saving')
      return false
    }

    const configsDir = getConfigsDir()
    const configPath = path.join(configsDir, `${entity.toLowerCase()}.json`)
    
    // Ensure the configs directory exists
    await fs.mkdir(configsDir, { recursive: true })
    
    // Write the configuration file
    const configContent = JSON.stringify(config, null, 2)
    await fs.writeFile(configPath, configContent, 'utf-8')
    
    // Update cache
    const cacheKey = entity.toLowerCase()
    configCache.set(cacheKey, config)
    cacheTimestamps.set(cacheKey, Date.now())
    
    console.log(`Search config saved successfully for entity: ${entity}`)
    return true
  } catch (error) {
    console.error(`Error saving search config for entity ${entity}:`, error)
    return false
  }
}

// ✨ Get all available entity configurations
export async function getAvailableEntities(): Promise<string[]> {
  try {
    const configsDir = getConfigsDir()
    
    // Check if configs directory exists
    try {
      await fs.access(configsDir)
    } catch {
      console.warn(`Configs directory not found: ${configsDir}`)
      return []
    }

    // Read all JSON files in the configs directory
    const files = await fs.readdir(configsDir)
    const entities = files
      .filter(file => file.endsWith('.json'))
      .map(file => file.replace('.json', ''))
    
    return entities
  } catch (error) {
    console.error('Error getting available entities:', error)
    return []
  }
}

// ✨ Validate search configuration structure
function isValidSearchConfig(config: any): config is SearchConfig {
  if (!config || typeof config !== 'object') {
    return false
  }

  // Check required fields
  const requiredFields = ['entity', 'sortOptions', 'dateFilterOptions', 'filters', 'searchableFields']
  for (const field of requiredFields) {
    if (!(field in config)) {
      console.error(`Missing required field: ${field}`)
      return false
    }
  }

  // Validate arrays
  if (!Array.isArray(config.sortOptions)) {
    console.error('sortOptions must be an array')
    return false
  }

  if (!Array.isArray(config.dateFilterOptions)) {
    console.error('dateFilterOptions must be an array')
    return false
  }

  if (!Array.isArray(config.filters)) {
    console.error('filters must be an array')
    return false
  }

  if (!Array.isArray(config.searchableFields)) {
    console.error('searchableFields must be an array')
    return false
  }

  // Validate sort options structure
  for (const sortOption of config.sortOptions) {
    if (!sortOption.value || !sortOption.label || !sortOption.field) {
      console.error('Invalid sort option structure - missing value, label, or field')
      return false
    }
  }

  // Validate date filter options structure
  for (const dateOption of config.dateFilterOptions) {
    if (!dateOption.value || !dateOption.label) {
      console.error('Invalid date filter option structure - missing value or label')
      return false
    }
  }

  // Validate filters structure
  for (const filter of config.filters) {
    if (!filter.id || !filter.name || !filter.field || !filter.type) {
      console.error('Invalid filter structure - missing id, name, field, or type')
      return false
    }

    // Validate filter types
    const validTypes = ['boolean', 'select', 'multiselect', 'range']
    if (!validTypes.includes(filter.type)) {
      console.error(`Invalid filter type: ${filter.type}`)
      return false
    }

    // Validate select/multiselect options
    if ((filter.type === 'select' || filter.type === 'multiselect') && !Array.isArray(filter.options)) {
      console.error('Select/multiselect filters must have options array')
      return false
    }
  }

  return true
}

// ✨ Clear configuration cache
export function clearConfigCache(entity?: string): void {
  if (entity) {
    const cacheKey = entity.toLowerCase()
    configCache.delete(cacheKey)
    cacheTimestamps.delete(cacheKey)
  } else {
    configCache.clear()
    cacheTimestamps.clear()
  }
}

// ✨ Get cache statistics (for debugging)
export function getCacheStats(): {
  totalCached: number
  entities: string[]
  oldestCache?: { entity: string; age: number }
} {
  const entities = Array.from(configCache.keys())
  const now = Date.now()
  
  let oldestCache: { entity: string; age: number } | undefined
  
  for (const entity of entities) {
    const timestamp = cacheTimestamps.get(entity)
    if (timestamp) {
      const age = now - timestamp
      if (!oldestCache || age > oldestCache.age) {
        oldestCache = { entity, age }
      }
    }
  }
  
  return {
    totalCached: entities.length,
    entities,
    oldestCache
  }
}
