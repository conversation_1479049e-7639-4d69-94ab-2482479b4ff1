import { NextRequest, NextResponse } from "next/server";
import { invitationsBusinessLogic } from "@/lib/repositories/businessLogics";
import { implHandleGetInvitation, implHandleUpdateInvitation, implHandleDeleteInvitation } from "../impl";
import { ERROR_CODES } from "@/app/api/error_codes";

export async function GET(
  _: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const result = await implHandleGetInvitation(id, invitationsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("invitation GET route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const body = await req.json();
    const result = await implHandleUpdateInvitation(id, body, invitationsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("invitation PUT route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    const result = await implHandleDeleteInvitation(id, invitationsBusinessLogic);
    return NextResponse.json(result.body, { status: result.status });
  } catch (error) {
    console.error("invitation DELETE route error:", error);
    return NextResponse.json(
      {
        status: "failed",
        data: null,
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    );
  }
}
