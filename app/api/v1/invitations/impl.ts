import { InvitationBusinessLogicInterface, Invitation, InvitationCreateInput, InvitationUpdateInput } from "@/lib/repositories/invitations/interface";
import { ResponseWrapper } from "@/lib/types/responseWrapper";
import { InvitationCreateSchema, InvitationUpdateSchema } from "@/lib/validations/invitation";
import { ERROR_CODES } from "@/app/api/error_codes";

// Create Invitation Implementation
export async function implHandleCreateInvitation(
  data: any,
  businessLogic: InvitationBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate input data
    const validationResult = InvitationCreateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const invitation = await businessLogic.create(validationResult.data);

    return {
      status: 201,
      body: new ResponseWrapper("success", invitation),
    };
  } catch (error: any) {
    console.error("Create invitation error:", error);

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_EMAIL") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to create invitation. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Update Invitation Implementation
export async function implHandleUpdateInvitation(
  id: string,
  data: any,
  businessLogic: InvitationBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Check for empty update object
    if (!data || Object.keys(data).length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["No data provided for update"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate input data
    const validationResult = InvitationUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      );

      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          errors,
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const invitation = await businessLogic.update(id, validationResult.data);

    if (!invitation) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Invitation not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", invitation),
    };
  } catch (error: any) {
    console.error("Update invitation error:", error);

    if (error.code === "DUPLICATE_PHONE" || error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to update invitation. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Delete Invitation Implementation
export async function implHandleDeleteInvitation(
  id: string,
  businessLogic: InvitationBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const success = await businessLogic.delete(id, hardDelete);

    if (!success) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Invitation not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { message: "Invitation deleted successfully" }),
    };
  } catch (error: any) {
    console.error("Delete invitation error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to delete invitation. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Get Invitation by ID Implementation
export async function implHandleGetInvitation(
  id: string,
  businessLogic: InvitationBusinessLogicInterface,
  includeDeleted: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    const invitation = await businessLogic.getById(id, includeDeleted);

    if (!invitation) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Invitation not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", invitation),
    };
  } catch (error: any) {
    console.error("Get invitation error:", error);

    if (error.code === "INVALID_ID") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to fetch invitation. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

interface GetAllResultPaginated<T> {
  items: T[];
  page: number;
  total: number;
}

// Get All Invitation Implementation (handles all, search, and tag filtering)
export async function implHandleGetAllInvitations(
  businessLogic: InvitationBusinessLogicInterface,
  params?: {
    search?: string;
    includeDeleted?: boolean;
    page?: number;
    limit?: number;
    sorts?: {
      field: keyof Invitation | string;
      direction: "asc" | "desc";
    }[],
    filters?: {
      field: keyof Invitation | string;
      value: Invitation[keyof Invitation] | any;
    }[];
  }
): Promise<{
  status: number;
  body: ResponseWrapper<GetAllResultPaginated<Invitation>>;
}> {
  try {
    // Validate search parameter if provided
    if (params?.search !== undefined) {
      if (!params.search || params.search.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper<GetAllResultPaginated<Invitation>>(
            "failed",
            undefined,
            ["Search keyword cannot be empty"],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    // Validate filters if provided
    if (params?.filters && params.filters.length > 0) {
      for (const filter of params.filters) {
        if (!filter.field || filter.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Invitation>>(
              "failed",
              undefined,
              ["Filter field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Validate sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      for (const sort of params.sorts) {
        if (!sort.field || sort.field.trim() === '') {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Invitation>>(
              "failed",
              undefined,
              ["Sort field cannot be empty"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
        if (!['asc', 'desc'].includes(sort.direction)) {
          return {
            status: 400,
            body: new ResponseWrapper<GetAllResultPaginated<Invitation>>(
              "failed",
              undefined,
              ["Sort direction must be 'asc' or 'desc'"],
              [ERROR_CODES.VALIDATION_FAILED]
            ),
          };
        }
      }
    }

    // Build query parameters for the repository
    const queryParams: any = {
      includeDeleted: params?.includeDeleted,
      page: params?.page,
      limit: params?.limit,
    };

    // Add search if provided
    if (params?.search) {
      queryParams.search = params.search.trim();
    }

    // Add filters if provided
    if (params?.filters && params.filters.length > 0) {
      queryParams.filters = params.filters;
    }

    // Add sorts if provided
    if (params?.sorts && params.sorts.length > 0) {
      queryParams.sorts = params.sorts;
    }

    const result = await businessLogic.getAll(queryParams);

    return {
      status: 200,
      body: new ResponseWrapper<GetAllResultPaginated<Invitation>>("success", { ...result, page: queryParams.page }),
    };
  } catch (error: any) {
    console.error("Get invitation error:", error);

    return {
      status: 500,
      body: new ResponseWrapper<GetAllResultPaginated<Invitation>>(
        "failed",
        undefined,
        ["Failed to fetch invitation. Please try again."],
        [ERROR_CODES.FETCH_FAILED]
      ),
    };
  }
}

// Bulk Create Invitation Implementation
export async function implHandleBulkCreateInvitation(
  data: any[],
  businessLogic: InvitationBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that data is an array
    if (!Array.isArray(data) || data.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Input must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each item in the array
    const validatedData: InvitationCreateInput[] = [];
    for (let i = 0; i < data.length; i++) {
      const validationResult = InvitationCreateSchema.safeParse(data[i]);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `Item ${i}: ${err.path.join('.')}: ${err.message}`
        );
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            errors,
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
      validatedData.push(validationResult.data);
    }

    const invitation = await businessLogic.bulkCreate(validatedData);

    return {
      status: 201,
      body: new ResponseWrapper("success", invitation),
    };
  } catch (error: any) {
    console.error("Bulk create invitation error:", error);

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk create invitation. Please try again."],
        [ERROR_CODES.CREATE_FAILED]
      ),
    };
  }
}

// Bulk Update Invitation Implementation
export async function implHandleBulkUpdateInvitation(
  updates: { id: string; data: any }[],
  businessLogic: InvitationBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that updates is an array
    if (!Array.isArray(updates) || updates.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Updates must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each update in the array
    const validatedUpdates: { id: string; data: InvitationUpdateInput }[] = [];
    for (let i = 0; i < updates.length; i++) {
      const update = updates[i];

      if (!update.id || typeof update.id !== 'string') {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`Update ${i}: ID is required and must be a string`],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }

      const validationResult = InvitationUpdateSchema.safeParse(update.data);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err =>
          `Update ${i}: ${err.path.join('.')}: ${err.message}`
        );
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            errors,
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }

      validatedUpdates.push({
        id: update.id,
        data: validationResult.data
      });
    }

    const updatedCount = await businessLogic.bulkUpdate(validatedUpdates);

    return {
      status: 200,
      body: new ResponseWrapper("success", { updatedCount }),
    };
  } catch (error: any) {
    console.error("Bulk update invitation error:", error);

    if (error.code === "DUPLICATE_NAME") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk update invitation. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}

// Bulk Delete Invitation Implementation
export async function implHandleBulkDeleteInvitation(
  ids: string[],
  businessLogic: InvitationBusinessLogicInterface,
  hardDelete: boolean = false
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate that ids is an array
    if (!Array.isArray(ids) || ids.length === 0) {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["IDs must be a non-empty array"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    // Validate each ID
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i];
      if (!id || typeof id !== 'string' || id.trim() === '') {
        return {
          status: 400,
          body: new ResponseWrapper(
            "failed",
            undefined,
            [`ID at index ${i} is required`],
            [ERROR_CODES.VALIDATION_FAILED]
          ),
        };
      }
    }

    const deletedCount = await businessLogic.bulkDelete(ids, hardDelete);

    return {
      status: 200,
      body: new ResponseWrapper("success", { deletedCount }),
    };
  } catch (error: any) {
    console.error("Bulk delete invitation error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to bulk delete invitation. Please try again."],
        [ERROR_CODES.DELETE_FAILED]
      ),
    };
  }
}

// Restore Invitation Implementation
export async function implHandleRestoreInvitation(
  id: string,
  businessLogic: InvitationBusinessLogicInterface
): Promise<{
  status: number;
  body: ResponseWrapper<any>;
}> {
  try {
    // Validate ID
    if (!id || typeof id !== 'string' || id.trim() === '') {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Invitation ID is required"],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      };
    }

    const restored = await businessLogic.restore(id);

    if (!restored) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          ["Invitation not found or cannot be restored"],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", { restored: true }),
    };
  } catch (error: any) {
    console.error("Restore invitation error:", error);

    if (error.code === "NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          undefined,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      };
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        undefined,
        ["Failed to restore invitation. Please try again."],
        [ERROR_CODES.UPDATE_FAILED]
      ),
    };
  }
}