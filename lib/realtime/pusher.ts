// lib/realtime/pusher.ts
import Pusher from "pusher";
import <PERSON>usher<PERSON>s from 'pusher-js';
import { 
  RealtimeInterface, 
  RealtimeServer, 
  RealtimeClient, 
  RealtimeChannel, 
  RealtimeConnection 
} from './interface';

// Pusher implementation
class PusherChannel implements RealtimeChannel {
  constructor(private channel: any) {}

  bind(event: string, callback: (data: any) => void): void {
    this.channel.bind(event, callback);
  }

  unbind_all(): void {
    this.channel.unbind_all();
  }

  unsubscribe(): void {
    this.channel.unsubscribe();
  }
}

class PusherConnection implements RealtimeConnection {
  constructor(private connection: any) {}

  bind(event: string, callback: () => void): void {
    this.connection.bind(event, callback);
  }

  get socket_id(): string | null {
    return this.connection.socket_id;
  }
}

class PusherClientImpl implements RealtimeClient {
  constructor(private pusherClient: PusherJs) {}

  subscribe(channel: string): RealtimeChannel {
    const pusherChannel = this.pusherClient.subscribe(channel);
    return new PusherChannel(pusherChannel);
  }

  get connection(): RealtimeConnection {
    return new PusherConnection(this.pusherClient.connection);
  }

  disconnect(): void {
    this.pusherClient.disconnect();
  }
}

class PusherServerImpl implements RealtimeServer {
  constructor(private pusher: Pusher) {}

  async trigger(channel: string, event: string, data: any): Promise<void> {
    await this.pusher.trigger(channel, event, data);
  }
}

class PusherImplementation implements RealtimeInterface {
  public server: RealtimeServer;
  public client: RealtimeClient;

  constructor() {
    const pusherServer = new Pusher({
      appId: process.env.PUSHER_APP_ID!,
      key: process.env.PUSHER_KEY!,
      secret: process.env.PUSHER_SECRET!,
      cluster: process.env.PUSHER_CLUSTER!,
      useTLS: true,
    });

    const pusherClient = new PusherJs(process.env.PUSHER_KEY!, {
      cluster: process.env.PUSHER_CLUSTER!
    });

    this.server = new PusherServerImpl(pusherServer);
    this.client = new PusherClientImpl(pusherClient);
  }
}

// Export the Pusher implementation
export const createPusherRealtime = (): RealtimeInterface => {
  return new PusherImplementation();
};
