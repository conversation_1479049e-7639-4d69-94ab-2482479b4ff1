// lib/realtime/websocket.ts
import { 
  RealtimeInterface, 
  RealtimeServer, 
  RealtimeClient, 
  RealtimeChannel, 
  RealtimeConnection 
} from './interface';

// WebSocket implementation
class WebSocketChannel implements RealtimeChannel {
  private eventListeners: Map<string, ((data: any) => void)[]> = new Map();
  private isSubscribed = true;

  constructor(
    private channelName: string,
    private websocket: WebSocket,
    private onUnsubscribe: (channel: string) => void
  ) {}

  bind(event: string, callback: (data: any) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  unbind_all(): void {
    this.eventListeners.clear();
  }

  unsubscribe(): void {
    if (this.isSubscribed) {
      this.isSubscribed = false;
      this.eventListeners.clear();
      this.onUnsubscribe(this.channelName);
      
      // Send unsubscribe message to server
      if (this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify({
          type: 'unsubscribe',
          channel: this.channelName
        }));
      }
    }
  }

  // Internal method to handle incoming messages
  _handleMessage(event: string, data: any): void {
    if (this.isSubscribed && this.eventListeners.has(event)) {
      const callbacks = this.eventListeners.get(event)!;
      callbacks.forEach(callback => callback(data));
    }
  }
}

class WebSocketConnection implements RealtimeConnection {
  private connectionListeners: Map<string, (() => void)[]> = new Map();
  public socket_id: string | null = null;

  constructor(private websocket: WebSocket) {
    this.setupConnectionListeners();
  }

  private setupConnectionListeners(): void {
    this.websocket.addEventListener('open', () => {
      this.socket_id = this.generateSocketId();
      this._triggerEvent('connected');
    });

    this.websocket.addEventListener('close', () => {
      this.socket_id = null;
      this._triggerEvent('disconnected');
    });

    this.websocket.addEventListener('error', () => {
      this._triggerEvent('error');
    });
  }

  private generateSocketId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  bind(event: string, callback: () => void): void {
    if (!this.connectionListeners.has(event)) {
      this.connectionListeners.set(event, []);
    }
    this.connectionListeners.get(event)!.push(callback);
  }

  private _triggerEvent(event: string): void {
    if (this.connectionListeners.has(event)) {
      const callbacks = this.connectionListeners.get(event)!;
      callbacks.forEach(callback => callback());
    }
  }
}

class WebSocketClientImpl implements RealtimeClient {
  private websocket: WebSocket;
  private channels: Map<string, WebSocketChannel> = new Map();
  private _connection: WebSocketConnection;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor(private url: string) {
    this.websocket = this.createWebSocket();
    this._connection = new WebSocketConnection(this.websocket);
    this.setupMessageHandler();
  }

  private createWebSocket(): WebSocket {
    const ws = new WebSocket(this.url);
    
    ws.addEventListener('close', () => {
      this.handleReconnect();
    });

    return ws;
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.websocket = this.createWebSocket();
        this._connection = new WebSocketConnection(this.websocket);
        this.setupMessageHandler();
        this.resubscribeChannels();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  private resubscribeChannels(): void {
    // Wait for connection to be established
    this.websocket.addEventListener('open', () => {
      this.channels.forEach((_, channelName) => {
        this.websocket.send(JSON.stringify({
          type: 'subscribe',
          channel: channelName
        }));
      });
      this.reconnectAttempts = 0; // Reset on successful connection
    });
  }

  private setupMessageHandler(): void {
    this.websocket.addEventListener('message', (event) => {
      try {
        const message = JSON.parse(event.data);
        
        if (message.type === 'event' && message.channel && message.event) {
          const channel = this.channels.get(message.channel);
          if (channel) {
            channel._handleMessage(message.event, message.data);
          }
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    });
  }

  subscribe(channelName: string): RealtimeChannel {
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }

    const channel = new WebSocketChannel(
      channelName,
      this.websocket,
      (name) => this.channels.delete(name)
    );

    this.channels.set(channelName, channel);

    // Send subscribe message to server
    if (this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'subscribe',
        channel: channelName
      }));
    } else {
      // Queue subscription for when connection is ready
      this.websocket.addEventListener('open', () => {
        this.websocket.send(JSON.stringify({
          type: 'subscribe',
          channel: channelName
        }));
      });
    }

    return channel;
  }

  get connection(): RealtimeConnection {
    return this._connection;
  }

  disconnect(): void {
    this.channels.forEach(channel => channel.unsubscribe());
    this.channels.clear();
    this.websocket.close();
  }
}

class WebSocketServerImpl implements RealtimeServer {
  constructor(private apiEndpoint: string) {}

  async trigger(channel: string, event: string, data: any): Promise<void> {
    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'trigger',
          channel,
          event,
          data
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to trigger event: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error triggering WebSocket event:', error);
      throw error;
    }
  }
}

class WebSocketImplementation implements RealtimeInterface {
  public server: RealtimeServer;
  public client: RealtimeClient;

  constructor(options: {
    clientUrl: string;
    serverApiEndpoint: string;
  }) {
    this.server = new WebSocketServerImpl(options.serverApiEndpoint);
    this.client = new WebSocketClientImpl(options.clientUrl);
  }
}

// Export the WebSocket implementation
export const createWebSocketRealtime = (options: {
  clientUrl: string;
  serverApiEndpoint: string;
}): RealtimeInterface => {
  return new WebSocketImplementation(options);
};
