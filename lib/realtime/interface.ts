// lib/realtime/interface.ts

// Types for realtime interface
export interface RealtimeChannel {
  bind(event: string, callback: (data: any) => void): void;
  unbind_all(): void;
  unsubscribe(): void;
}

export interface RealtimeConnection {
  bind(event: string, callback: () => void): void;
  socket_id: string | null;
}

export interface RealtimeClient {
  subscribe(channel: string): RealtimeChannel;
  connection: RealtimeConnection;
  disconnect(): void;
}

export interface RealtimeServer {
  trigger(channel: string, event: string, data: any): Promise<void>;
}

export interface RealtimeInterface {
  server: RealtimeServer;
  client: RealtimeClient;
}
