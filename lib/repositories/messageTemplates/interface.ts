export interface MessageTemplate {
  id: string;
  title: string;
  content: string;
  category?: string;
  tags?: string[];
  variables?: string[]; // Template variables like {name}, {company}, etc.
  isActive?: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy?: string;
}

export interface MessageTemplateCreateInput {
  title: string;
  content: string;
  category?: string;
  tags?: string[];
  variables?: string[];
  isActive?: boolean;
  createdBy: string;
}

export interface MessageTemplateUpdateInput {
  title?: string;
  content?: string;
  category?: string;
  tags?: string[];
  variables?: string[];
  isActive?: boolean;
  updatedBy?: string;
}

export interface MessageTemplateQueryParams {
  search?: string;
  filters?: { field: keyof MessageTemplate | string; value: any }[];
  sorts?: { field: keyof MessageTemplate | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface MessageTemplateBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<MessageTemplate | null>;
  getAll(params?: MessageTemplateQueryParams): Promise<{
    items: MessageTemplate[];
    total: number;
  }>;
  create(data: MessageTemplateCreateInput): Promise<MessageTemplate>;
  update(id: string, data: MessageTemplateUpdateInput): Promise<MessageTemplate | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: MessageTemplateCreateInput[]): Promise<MessageTemplate[]>;
  bulkUpdate(updates: { id: string; data: MessageTemplateUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
