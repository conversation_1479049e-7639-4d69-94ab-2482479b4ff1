import {
  MessageTemplate,
  MessageTemplateCreateInput,
  MessageTemplateUpdateInput,
  MessageTemplateQueryParams,
  MessageTemplateBusinessLogicInterface,
} from "./interface";
import { createError } from "@/lib/utils/common";
import { MessageTemplateDBRepository } from "./DBRepository";

export class MessageTemplateBusinessLogic implements MessageTemplateBusinessLogicInterface {
  constructor(private readonly db: MessageTemplateDBRepository) {}

  private validateId(id: string) {
    if (!id || !id.trim()) throw createError("MessageTemplate ID is required", "INVALID_ID");
  }

  private trimCreateInput(data: MessageTemplateCreateInput): MessageTemplateCreateInput {
    return {
      ...data,
      title: data.title.trim(),
      content: data.content?.trim() ?? "",
      tags: data.tags?.map(t => t.trim()) ?? [],
      variables: data.variables?.map(c => c.trim()) ?? [],
      isActive: data.isActive ?? true,
    };
  }

  private trimUpdateInput(data: MessageTemplateUpdateInput): MessageTemplateUpdateInput {
    return {
      ...data,
      title: data.title?.trim(),
      content: data.content?.trim(),
      tags: data.tags?.map(t => t.trim()),
      variables: data.variables?.map(c => c.trim()),
    };
  }

  async getById(id: string, includeDeleted = false): Promise<MessageTemplate | null> {
    this.validateId(id);
    return this.db.getById(id, includeDeleted);
  }

  async getAll(params?: MessageTemplateQueryParams): Promise<{ items: MessageTemplate[]; total: number }> {
    // Optionally validate filters/sorts here if needed
    return this.db.getAll(params);
  }

  async create(data: MessageTemplateCreateInput): Promise<MessageTemplate> {
    const trimmedData = this.trimCreateInput(data);

    // Check for duplicate title
    const existing = await this.db.getAll({ filters: [{ field: "title", value: trimmedData.title }] });
    if (existing.items.length > 0) {
      throw createError("AI Rule with the same title already exists", "DUPLICATE_NAME");
    }

    return this.db.create(trimmedData);
  }

  async update(id: string, data: MessageTemplateUpdateInput): Promise<MessageTemplate | null> {
    this.validateId(id);

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA");
    }

    const existingRule = await this.db.getById(id);
    if (!existingRule) throw createError("AI Rule not found", "NOT_FOUND");

    if (data.title && data.title.trim() !== existingRule.title) {
      const duplicates = await this.db.getAll({ filters: [{ field: "title", value: data.title.trim() }] });
      if (duplicates.items.some(r => r.id !== id)) {
        throw createError("Another AI Rule with this title exists", "DUPLICATE_NAME");
      }
    }

    const trimmedData = this.trimUpdateInput(data);
    return this.db.update(id, trimmedData);
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    this.validateId(id);

    const existingRule = await this.db.getById(id);
    if (!existingRule) throw createError("AI Rule not found", "NOT_FOUND");

    return this.db.delete(id, hardDelete);
  }

  async restore(id: string): Promise<boolean> {
    this.validateId(id);

    const rule = await this.db.getById(id, true);
    if (!rule || !rule.deletedAt) return false;

    // Check for conflicts by title
    const conflict = await this.db.getAll({ filters: [{ field: "title", value: rule.title }] });
    if (conflict.items.length > 0) return false;

    return this.db.restore(id);
  }

  async bulkCreate(data: MessageTemplateCreateInput[]): Promise<MessageTemplate[]> {
    if (!Array.isArray(data) || data.length === 0) {
      throw createError("Input must be a non-empty array", "INVALID_BULK_CREATE_DATA");
    }

    for (const entry of data) {
      this.trimCreateInput(entry); // Will throw if invalid
      const existing = await this.db.getAll({ filters: [{ field: "title", value: entry.title.trim() }] });
      if (existing.items.length > 0) {
        throw createError(`Duplicate title found: ${entry.title}`, "DUPLICATE_NAME");
      }
    }

    const trimmedData = data.map(d => this.trimCreateInput(d));
    return this.db.bulkCreate(trimmedData);
  }

  async bulkUpdate(updates: { id: string; data: MessageTemplateUpdateInput }[]): Promise<number> {
    if (!Array.isArray(updates) || updates.length === 0) {
      throw createError("Input must be a non-empty array", "INVALID_BULK_UPDATE_DATA");
    }

    for (const { id, data } of updates) {
      this.validateId(id);

      if (!data || Object.keys(data).length === 0) {
        throw createError(`No data provided for update of ID ${id}`, "INVALID_UPDATE_DATA");
      }

      const existingRule = await this.db.getById(id);
      if (!existingRule) throw createError(`Rule with ID ${id} not found`, "NOT_FOUND");

      if (data.title && data.title.trim() !== existingRule.title) {
        const duplicates = await this.db.getAll({ filters: [{ field: "title", value: data.title.trim() }] });
        if (duplicates.items.some(r => r.id !== id)) {
          throw createError(`Duplicate title in update: ${data.title}`, "DUPLICATE_NAME");
        }
      }

      this.trimUpdateInput(data); // Will throw if invalid
    }

    const trimmedUpdates = updates.map(({ id, data }) => ({
      id,
      data: this.trimUpdateInput(data),
    }));

    return this.db.bulkUpdate(trimmedUpdates);
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError("IDs must be a non-empty array", "INVALID_BULK_DELETE_DATA");
    }

    for (const id of ids) {
      this.validateId(id);
      const rule = await this.db.getById(id);
      if (!rule) throw createError(`Rule with ID ${id} not found`, "NOT_FOUND");
    }

    return this.db.bulkDelete(ids, hardDelete);
  }

  async search(query: string): Promise<MessageTemplate[]> {
    if (!query || !query.trim()) {
      return [];
    }

    const searchTerm = query.trim();
    const result = await this.db.getAll({
      filters: [
        { field: "title", value: { $regex: searchTerm, $options: "i" } }
      ]
    });

    return result.items;
  }
}
