import { BaseQueryParams } from "./BaseDBRepository";

export function buildMongoQuery<T>(params?: BaseQueryParams<T>, searchableFields: (keyof T | string)[] = []): { query: any; sort: any; limit: number; offset: number } {
  const query: any = {};

  // Handle search
  if (params?.search && searchableFields.length > 0) {
    const searchTerm = params.search.trim();
    query.$or = searchableFields.map(field => ({
      [field]: { $regex: searchTerm, $options: "i" }
    }));
  }

  // Filters
  if (params?.filters) {
    for (const filter of params.filters) {
      query[filter.field] = filter.value;
    }
  }

  // Handle deletedAt
  if (!params?.includeDeleted && !params?.filters?.some(f => f.field === "deletedAt")) {
    query.deletedAt = { $exists: false };
  }

  // Sorting
  const sort: any = {};
  if (params?.sorts?.length) {
    for (const s of params.sorts) {
      sort[s.field] = s.direction === "asc" ? 1 : -1;
    }
  } else {
    sort.createdAt = -1;
  }

  const limit = params?.limit ?? 20;
  const page = params?.offset ?? 1;
  const offset = (page - 1) * limit;

  return { query, sort, limit, offset };
}
