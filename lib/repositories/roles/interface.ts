export interface Role {
  id: string;
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy?: string;
}

export interface RoleCreateInput {
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive?: boolean;
  createdBy: string;
}

export interface RoleUpdateInput {
  STRING_FIELD?: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2?: string[];
  tags?: string[];
  ARRAY_FIELD?: string[];
  isActive?: boolean;
  updatedBy?: string;
}

export interface RoleQueryParams {
  search?: string;
  filters?: { field: keyof Role | string; value: any }[];
  sorts?: { field: keyof Role | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface RoleBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<Role | null>;
  getAll(params?: RoleQueryParams): Promise<{
    items: Role[];
    total: number;
  }>;
  create(data: RoleCreateInput): Promise<Role>;
  update(id: string, data: RoleUpdateInput): Promise<Role | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: RoleCreateInput[]): Promise<Role[]>;
  bulkUpdate(updates: { id: string; data: RoleUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
