import { Team, TeamCreateInput, TeamUpdateInput, TeamQueryParams } from "./interface";
import { ObjectId, WithId, Document } from "mongodb";
import { MongoDriver } from "../MongoDriver";
import { TeamDBRepository, TeamDbQueryParams } from "./DBRepository";
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections";
import { buildMongoQuery } from "../queryBuilder";

function mapMongoDocToTeam(doc: WithId<Document> | null): Team | null {
  if (!doc) return null;
  const { _id, ...rest } = doc;
  return {
    id: _id.toString(),
    ...rest,
  } as Team;
}

export class MongoTeamRepository
  implements TeamDBRepository {

  private collection;

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.TEAMS);
  }

  async getById(id: string, includeDeleted = false): Promise<Team | null> {
    const query: any = { _id: new ObjectId(id) };
    if (!includeDeleted) query.deletedAt = { $exists: false };
    const doc = await this.collection.findOne(query);
    return mapMongoDocToTeam(doc);
  }

  async getAll(params?: TeamQueryParams): Promise<{ items: Team[]; total: number }> {
    const { query, sort, limit, offset } = buildMongoQuery(params, ["STRING_FIELD", "STRING_FIELD2", "ARRAY_FIELD"]);

    const cursor = this.collection.find(query).sort(sort).skip(offset).limit(limit);
    const docs = await cursor.toArray();
    const items = docs.map(mapMongoDocToTeam).filter((i): i is Team => i !== null);

    const total = await this.collection.countDocuments(query);
    return { items, total };
  }



  async getCount(params?: TeamQueryParams): Promise<{ total: number }> {
    const { query } = buildMongoQuery(params, ["STRING_FIELD", "STRING_FIELD2", "ARRAY_FIELD"]);
    const total = await this.collection.countDocuments(query);
    return { total };
  }

  async create(data: TeamCreateInput): Promise<Team> {
    const now = new Date();
    const doc = {
      ...data,
      createdAt: now,
      updatedAt: now,
    };
    const result = await this.collection.insertOne(doc);
    return mapMongoDocToTeam({ _id: result.insertedId, ...doc } as WithId<Document>)!;
  }

  async update(id: string, data: TeamUpdateInput): Promise<Team | null> {
    // First check if the document exists
    const existing = await this.collection.findOne({
      _id: new ObjectId(id),
      deletedAt: { $exists: false }
    });

    if (!existing) return null;

    // Filter out undefined values to avoid overwriting existing fields
    const updateData = Object.fromEntries(
      Object.entries({ ...data, updatedAt: new Date() })
        .filter(([_, value]) => value !== undefined)
    );

    // Update the document
    const updateResult = await this.collection.updateOne(
      { _id: new ObjectId(id), deletedAt: { $exists: false } },
      { $set: updateData }
    );

    if (updateResult.modifiedCount === 0) return null;

    // Fetch the updated document
    const updated = await this.collection.findOne({
      _id: new ObjectId(id)
    });

    if (!updated) return null;
    return mapMongoDocToTeam(updated);
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ _id: new ObjectId(id) });
      return result.deletedCount === 1;
    } else {
      const result = await this.collection.updateOne(
        { _id: new ObjectId(id) },
        { $set: { deletedAt: new Date() } }
      );
      return result.modifiedCount === 1;
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { _id: new ObjectId(id) },
      {
        $unset: { deletedAt: "" },
        $set: { updatedAt: new Date() }
      }
    );
    return result.modifiedCount === 1;
  }

  async bulkCreate(data: TeamCreateInput[]): Promise<Team[]> {
    const now = new Date();
    const docs = data.map(d => ({
      ...d,
      createdAt: now,
      updatedAt: now,
    }));
    const result = await this.collection.insertMany(docs);
    return docs.map((d, i) =>
      mapMongoDocToTeam({ _id: result.insertedIds[i], ...d } as WithId<Document>)!
    );
  }

  async bulkUpdate(updates: { id: string; data: TeamUpdateInput }[]): Promise<number> {
    let count = 0;
    for (const { id, data } of updates) {
      const res = await this.collection.updateOne(
        { _id: new ObjectId(id) },
        { $set: { ...data, updatedAt: new Date() } }
      );
      if (res.modifiedCount) count++;
    }
    return count;
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    const objectIds = ids.map(id => new ObjectId(id));
    if (hardDelete) {
      const result = await this.collection.deleteMany({ _id: { $in: objectIds } });
      return result.deletedCount ?? 0;
    } else {
      const result = await this.collection.updateMany(
        { _id: { $in: objectIds } },
        { $set: { deletedAt: new Date() } }
      );
      return result.modifiedCount ?? 0;
    }
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({});
  }
}
