import {
  Team,
  TeamCreateInput,
  TeamUpdateInput,
  TeamQueryParams,
  TeamBusinessLogicInterface,
} from "./interface";
import { createError } from "@/lib/utils/common";
import { TeamDBRepository } from "./DBRepository";

export class TeamBusinessLogic implements TeamBusinessLogicInterface {
  constructor(private readonly db: TeamDBRepository) {}

  private validateId(id: string) {
    if (!id || !id.trim()) throw createError("Team ID is required", "INVALID_ID");
  }

  private trimCreateInput(data: TeamCreateInput): TeamCreateInput {
    return {
      ...data,
      STRING_FIELD: data.STRING_FIELD.trim(),
      STRING_FIELD2: data.STRING_FIELD2?.trim() ?? "",
      ARRAY_FIELD: data.ARRAY_FIELD?.map(t => t.trim()) ?? [],
      ARRAY_FIELD2: data.ARRAY_FIELD2?.map(c => c.trim()) ?? [],
      tags: data.tags?.map(a => a.trim()) ?? [],
      isActive: data.isActive ?? true,
    };
  }

  private trimUpdateInput(data: TeamUpdateInput): TeamUpdateInput {
    return {
      ...data,
      STRING_FIELD: data.STRING_FIELD?.trim(),
      STRING_FIELD2: data.STRING_FIELD2?.trim(),
      ARRAY_FIELD: data.ARRAY_FIELD?.map(t => t.trim()),
      ARRAY_FIELD2: data.ARRAY_FIELD2?.map(c => c.trim()),
      tags: data.tags?.map(a => a.trim()),
    };
  }

  async getById(id: string, includeDeleted = false): Promise<Team | null> {
    this.validateId(id);
    return this.db.getById(id, includeDeleted);
  }

  async getAll(params?: TeamQueryParams): Promise<{ items: Team[]; total: number }> {
    // Optionally validate filters/sorts here if needed
    return this.db.getAll(params);
  }

  async create(data: TeamCreateInput): Promise<Team> {
    const trimmedData = this.trimCreateInput(data);

    // Check for duplicate STRING_FIELD
    const existing = await this.db.getAll({ filters: [{ field: "STRING_FIELD", value: trimmedData.STRING_FIELD }] });
    if (existing.items.length > 0) {
      throw createError("Team with the same STRING_FIELD already exists", "DUPLICATE_NAME");
    }

    return this.db.create(trimmedData);
  }

  async update(id: string, data: TeamUpdateInput): Promise<Team | null> {
    this.validateId(id);

    if (!data || Object.keys(data).length === 0) {
      throw createError("No data provided for update", "INVALID_UPDATE_DATA");
    }

    const existingRule = await this.db.getById(id);
    if (!existingRule) return null;

    if (data.STRING_FIELD && data.STRING_FIELD.trim() !== existingRule.STRING_FIELD) {
      const duplicates = await this.db.getAll({ filters: [{ field: "STRING_FIELD", value: data.STRING_FIELD.trim() }] });
      if (duplicates.items.some(r => r.id !== id)) {
        throw createError("Another Team with this STRING_FIELD exists", "DUPLICATE_NAME");
      }
    }

    const trimmedData = this.trimUpdateInput(data);
    return this.db.update(id, trimmedData);
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    this.validateId(id);

    const existingRule = await this.db.getById(id);
    if (!existingRule) throw createError("Team not found", "NOT_FOUND");

    return this.db.delete(id, hardDelete);
  }

  async restore(id: string): Promise<boolean> {
    this.validateId(id);

    const team = await this.db.getById(id, true);
    if (!team || !team.deletedAt) return false;

    // Check for conflicts by STRING_FIELD
    const conflict = await this.db.getAll({ filters: [{ field: "STRING_FIELD", value: team.STRING_FIELD }] });
    if (conflict.items.length > 0) return false;

    return this.db.restore(id);
  }

  async bulkCreate(data: TeamCreateInput[]): Promise<Team[]> {
    if (!Array.isArray(data) || data.length === 0) {
      throw createError("Input must be a non-empty array", "INVALID_BULK_CREATE_DATA");
    }

    for (const entry of data) {
      this.trimCreateInput(entry); // Will throw if invalid
      const existing = await this.db.getAll({ filters: [{ field: "STRING_FIELD", value: entry.STRING_FIELD.trim() }] });
      if (existing.items.length > 0) {
        throw createError(`Duplicate STRING_FIELD found: ${entry.STRING_FIELD}`, "DUPLICATE_NAME");
      }
    }

    const trimmedData = data.map(d => this.trimCreateInput(d));
    return this.db.bulkCreate(trimmedData);
  }

  async bulkUpdate(updates: { id: string; data: TeamUpdateInput }[]): Promise<number> {
    if (!Array.isArray(updates) || updates.length === 0) {
      throw createError("Input must be a non-empty array", "INVALID_BULK_UPDATE_DATA");
    }

    for (const { id, data } of updates) {
      this.validateId(id);

      if (!data || Object.keys(data).length === 0) {
        throw createError(`No data provided for update of ID ${id}`, "INVALID_UPDATE_DATA");
      }

      const existingRule = await this.db.getById(id);
      if (!existingRule) throw createError(`Rule with ID ${id} not found`, "NOT_FOUND");

      if (data.STRING_FIELD && data.STRING_FIELD.trim() !== existingRule.STRING_FIELD) {
        const duplicates = await this.db.getAll({ filters: [{ field: "STRING_FIELD", value: data.STRING_FIELD.trim() }] });
        if (duplicates.items.some(r => r.id !== id)) {
          throw createError(`Duplicate STRING_FIELD in update: ${data.STRING_FIELD}`, "DUPLICATE_NAME");
        }
      }

      this.trimUpdateInput(data); // Will throw if invalid
    }

    const trimmedUpdates = updates.map(({ id, data }) => ({
      id,
      data: this.trimUpdateInput(data),
    }));

    return this.db.bulkUpdate(trimmedUpdates);
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError("IDs must be a non-empty array", "INVALID_BULK_DELETE_DATA");
    }

    for (const id of ids) {
      this.validateId(id);
      const team = await this.db.getById(id);
      if (!team) throw createError(`Rule with ID ${id} not found`, "NOT_FOUND");
    }

    return this.db.bulkDelete(ids, hardDelete);
  }
}
