export interface Team {
  id: string;
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy?: string;
}

export interface TeamCreateInput {
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive?: boolean;
  createdBy: string;
}

export interface TeamUpdateInput {
  STRING_FIELD?: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2?: string[];
  tags?: string[];
  ARRAY_FIELD?: string[];
  isActive?: boolean;
  updatedBy?: string;
}

export interface TeamQueryParams {
  search?: string;
  filters?: { field: keyof Team | string; value: any }[];
  sorts?: { field: keyof Team | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface TeamBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<Team | null>;
  getAll(params?: TeamQueryParams): Promise<{
    items: Team[];
    total: number;
  }>;
  create(data: TeamCreateInput): Promise<Team>;
  update(id: string, data: TeamUpdateInput): Promise<Team | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: TeamCreateInput[]): Promise<Team[]>;
  bulkUpdate(updates: { id: string; data: TeamUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
