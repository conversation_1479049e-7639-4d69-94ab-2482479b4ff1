export interface User {
  id: string;
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy?: string;
}

export interface UserCreateInput {
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive?: boolean;
  createdBy: string;
}

export interface UserUpdateInput {
  STRING_FIELD?: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2?: string[];
  tags?: string[];
  ARRAY_FIELD?: string[];
  isActive?: boolean;
  updatedBy?: string;
}

export interface UserQueryParams {
  search?: string;
  filters?: { field: keyof User | string; value: any }[];
  sorts?: { field: keyof User | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface UserBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<User | null>;
  getAll(params?: UserQueryParams): Promise<{
    items: User[];
    total: number;
  }>;
  create(data: UserCreateInput): Promise<User>;
  update(id: string, data: UserUpdateInput): Promise<User | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: UserCreateInput[]): Promise<User[]>;
  bulkUpdate(updates: { id: string; data: UserUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
