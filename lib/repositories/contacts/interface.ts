// contact.ts

export interface Contact {
  id: string;
  name: string;
  phone: string;
  email?: string;
  tags?: string[];
  notes?: { text: string; createdAt: string }[];

  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
}

export interface ContactCreateInput {
  name: string;
  phone: string;
  email?: string;
  tags?: string[];
  notes?: { text: string; createdAt: string }[];
  createdBy?: string;
}

export interface ContactUpdateInput {
  name?: string;
  phone?: string;
  email?: string;
  tags?: string[];
  notes?: { text: string; createdAt: string }[];
  updatedBy?: string;
}

export interface ContactQueryParams {
  search?: string;
  filters?: { field: keyof Contact | string; value: any }[];
  sorts?: { field: keyof Contact | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface ContactBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<Contact | null>;
  getAll(params?: ContactQueryParams): Promise<{
    items: Contact[];
    total: number;
  }>;
  create(data: ContactCreateInput): Promise<Contact>;
  update(id: string, data: ContactUpdateInput): Promise<Contact | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;

  bulkCreate(data: ContactCreateInput[]): Promise<Contact[]>;
  bulkUpdate(updates: { id: string; data: ContactUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
