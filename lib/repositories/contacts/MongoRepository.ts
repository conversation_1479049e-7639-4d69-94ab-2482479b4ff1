import { Contact, ContactCreateInput, ContactUpdateInput, ContactQueryParams } from "./interface";
import { ObjectId, WithId, Document } from "mongodb";
import { MongoDriver } from "../MongoDriver";
import { ContactDBRepository, ContactDbQueryParams } from "./DBRepository";
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections";
import { buildMongoQuery } from "../queryBuilder";

function mapMongoDocToContact(doc: WithId<Document> | null): Contact | null {
  if (!doc) return null;
  const { _id, ...rest } = doc;
  return {
    id: _id.toString(),
    ...rest,
  } as Contact;
}

export class MongoContactRepository
  implements ContactDBRepository {

  private collection;

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.CONTACTS);
  }

  async getById(id: string, includeDeleted = false): Promise<Contact | null> {
    const query: any = { _id: new ObjectId(id) };
    if (!includeDeleted) query.deletedAt = { $exists: false };
    const doc = await this.collection.findOne(query);
    return mapMongoDocToContact(doc);
  }

  async getAll(params?: ContactQueryParams): Promise<{ items: Contact[]; total: number }> {
    const query: any = {};

    // Handle search parameter
    if (params?.search) {
      const searchTerm = params.search.trim();
      query.$or = [
        { name: { $regex: searchTerm, $options: "i" } },
        { phone: { $regex: searchTerm, $options: "i" } },
        { tags: { $in: [new RegExp(searchTerm, "i")] } }
      ];
    }

    // Build filters
    if (params?.filters) {
      for (const filter of params.filters) {
        query[filter.field] = filter.value;
      }
    }

    // Exclude deleted unless explicitly included
    if (!params?.includeDeleted && !params?.filters?.some(f => f.field === "deletedAt")) {
      query.deletedAt = { $exists: false };
    }

    // Build sort object from params.sorts (array)
    let sort: any = {};
    if (params?.sorts?.length) {
      for (const s of params.sorts) {
        sort[s.field] = s.direction === "asc" ? 1 : -1;
      }
    } else {
      sort = { createdAt: -1 };
    }

    const limit = params?.limit ?? 20;
    const page = params?.page ?? 1;
    const offset = (page - 1) * limit;

    const cursor = this.collection.find(query).sort(sort).skip(offset).limit(limit);
    const docs = await cursor.toArray();
    const items = docs.map(mapMongoDocToContact).filter((i): i is Contact => i !== null);

    const total = await this.collection.countDocuments(query);

    return { items, total };
  }

  async getCount(params?: ContactQueryParams): Promise<{ total: number }> {
    const { query } = buildMongoQuery(params, ["name", "description", "tags"]);
    const total = await this.collection.countDocuments(query);
    return { total };
  }

  async create(data: ContactCreateInput): Promise<Contact> {
    const now = new Date();
    const doc = {
      ...data,
      createdAt: now,
      updatedAt: now,
    };
    const result = await this.collection.insertOne(doc);
    return mapMongoDocToContact({ _id: result.insertedId, ...doc } as WithId<Document>)!;
  }

  async update(id: string, data: ContactUpdateInput): Promise<Contact | null> {
    // First check if the document exists
    const existing = await this.collection.findOne({
      _id: new ObjectId(id),
      deletedAt: { $exists: false }
    });

    if (!existing) return null;

    // Filter out undefined values to avoid overwriting existing fields
    const updateData = Object.fromEntries(
      Object.entries({ ...data, updatedAt: new Date() })
        .filter(([_, value]) => value !== undefined)
    );

    // Update the document
    const updateResult = await this.collection.updateOne(
      { _id: new ObjectId(id), deletedAt: { $exists: false } },
      { $set: updateData }
    );

    if (updateResult.modifiedCount === 0) return null;

    // Fetch the updated document
    const updated = await this.collection.findOne({
      _id: new ObjectId(id)
    });

    if (!updated) return null;
    return mapMongoDocToContact(updated);
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ _id: new ObjectId(id) });
      return result.deletedCount === 1;
    } else {
      const result = await this.collection.updateOne(
        { _id: new ObjectId(id) },
        { $set: { deletedAt: new Date() } }
      );
      return result.modifiedCount === 1;
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { _id: new ObjectId(id) },
      {
        $unset: { deletedAt: "" },
        $set: { updatedAt: new Date() }
      }
    );
    return result.modifiedCount === 1;
  }

  async bulkCreate(data: ContactCreateInput[]): Promise<Contact[]> {
    const now = new Date();
    const docs = data.map(d => ({
      ...d,
      createdAt: now,
      updatedAt: now,
    }));
    const result = await this.collection.insertMany(docs);
    return docs.map((d, i) =>
      mapMongoDocToContact({ _id: result.insertedIds[i], ...d } as WithId<Document>)!
    );
  }

  async bulkUpdate(updates: { id: string; data: ContactUpdateInput }[]): Promise<number> {
    let count = 0;
    for (const { id, data } of updates) {
      const res = await this.collection.updateOne(
        { _id: new ObjectId(id) },
        { $set: { ...data, updatedAt: new Date() } }
      );
      if (res.modifiedCount) count++;
    }
    return count;
  }

  async bulkDelete(ids: string[], hardDelete = false): Promise<number> {
    const objectIds = ids.map(id => new ObjectId(id));
    if (hardDelete) {
      const result = await this.collection.deleteMany({ _id: { $in: objectIds } });
      return result.deletedCount ?? 0;
    } else {
      const result = await this.collection.updateMany(
        { _id: { $in: objectIds } },
        { $set: { deletedAt: new Date() } }
      );
      return result.modifiedCount ?? 0;
    }
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({});
  }
}
