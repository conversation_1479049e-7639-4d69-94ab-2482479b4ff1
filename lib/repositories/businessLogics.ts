import { AiRuleBusinessLogic } from "./aiRules";
import { MongoAiRuleRepository } from "./aiRules/MongoRepository";
import { MongoAuthDBRepository, AuthBusinessLogic } from "./auth";
import { ChatMessageBusinessLogic } from "./chatMessages";
import { MongoChatMessageRepository } from "./chatMessages/MongoRepository";
import { ChatBusinessLogic } from "./chats";
import { MongoChatRepository } from "./chats/MongoRepository";
import { ContactBusinessLogic } from "./contacts";
import { MongoContactRepository } from "./contacts/MongoRepository";
import { InvitationBusinessLogic } from "./invitations";
import { MongoInvitationRepository } from "./invitations/MongoRepository";
import { driver } from "./LiveMongoDriver";
import { MessageTemplateBusinessLogic } from "./messageTemplates";
import { MongoMessageTemplateRepository } from "./messageTemplates/MongoRepository";
import { RoleBusinessLogic } from "./roles";
import { MongoRoleRepository } from "./roles/MongoRepository";
import { TeamBusinessLogic } from "./teams";
import { MongoTeamRepository } from "./teams/MongoRepository";
import { UserBusinessLogic } from "./users";
import { MongoUserRepository } from "./users/MongoRepository";

const authDb = new MongoAuthDBRepository(driver);
export const authBusinessLogic = new AuthBusinessLogic(authDb);

const aiRulesDb = new MongoAiRuleRepository(driver);
export const aiRulesBusinessLogic = new AiRuleBusinessLogic(aiRulesDb);

const contactsDb = new MongoContactRepository(driver);
export const contactsBusinessLogic = new ContactBusinessLogic(contactsDb);


const messageTemplatesDb = new MongoMessageTemplateRepository(driver);
export const messageTemplatesBusinessLogic = new MessageTemplateBusinessLogic(messageTemplatesDb);


const chatMessagesDb = new MongoChatMessageRepository(driver);
export const chatMessagesBusinessLogic = new ChatMessageBusinessLogic(chatMessagesDb);

const chatsDb = new MongoChatRepository(driver);
export const chatsBusinessLogic = new ChatBusinessLogic(chatsDb);

// Users
const usersDb = new MongoUserRepository(driver);
export const usersBusinessLogic = new UserBusinessLogic(usersDb);

// Teams
const teamsDb = new MongoTeamRepository(driver);
export const teamsBusinessLogic = new TeamBusinessLogic(teamsDb);

// Invitations
const invitationsDb = new MongoInvitationRepository(driver);
export const invitationsBusinessLogic = new InvitationBusinessLogic(invitationsDb);

const rolesDb = new MongoRoleRepository(driver);
export const rolesBusinessLogic = new RoleBusinessLogic(rolesDb);