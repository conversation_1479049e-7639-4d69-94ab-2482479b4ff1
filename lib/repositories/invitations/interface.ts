export interface Invitation {
  id: string;
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy?: string;
}

export interface InvitationCreateInput {
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive?: boolean;
  createdBy: string;
}

export interface InvitationUpdateInput {
  STRING_FIELD?: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2?: string[];
  tags?: string[];
  ARRAY_FIELD?: string[];
  isActive?: boolean;
  updatedBy?: string;
}

export interface InvitationQueryParams {
  search?: string;
  filters?: { field: keyof Invitation | string; value: any }[];
  sorts?: { field: keyof Invitation | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface InvitationBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<Invitation | null>;
  getAll(params?: InvitationQueryParams): Promise<{
    items: Invitation[];
    total: number;
  }>;
  create(data: InvitationCreateInput): Promise<Invitation>;
  update(id: string, data: InvitationUpdateInput): Promise<Invitation | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: InvitationCreateInput[]): Promise<Invitation[]>;
  bulkUpdate(updates: { id: string; data: InvitationUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
