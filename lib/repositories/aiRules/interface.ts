export interface AiRule {
  id: string;
  name: string;
  description?: string;
  conditions: string[];
  actions: string[];
  tags?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy?: string;
}

export interface AiRuleCreateInput {
  name: string;
  description?: string;
  conditions: string[];
  actions: string[];
  tags?: string[];
  isActive?: boolean;
  createdBy: string;
}

export interface AiRuleUpdateInput {
  name?: string;
  description?: string;
  conditions?: string[];
  actions?: string[];
  tags?: string[];
  isActive?: boolean;
  updatedBy?: string;
}

export interface AiRuleQueryParams {
  search?: string;
  filters?: { field: keyof AiRule | string; value: any }[];
  sorts?: { field: keyof AiRule | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface AiRuleBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<AiRule | null>;
  getAll(params?: AiRuleQueryParams): Promise<{
    items: AiRule[];
    total: number;
  }>;
  create(data: AiRuleCreateInput): Promise<AiRule>;
  update(id: string, data: AiRuleUpdateInput): Promise<AiRule | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: AiRuleCreateInput[]): Promise<AiRule[]>;
  bulkUpdate(updates: { id: string; data: AiRuleUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
