# Auth Repository Architecture

This directory implements a clean separation of concerns for authentication using the Repository Pattern with dependency injection.

## Architecture Overview

```
┌─────────────────────────────────────┐
│        Business Logic Layer        │
│   (AuthBusinessLogic)     │
│                                     │
│ • Authentication logic              │
│ • Password hashing/validation       │
│ • Token generation/validation       │
│ • Business rules                    │
│ • Error handling                    │
└─────────────────┬───────────────────┘
                  │ depends on
                  ▼
┌─────────────────────────────────────┐
│         Data Access Layer          │
│      (AuthDBRepository)           │
│                                     │
│ • Pure CRUD operations              │
│ • Database queries                  │
│ • No business logic                 │
│ • Swappable implementations         │
└─────────────────────────────────────┘
```

## Components

### 1. Interfaces

#### `AuthBusinessLogicInterface` (Business Logic Interface)
- High-level authentication operations
- Used by application services and API handlers
- Contains business logic methods like `login()`, `register()`, `validateToken()`

#### `AuthDBRepository` (Data Access Interface)
- Low-level CRUD operations
- Pure data access without business logic
- Methods like `findUserByEmail()`, `createUser()`, `createToken()`

### 2. Implementations

#### `AuthBusinessLogic`
- Implements `AuthBusinessLogicInterface`
- Contains all authentication business logic
- Depends on `AuthDBRepository` via constructor injection
- Handles password hashing, token generation, validation rules

#### `MongoAuthDBRepository`
- Implements `AuthDBRepository`
- MongoDB-specific data access operations
- No business logic, only database operations

#### `InMemoryAuthDBRepository`
- Implements `AuthDBRepository`
- In-memory implementation for testing
- Provides same interface as MongoDB implementation

## Usage Examples

### Production Usage
```typescript
import { authBusinessLogic } from '@/lib/repositories/auth';

// The authBusinessLogic is pre-configured with MongoDB
const result = await authBusinessLogic.login({ email: "<EMAIL>", password: "password123" });
```

### Testing with Mocks (Unit Tests)
```typescript
import { mockAuthRepository } from '@/tests/mocks/authBusinessLogicMock';

// Full control over mock behavior
mockAuthRepository.login.mockResolvedValue({
  token: "mock-token",
  refresh_token: "mock-refresh",
  user: mockUser
});
```

### Testing with In-Memory Repository (Integration Tests)
```typescript
import { createInMemoryAuthRepository } from '@/tests/mocks/authBusinessLogicMock';

// Real business logic with in-memory storage
const authBusinessLogic = createInMemoryAuthRepository();
const result = await authBusinessLogic.register({
  email: "<EMAIL>",
  password: "password123",
  name: "Test User"
});
```

### Custom Implementation
```typescript
import { AuthBusinessLogic } from '@/lib/repositories/auth';
import { InMemoryAuthDBRepository } from '@/tests/InMemoryDBRepository';

// Create custom configuration
const crudRepo = new InMemoryAuthDBRepository();
const authBusinessLogic = new AuthBusinessLogic(crudRepo);
```

## Benefits

### 1. **Separation of Concerns**
- Business logic is separated from data access
- Each layer has a single responsibility
- Easier to maintain and understand

### 2. **Testability**
- Business logic can be tested with in-memory storage
- CRUD operations can be tested independently
- Easy to mock dependencies

### 3. **Flexibility**
- Easy to swap MongoDB for other databases
- Can use different storage for different environments
- Business logic remains unchanged when storage changes

### 4. **Dependency Injection**
- Loose coupling between layers
- Easy to configure different implementations
- Supports testing and development scenarios

## File Structure

```
src/lib/repositories/auth/
├── README.md                     # This documentation
├── AuthBusinessLogicInterface.ts             # Business logic interface
├── DBRepository.ts             # Data access interface
├── BusinessLogic.ts    # Business logic implementation
├── MongoDBRepository.ts        # MongoDB data access implementation
├── index.ts                      # Main exports and configuration
└── mongo.ts                      # Legacy implementation (deprecated)

src/tests/
├── InMemoryDBRepository.ts     # In-memory data access implementation (testing only)
├── mocks/
│   └── authBusinessLogicMock.ts          # Mock repository utilities
├── auth-api.test.ts             # API integration tests
└── auth-business-logic.test.ts  # Business logic tests
```

## Migration from Legacy

The old `MongoAuthRepository` in `mongo.ts` mixed business logic with data access. The new architecture separates these concerns:

**Before:**
```typescript
// Everything in one class
class MongoAuthRepository {
  async login(credentials) {
    // Database access + business logic mixed together
  }
}
```

**After:**
```typescript
// Separated concerns
class AuthBusinessLogic {
  constructor(private crudRepo: AuthDBRepository) {}
  
  async login(credentials) {
    // Only business logic, delegates data access to crudRepo
  }
}

class MongoAuthDBRepository {
  async findUserWithPasswordByEmail(email) {
    // Only database operations
  }
}
```

## Testing Strategy

1. **Unit Tests**: Use `mockAuthRepository` for testing API handlers and services
2. **Integration Tests**: Use `createInMemoryAuthRepository()` for testing business logic
3. **E2E Tests**: Use the real MongoDB implementation

This architecture provides the flexibility to choose the right testing approach for each scenario.
