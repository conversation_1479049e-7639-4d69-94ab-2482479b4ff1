export interface Chat {
  id: string;
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy?: string;
}

export interface ChatCreateInput {
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive?: boolean;
  createdBy: string;
}

export interface ChatUpdateInput {
  STRING_FIELD?: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2?: string[];
  tags?: string[];
  ARRAY_FIELD?: string[];
  isActive?: boolean;
  updatedBy?: string;
}

export interface ChatQueryParams {
  search?: string;
  filters?: { field: keyof Chat | string; value: any }[];
  sorts?: { field: keyof Chat | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface ChatBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<Chat | null>;
  getAll(params?: ChatQueryParams): Promise<{
    items: Chat[];
    total: number;
  }>;
  create(data: ChatCreateInput): Promise<Chat>;
  update(id: string, data: ChatUpdateInput): Promise<Chat | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: ChatCreateInput[]): Promise<Chat[]>;
  bulkUpdate(updates: { id: string; data: ChatUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
