export interface ChatMessage {
  id: string;
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy?: string;
}

export interface ChatMessageCreateInput {
  STRING_FIELD: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2: string[];
  ARRAY_FIELD?: string[];
  tags: string[];
  isActive?: boolean;
  createdBy: string;
}

export interface ChatMessageUpdateInput {
  STRING_FIELD?: string;
  STRING_FIELD2?: string;
  ARRAY_FIELD2?: string[];
  tags?: string[];
  ARRAY_FIELD?: string[];
  isActive?: boolean;
  updatedBy?: string;
}

export interface ChatMessageQueryParams {
  search?: string;
  filters?: { field: keyof ChatMessage | string; value: any }[];
  sorts?: { field: keyof ChatMessage | string; direction: "asc" | "desc" }[];
  page?: number;
  limit?: number;
  includeDeleted?: boolean;
}

export interface ChatMessageBusinessLogicInterface {
  getById(id: string, includeDeleted?: boolean): Promise<ChatMessage | null>;
  getAll(params?: ChatMessageQueryParams): Promise<{
    items: ChatMessage[];
    total: number;
  }>;
  create(data: ChatMessageCreateInput): Promise<ChatMessage>;
  update(id: string, data: ChatMessageUpdateInput): Promise<ChatMessage | null>;
  delete(id: string, hardDelete?: boolean): Promise<boolean>;
  restore(id: string): Promise<boolean>;
  bulkCreate(data: ChatMessageCreateInput[]): Promise<ChatMessage[]>;
  bulkUpdate(updates: { id: string; data: ChatMessageUpdateInput }[]): Promise<number>;
  bulkDelete(ids: string[], hardDelete?: boolean): Promise<number>;
}
