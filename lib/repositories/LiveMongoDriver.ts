/**
 * LiveMongoDriver
 * ===============
 * 
 * This MongoDB driver implementation is designed to be **serverless-aware**,
 * making it suitable for environments like AWS Lambda, Vercel Functions, Cloudflare Workers, etc.
 *
 * ## Why Global Caching?
 * In serverless environments, the runtime may be **frozen and reused** across multiple invocations.
 * To avoid repeated connections on each cold or warm start, we use **cached global instances**
 * (`cachedClient` and `cachedDb`) to persist connections between invocations, when possible.
 *
 * This improves:
 * - ✅ Performance: avoids redundant MongoDB connection overhead.
 * - ✅ Resource usage: minimizes open connections to MongoDB.
 * - ✅ Compatibility: avoids exceeding connection limits in short-lived environments.
 *
 * ## Usage
 * You **must call `connect()` once** before using `getCollection()`.
 *
 * Example:
 * ```ts
 * const driver = new LiveMongoDriver(process.env.MONGODB_URI, "myDatabase");
 * await driver.connect(); // Ensure connection is established
 * const users = driver.getCollection("users");
 * ```
 *
 * ## Safety
 * - Internally, `connect()` uses `ping` to check if the connection is live, avoiding reliance on deprecated APIs.
 * - On `close()`, global references are cleared for completeness, though `close()` is rarely needed in serverless.
 *
 * ## Notes
 * - This driver assumes a single MongoDB database instance.
 * - You can extend this logic to support multiple DBs or collections as needed.
 */


import { MongoClient, Collection, Document, Db } from "mongodb";
import { MongoDriver } from "./MongoDriver";

let cachedClient: MongoClient | null = null;
let cachedDb: Db | null = null;

class LiveMongoDriver implements MongoDriver {
  private client: MongoClient;
  private db: Db;
  private isConnected: boolean = false;

  constructor() {
    if (!cachedClient) {
      cachedClient = new MongoClient(process.env.MONGODB_URI!);
    }
    this.client = cachedClient;

    if (cachedDb) {
      this.db = cachedDb;
      this.isConnected = true;
    } else {
      this.db = this.client.db();
    }
  }

  async connect(): Promise<void> {
    if (!this.isConnected) {
      try {
        // Try a ping to see if client is connected
        await this.client.db().command({ ping: 1 });
      } catch {
        await this.client.connect();
      }


      console.log("Connect to MongoDB", Math.floor(Math.random() * 100) + 1);

      this.db = this.client.db(); // You can still pass dbName if needed
      cachedDb = this.db;
      this.isConnected = true;
    }
  }

  getCollection<T extends Document = Document>(name: string): Collection<T> {
    return this.db.collection<T>(name);
  }

  async close(): Promise<void> {
    if (this.isConnected) {
      await this.client.close();
      this.isConnected = false;
      cachedClient = null;
      cachedDb = null;
    }
  }
}

export const driver = new LiveMongoDriver()
driver.connect()