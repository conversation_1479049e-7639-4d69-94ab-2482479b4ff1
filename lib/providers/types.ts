// src/lib/providers/types.ts
export interface Provider {
  name: string;
  createSession?: () => Promise<string | void>;
  restartSession?: (sessionId: string) => Promise<string | void>;
  startSession?: (sessionId: string) => Promise<string | void>;
  stopSession?: (sessionId: string) => Promise<string | void>;
  logout: (sessionId?: string) => Promise<string | void>;
  getQr: (sessionId?: string) => Promise<string>;
  listDevices: () => Promise<any>;
  listChats: (
    sessionId?: string,
    limit?: number,
    offset?: number,
    filter?: string[]
  ) => Promise<any>;
  receiveMessages: (
    sessionId?: string,
    chatId?: string,
    limit?: number,
    offset?: number
  ) => Promise<any>;
  readMessage: (chatId: string, sessionId?: string) => Promise<string | void>;
  sendPresenceStatus: (
    presence: string,
    sessionId?: string
  ) => Promise<string | void>;
  sendMessage: (
    chatId: string, // reply_message_id Gowa
    text: string, // message Gowa
    session: string // phone Gowa
  ) => Promise<string | void>;
  sendTyping: (status: string, chatId: string, sessionId?: string) => Promise<string>;
}
