// src/lib/providers/index.ts
import { WahaProvider } from "./WahaProvider";
import { GowaProvider } from "./GowaProvider";
import { Provider } from "./types";

const WAHA_API_URL = process.env.WAHA_API_URL!;
const WAHA_API_KEY = process.env.WAHA_API_KEY!;
const GOWA_API_BASE = process.env.GOWA_API_BASE!;
const GOWA_USERNAME = process.env.GOWA_USERNAME!;
const GOWA_PASSWORD = process.env.GOWA_PASSWORD!;

export const providers: Record<string, Provider> = {
  waha: WahaProvider(WAHA_API_URL, WAHA_API_KEY),
  gowa: GowaProvider(GOWA_API_BASE, GOWA_USERNAME, GOWA_PASSWORD),
};
