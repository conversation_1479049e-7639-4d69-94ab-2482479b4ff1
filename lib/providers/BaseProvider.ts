// src/lib/providers/BaseProvider.ts
import axios, { AxiosInstance } from "axios";

export abstract class BaseProvider {
  public name: string;
  protected client: AxiosInstance;

  constructor(name: string, baseURL: string) {
    this.name = name;
    this.client = axios.create({
      baseURL,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  /**
   * Membuat session baru jika perlu.
   * Kalau provider tidak butuh session, TIDAK WAJIB implement.
   */
  abstract createSession?(): Promise<string | void>;
  abstract restartSession?(sessionId: string): Promise<string | void>;
  abstract startSession?(sessionId: string): Promise<string | void>;
  abstract stopSession?(sessionId: string): Promise<string | void>;

  /**
   * Mendapatkan QR code.
   */
  abstract getQr(sessionId?: string): Promise<string>;

  abstract logout(sessionId: string): Promise<string>;

  /**
   * List connected devices.
   */
  abstract listDevices(): Promise<any>;

  abstract listChats(
    sessionId?: string,
    limit?: number,
    offset?: number,
    filter?: string[]
  ): Promise<any>;

  abstract receiveMessages  (
    sessionId?: string,
    chatId?: string,
    limit?: number,
    offset?: number,
  ): Promise<any>;

  abstract readMessage(chatId: string, sessionId?: string): Promise<string>;

  abstract sendPresenceStatus(presence: string, sessionId?: string): Promise<string | void>;

  abstract sendPresenceStatus(
    chatId: string, // reply_message_id Gowa
    text: string, // message Gowa
    session: string // phone Gowa
  ): Promise<string | void>;

  abstract sendTyping(status: string, chatId: string, sessionId?: string): Promise<string>;

}
