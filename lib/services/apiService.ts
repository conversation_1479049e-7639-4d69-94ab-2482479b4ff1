import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import { BaseAPI } from "./baseApi";

const BASE_URL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1`;

export class ResponseWrapper<T> {
  constructor(
    public status: "success" | "failed",
    public data?: T,
    public error?: string[],
    public errorCodes?: string[],
    public messages: string[] = []
  ) { }
}

// 🧠 JSON Reviver to auto-convert ISO 8601 strings to Date objects
function jsonDateReviver(key: string, value: any) {
  const isLikelyDate = typeof value === "string" && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.*Z$/.test(value);
  if (isLikelyDate) {
    const parsed = new Date(value);
    if (!isNaN(parsed.getTime())) return parsed;
  }
  return value;
}

export class ApiService {
  private static instance: ApiService;

  private constructor() { }

  // Deprecated: use API.request() instead
  static __getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }

    return ApiService.instance;
  }

  // Deprecated: use API.request() instead
  async __request<T>(api: BaseAPI): Promise<T> {
    if (api.devMock) {
      return api.devMock;
    }

    try {
      const response: AxiosResponse = await axios({ ...this.buildAxiosRequestConfig(api), transformResponse: (res) => JSON.parse(res, jsonDateReviver) });
      const responseBody: ResponseWrapper<T> = response.data;

      if (responseBody.status === "success") {
        return responseBody.data!;
      } else {
        const firstError = responseBody.error?.[0] || "Unknown error";
        throw new Error(firstError);
      }
    } catch (error) {
      throw new Error(this.getErrorString(error, "Error in API Request"));
    }
  }

  getErrorString(error: any, defaultError: string): string {
    if (axios.isAxiosError(error)) {
      const apiError = error.response?.data?.error;
      if (Array.isArray(apiError) && apiError.length > 0) {
        return apiError[0];
      }
    }
    return defaultError;
  }

  private buildAxiosRequestConfig(api: BaseAPI): AxiosRequestConfig {
    return {
      method: api.method,
      url: `${BASE_URL}${api.url}`,
      data: api.getRequestBody(),
      headers: api.headers()
    };
  }
}
