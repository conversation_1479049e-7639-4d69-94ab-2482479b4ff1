export interface PagingAndSearch<T extends Record<string, string | boolean | string[] | undefined>> {
  page: number
  per_page?: number
  search?: string
  sorts: { field: string; direction: 'asc' | 'desc' }[]
  filters?: { field: string; value: string | string[] | boolean }[]
}

export namespace PagingAndSearch {
  export function toRecordString<T extends Record<string, string | boolean | string[] | undefined>>(
    p: PagingAndSearch<T>
  ): Record<string, string> {
    const query: Record<string, string> = {
      page: p.page.toString(),
      per_page: (p.per_page ?? 20).toString(),
    }

    if (p.search) {
      query.search = p.search
    }

    // Convert sorts array to a string like "field1:asc,field2:desc"
    if (p.sorts && p.sorts.length > 0) {
      query.sorts = p.sorts.map(s => `${s.field}:${s.direction}`).join(',')
    }

    // Convert filters to key-value string pairs
    if (p.filters) {
      for (const filter of p.filters) {
        if (filter.value !== undefined) {
          query[filter.field] = Array.isArray(filter.value)
            ? filter.value.join(',')
            : filter.value.toString()
        }
      }
    }

    return query
  }
}


export interface PaginatedResponse<T> {
  items: T[]
  page: number
  total: number
}