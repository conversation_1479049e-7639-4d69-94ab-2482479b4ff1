import crypto from "crypto";

// ID Generation
export function generateId(): string {
  return crypto.randomUUID();
}

// Date utilities
export function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

export function formatDateTime(date: Date): string {
  return date.toISOString();
}

export function parseDate(dateString: string): Date {
  return new Date(dateString);
}

// Query building utilities
export function buildSearchQuery(search: string, fields: string[]): any {
  if (!search) return {};
  
  const regex = new RegExp(search, "i");
  return {
    $or: fields.map(field => ({ [field]: { $regex: regex } }))
  };
}

export function buildPaginationQuery(page?: number, pageSize?: number): { skip?: number; limit?: number } {
  if (!page || !pageSize) return {};
  
  return {
    skip: (page - 1) * pageSize,
    limit: pageSize
  };
}

// MongoDB document mapping utilities
export function mapToEntity<T>(doc: any): T {
  const { _id, ...rest } = doc;
  return rest as T;
}

export function removeMongoId(doc: any): any {
  const { _id, ...rest } = doc;
  return rest;
}

// Validation utilities
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone);
}

// Error handling utilities
export function createError(message: string, code?: string, details?: any): Error {
  const error = new Error(message);
  (error as any).code = code;
  (error as any).details = details;
  return error;
}

// Response wrapper utilities
export function createSuccessResponse<T>(data: T): { status: "success"; data: T } {
  return {
    status: "success",
    data
  };
}

export function createErrorResponse(errors: string[], errorCodes?: string[]): { 
  status: "failed"; 
  errors: string[]; 
  errorCodes?: string[] 
} {
  return {
    status: "failed",
    errors,
    errorCodes
  };
}
