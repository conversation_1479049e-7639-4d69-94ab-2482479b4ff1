/**
 * Converts LaTeX delimiters to dollar sign notation that can be processed by remark-math
 *
 * Converts:
 * - $$...$$ to $...$
 * - \[...\] to $$...$$
 * - \(...\) to $...$
 */
export function convertLatexDelimiters(content: string): string {
  // Replace inline math delimiters \( ... \) with $ ... $
  let processedContent = content.replace(/\\\(([^]*?)\\\)/g, (_, formula) => {
    return `$${formula}$`;
  });

  // Replace block math delimiters \[ ... \] with $$ ... $$
  processedContent = processedContent.replace(/\\\[([^\]]*?)\\\]/g, (_, formula) => {
    return `$$${formula}$$`;
  });

  // Replace inline math delimiters $$...$$ with $...$
  processedContent = processedContent.replace(/\$\$([^]*?)\$\$/g, (_, formula) => {
    return `$${formula}$`;
  });

  return processedContent;
}
