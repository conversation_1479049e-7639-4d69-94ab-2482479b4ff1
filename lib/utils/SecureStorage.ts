export enum StorageKeys {
  UserToken = "UserToken",
  RefreshToken = "RefreshToken",
  CurrentOrganization = "CurrentOrganization",
}

class SecureStorage {
  private static instance: SecureStorage

  private constructor() {
    // Private constructor to prevent instantiation
  }

  public static getInstance(): SecureStorage {
    if (!SecureStorage.instance) {
      SecureStorage.instance = new SecureStorage()
    }

    return SecureStorage.instance
  }

  public setItem(key: StorageKeys, value: string): void {
    try {
      localStorage.setItem(key, value)
    } catch (error) {
      console.error(`Error setting item in Secure Storage (${key}):`, error)
      throw error
    }
  }

  public getItem(key: StorageKeys): string | null {
    try {
      const value = localStorage.getItem(key)
      return value
    } catch (error) {
      console.error(`Error getting item from Secure Storage (${key}):`, error)
      throw error
    }
  }

  // Add other secure storage methods as needed

  public removeItem(key: StorageKeys): void {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error(`Error removing item from Secure Storage (${key}):`, error)
      throw error
    }
  }
}

export const secureStorage = SecureStorage.getInstance()
