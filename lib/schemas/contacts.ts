import { z } from "zod";

export const ContactCreateSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string().min(1, "Phone number is required"),
  email: z.string().email("Invalid email format").optional(),
  tags: z.array(z.string()).optional(),
  notes: z.array(z.object({
    text: z.string().min(1, "Note text cannot be empty"),
    createdAt: z.string(),
  })).optional(),
});

export const ContactUpdateSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  phone: z.string().min(1, "Phone number is required").optional(),
  email: z.string().email("Invalid email format").optional(),
  tags: z.array(z.string()).optional(),
  notes: z.array(z.object({
    text: z.string().min(1, "Note text cannot be empty"),
    createdAt: z.string(),
  })).optional(),
});

export const ContactIdSchema = z.object({
  id: z.string().min(1, "ID is required"),
});

export type ContactCreateInput = z.infer<typeof ContactCreateSchema>;
export type ContactUpdateInput = z.infer<typeof ContactUpdateSchema>;
export type ContactIdInput = z.infer<typeof ContactIdSchema>;
