import { z } from "zod";

// Contact validation schemas
export const ContactCreateSchema = z.object({
  name: z.string()
    .min(1, "Name is required")
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters")
    .trim(),
  
  phone: z.string()
    .min(1, "Phone number is required")
    .min(10, "Phone number must be at least 10 digits")
    .max(20, "Phone number must be less than 20 characters")
    .regex(/^[\d+\-\s()]+$/, "Phone number contains invalid characters"),
  
  email: z.string()
    .email("Invalid email format")
    .max(255, "Email must be less than 255 characters")
    .optional()
    .or(z.literal("")),
  
  tags: z.array(z.string().trim().min(1))
    .max(10, "Maximum 10 tags allowed")
    .optional(),
  
  notes: z.array(z.object({
    text: z.string().trim().min(1, "Note text cannot be empty")
  }))
    .max(20, "Maximum 20 notes allowed")
    .optional(),
});

export const ContactUpdateSchema = z.object({
  name: z.string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters")
    .trim()
    .optional(),
  
  phone: z.string()
    .min(10, "Phone number must be at least 10 digits")
    .max(20, "Phone number must be less than 20 characters")
    .regex(/^[\d+\-\s()]+$/, "Phone number contains invalid characters")
    .optional(),
  
  email: z.string()
    .email("Invalid email format")
    .max(255, "Email must be less than 255 characters")
    .optional()
    .or(z.literal("")),
  
  tags: z.array(z.string().trim().min(1))
    .max(10, "Maximum 10 tags allowed")
    .optional(),
  
  notes: z.array(z.object({
    text: z.string().trim().min(1, "Note text cannot be empty"),
    createdAt: z.string().datetime("Invalid date format")
  }))
    .max(20, "Maximum 20 notes allowed")
    .optional(),
});

export const ContactSearchSchema = z.object({
  keyword: z.string()
    .min(1, "Search keyword is required")
    .max(100, "Search keyword must be less than 100 characters")
    .trim(),
});

export const ContactPaginationSchema = z.object({
  limit: z.number()
    .int("Limit must be an integer")
    .min(1, "Limit must be at least 1")
    .max(100, "Limit must be at most 100")
    .optional(),
  
  offset: z.number()
    .int("Offset must be an integer")
    .min(0, "Offset must be non-negative")
    .optional(),
  
  sortBy: z.enum(['name', 'createdAt', 'updatedAt'])
    .optional(),
  
  sortOrder: z.enum(['asc', 'desc'])
    .optional(),
  
  filters: z.object({
    tags: z.array(z.string().trim().min(1))
      .optional(),
    
    hasEmail: z.boolean()
      .optional(),
    
    createdAfter: z.string()
      .datetime("Invalid date format")
      .transform(str => new Date(str))
      .optional(),
    
    createdBefore: z.string()
      .datetime("Invalid date format")
      .transform(str => new Date(str))
      .optional(),
  }).optional(),
});

// Type exports
export type ContactCreateInput = z.infer<typeof ContactCreateSchema>;
export type ContactUpdateInput = z.infer<typeof ContactUpdateSchema>;
export type ContactSearchInput = z.infer<typeof ContactSearchSchema>;
export type ContactPaginationInput = z.infer<typeof ContactPaginationSchema>;
