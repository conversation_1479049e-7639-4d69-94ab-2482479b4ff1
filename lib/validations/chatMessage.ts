import { z } from "zod";

export const ChatMessageCreateSchema = z.object({
  chatId: z.string().min(1, "Chat ID is required"),
  senderId: z.string().min(1, "Sender ID is required"),
  content: z.string().min(1, "Message content is required"),
  sentAt: z.date().optional(), // Optional, or default to new Date() in backend
});

export const ChatMessageUpdateSchema = z.object({
  content: z.string().min(1, "Message content is required").optional(),
  updatedBy: z.string().min(1, "Updated by is required").optional(),
  updatedAt: z.date().optional(),
});

export const ChatMessageIdSchema = z.object({
  id: z.string().min(1, "Message ID is required"),
});

export type ChatMessageCreateInput = z.infer<typeof ChatMessageCreateSchema>;
export type ChatMessageUpdateInput = z.infer<typeof ChatMessageUpdateSchema>;
export type ChatMessageIdInput = z.infer<typeof ChatMessageIdSchema>;
