import { z } from "zod";

export const ChatCreateSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  messages: z.array(z.string().min(1, "Message cannot be empty")).min(1, "At least one message is required"),
  participants: z.array(z.string().min(1, "Participant ID cannot be empty")).min(1, "At least one participant is required"),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional().default(true),
  createdBy: z.string().min(1, "Created by is required"),
});

export const ChatUpdateSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().optional(),
  messages: z.array(z.string().min(1, "Message cannot be empty")).min(1, "At least one message is required").optional(),
  participants: z.array(z.string().min(1, "Participant ID cannot be empty")).min(1, "At least one participant is required").optional(),
  tags: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  updatedBy: z.string().min(1, "Updated by is required").optional(),
});

export const ChatIdSchema = z.object({
  id: z.string().min(1, "Chat ID is required"),
});

export type ChatCreateInput = z.infer<typeof ChatCreateSchema>;
export type ChatUpdateInput = z.infer<typeof ChatUpdateSchema>;
export type ChatIdInput = z.infer<typeof ChatIdSchema>;
