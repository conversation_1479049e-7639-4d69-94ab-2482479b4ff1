import { z } from "zod";

// Base validation schemas
const titleSchema = z.string()
  .trim()
  .min(1, "Title is required")
  .max(200, "Title must be less than 200 characters");

const contentSchema = z.string()
  .trim()
  .min(1, "Content is required")
  .max(10000, "Content must be less than 10,000 characters");

const categorySchema = z.string()
  .trim()
  .min(1, "Category cannot be empty")
  .max(100, "Category must be less than 100 characters")
  .optional();

const tagsSchema = z.array(z.string().trim())
  .max(10, "Maximum 10 tags allowed")
  .optional();

const variablesSchema = z.array(z.string().trim())
  .max(50, "Maximum 50 variables allowed")
  .optional();

const isActiveSchema = z.boolean().optional();

const createdBySchema = z.string()
  .trim()
  .min(1, "Created by is required");

const updatedBySchema = z.string()
  .trim()
  .min(1, "Updated by is required")
  .optional();

// Create message template schema
export const MessageTemplateCreateSchema = z.object({
  title: titleSchema,
  content: contentSchema,
  category: categorySchema,
  tags: tagsSchema,
  variables: variablesSchema,
  isActive: isActiveSchema,
  createdBy: createdBySchema,
});

// Update message template schema
export const MessageTemplateUpdateSchema = z.object({
  title: titleSchema.optional(),
  content: contentSchema.optional(),
  category: categorySchema,
  tags: tagsSchema,
  variables: variablesSchema,
  isActive: isActiveSchema,
  updatedBy: updatedBySchema,
}).refine(
  (data) => Object.keys(data).length > 0,
  {
    message: "At least one field must be provided for update",
  }
);

// Search/query schema
export const MessageTemplateQuerySchema = z.object({
  search: z.string().trim().min(1, "Search keyword cannot be empty").optional(),
  tag: z.string().trim().min(1, "Tag cannot be empty").optional(),
  category: z.string().trim().min(1, "Category cannot be empty").optional(),
  isActive: z.boolean().optional(),
  includeDeleted: z.boolean().optional(),
  page: z.number().int().min(1, "Page must be at least 1").optional(),
  limit: z.number().int().min(1, "Limit must be at least 1").max(100, "Limit cannot exceed 100").optional(),
  sortBy: z.enum(["title", "createdAt", "updatedAt", "category"]).optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

// Template rendering schema
export const TemplateRenderSchema = z.object({
  templateId: z.string().trim().min(1, "Template ID is required"),
  data: z.record(z.string(), z.string()).refine(
    (data) => Object.keys(data).length > 0,
    {
      message: "Template data cannot be empty",
    }
  ),
});

export type MessageTemplateCreateInput = z.infer<typeof MessageTemplateCreateSchema>;
export type MessageTemplateUpdateInput = z.infer<typeof MessageTemplateUpdateSchema>;
export type MessageTemplateQueryInput = z.infer<typeof MessageTemplateQuerySchema>;
export type TemplateRenderInput = z.infer<typeof TemplateRenderSchema>;
