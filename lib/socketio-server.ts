// lib/socketio-server.ts
// Socket.IO server implementation for realtime communication

import { Server as SocketIOServer } from 'socket.io';
import { createServer } from 'http';

interface SocketIOMessage {
  type: 'trigger' | 'join-channel' | 'leave-channel';
  channel?: string;
  event?: string;
  data?: any;
}

class RealtimeSocketIOServer {
  private io: SocketIOServer;
  private httpServer: any;
  private channels: Map<string, Set<string>> = new Map(); // channel -> socket IDs
  private socketChannels: Map<string, Set<string>> = new Map(); // socket ID -> channels

  constructor(port: number = 3001, corsOptions?: any) {
    this.httpServer = createServer();
    this.io = new SocketIOServer(this.httpServer, {
      cors: corsOptions || {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    this.setupServer();
    this.httpServer.listen(port, () => {
      console.log(`Socket.IO server running on port ${port}`);
    });
  }

  private setupServer(): void {
    this.io.on('connection', (socket) => {
      console.log(`Socket.IO client connected: ${socket.id}`);
      this.socketChannels.set(socket.id, new Set());

      // Handle channel joining
      socket.on('join-channel', (channelName: string) => {
        this.joinChannel(socket.id, channelName);
        socket.join(channelName);
        console.log(`Socket ${socket.id} joined channel: ${channelName}`);
      });

      // Handle channel leaving
      socket.on('leave-channel', (channelName: string) => {
        this.leaveChannel(socket.id, channelName);
        socket.leave(channelName);
        console.log(`Socket ${socket.id} left channel: ${channelName}`);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`Socket.IO client disconnected: ${socket.id}`);
        this.handleDisconnection(socket.id);
      });

      // Handle errors
      socket.on('error', (error) => {
        console.error(`Socket.IO error for ${socket.id}:`, error);
      });

      // Send connection confirmation
      socket.emit('connected', {
        socket_id: socket.id,
        timestamp: new Date().toISOString()
      });
    });
  }

  private joinChannel(socketId: string, channelName: string): void {
    // Add socket to channel
    if (!this.channels.has(channelName)) {
      this.channels.set(channelName, new Set());
    }
    this.channels.get(channelName)!.add(socketId);

    // Track channels for this socket
    this.socketChannels.get(socketId)!.add(channelName);
  }

  private leaveChannel(socketId: string, channelName: string): void {
    // Remove socket from channel
    const channelSockets = this.channels.get(channelName);
    if (channelSockets) {
      channelSockets.delete(socketId);
      if (channelSockets.size === 0) {
        this.channels.delete(channelName);
      }
    }

    // Remove channel from socket tracking
    this.socketChannels.get(socketId)?.delete(channelName);
  }

  private handleDisconnection(socketId: string): void {
    // Remove socket from all channels
    const socketChannelSet = this.socketChannels.get(socketId);
    if (socketChannelSet) {
      socketChannelSet.forEach(channelName => {
        this.leaveChannel(socketId, channelName);
      });
      this.socketChannels.delete(socketId);
    }
  }

  // Public method to broadcast messages (called from API routes)
  public broadcast(channel: string, event: string, data: any): void {
    const eventName = `${channel}:${event}`;
    
    // Emit to all sockets in the channel
    this.io.to(channel).emit(eventName, data);

    const channelSockets = this.channels.get(channel);
    const socketCount = channelSockets ? channelSockets.size : 0;
    
    console.log(`Broadcasted event "${eventName}" to ${socketCount} sockets in channel "${channel}"`);
  }

  // Get statistics about channels and connections
  public getChannelStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    this.channels.forEach((sockets, channel) => {
      stats[channel] = sockets.size;
    });
    return stats;
  }

  // Get total connected sockets
  public getConnectedSocketsCount(): number {
    return this.io.engine.clientsCount;
  }

  // Close the server
  public close(): void {
    this.io.close();
    this.httpServer.close();
  }
}

// Singleton instance
let socketIOServer: RealtimeSocketIOServer | null = null;

export const getSocketIOServer = (options?: {
  port?: number;
  corsOptions?: any;
}): RealtimeSocketIOServer => {
  if (!socketIOServer) {
    const port = options?.port || parseInt(process.env.SOCKETIO_PORT || '3001');
    const corsOptions = options?.corsOptions || {
      origin: process.env.SOCKETIO_CORS_ORIGIN || "*",
      methods: ["GET", "POST"]
    };
    socketIOServer = new RealtimeSocketIOServer(port, corsOptions);
  }
  return socketIOServer;
};

// Export for use in API routes
export { RealtimeSocketIOServer };
