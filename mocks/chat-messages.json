[{"id": "1", "sender": "customer", "content": "<PERSON><PERSON>, saya mengalami masalah dengan pesanan saya yang terakhir. Sehar<PERSON><PERSON> sampai kemarin, tapi sampai sekarang belum diterima.", "timestamp": "2025-06-14T03:30:00Z", "customerName": "<PERSON><PERSON>"}, {"id": "2", "sender": "agent", "content": "Halo Bu Sarah! Mohon maaf atas keterlambatan pengiriman pesanan Ibu. <PERSON><PERSON> akan bantu cek segera. <PERSON><PERSON> dibantu nomor pesanan Ibu?", "timestamp": "2025-06-14T03:32:00Z", "agentName": "<PERSON>", "agentId": "agen-1", "agentAvatar": "AT", "agentColor": "bg-blue-500"}, {"id": "3", "sender": "customer", "content": "Tentu, ini nomornya: #ORD-2024-001234. <PERSON><PERSON> pesan hari <PERSON> dan pilih pen<PERSON>man kilat.", "timestamp": "2025-06-14T03:33:00Z", "customerName": "<PERSON><PERSON>"}, {"id": "4", "sender": "agent", "content": "<PERSON><PERSON> kasih Bu sudah menginformasikan. <PERSON>a sudah cek <PERSON>, dan benar ada keterlambatan saat proses awal. <PERSON>a akan koordinasikan dengan pihak ekspedisi untuk memastikan status terbaru.", "timestamp": "2025-06-14T03:35:00Z", "agentName": "<PERSON>", "agentId": "agen-1", "agentAvatar": "AT", "agentColor": "bg-blue-500"}, {"id": "4-note", "sender": "supervisor", "type": "note", "content": "Pastikan untuk tawarkan kompensasi jika keterlambatan lebih dari 1 hari. Perhatikan nada bicara agar tetap ramah.", "timestamp": "2025-06-14T03:36:00Z", "supervisorName": "<PERSON>"}, {"id": "5", "sender": "agent", "content": "<PERSON><PERSON>, say<PERSON> <PERSON> dari bagian pen<PERSON>. <PERSON><PERSON> sudah bantu cek ya. Saya sudah hubungi pihak ekspedisi, dan paket Ibu saat ini sedang dalam perjalanan. Estimasi akan sampai dalam 2–3 jam ke depan.", "timestamp": "2025-06-14T03:38:00Z", "agentName": "<PERSON>", "agentId": "agen-2", "agentAvatar": "MC", "agentColor": "bg-green-500"}, {"id": "6", "sender": "customer", "content": "<PERSON>ah, kabar baik sekali! <PERSON><PERSON> kasih ya atas bantuannya, cepat sekali responnya.", "timestamp": "2025-06-14T03:40:00Z", "customerName": "<PERSON><PERSON>"}, {"id": "7", "sender": "agent", "content": "Sama-sama Bu! Se<PERSON>ai bentuk permohonan maaf kami atas keti<PERSON>a, kami berikan voucher diskon 10% untuk pesanan berikutnya. Kode: MAAF10, berlaku selama 30 hari ya Bu.", "timestamp": "2025-06-14T03:42:00Z", "agentName": "<PERSON>", "agentId": "agen-1", "agentAvatar": "AT", "agentColor": "bg-blue-500"}, {"id": "8", "sender": "<PERSON><PERSON><PERSON>", "content": "<PERSON> sedang mengetik...", "timestamp": "", "agentName": "<PERSON>", "agentId": "agen-2", "agentAvatar": "MC", "agentColor": "bg-green-500"}]