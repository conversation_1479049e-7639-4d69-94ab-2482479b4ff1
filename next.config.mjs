/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    WAHA_API_URL: process.env.WAHA_API_URL,
    WAHA_API_KEY: process.env.WAHA_API_KEY,
    
    GOWA_API_BASE: process.env.GOWA_API_BASE,
    GOWA_USERNAME: process.env.GOWA_USERNAME,
    GOWA_PASSWORD: process.env.GOWA_PASSWORD,

    PUSHER_APP_ID: process.env.PUSHER_APP_ID,
    PUSHER_KEY: process.env.PUSHER_KEY,
    PUSHER_SECRET: process.env.PUSHER_SECRET,
    PUSHER_CLUSTER: process.env.PUSHER_CLUSTER,

    WEBHOOKS_URL: process.env.WEBHOOKS_URL,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
};

export default nextConfig;
