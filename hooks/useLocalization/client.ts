// src/hooks/useLocalization/client.ts
import { useLocalizationContext } from "./localization-context"

type LocaleStrings = {
  [key: string]: any | { [key: string]: any }
}

type LocalizationData = {
  [locale: string]: LocaleStrings
}

declare global {
  interface Window {
    __missingTranslations__?: Record<string, Record<string, string>>
  }
}

export function useLocalization(namespace: string, locales: LocalizationData) {
  const { locale } = useLocalizationContext()

  const t = (key: string, template?: Record<string, string | number>): string => {
    const keys = key.split(".")
    let current: any = locales[locale] || {}

    for (const k of keys) {
      current = current[k]
      if (current === undefined || current === null) {
        if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
          window.__missingTranslations__ ||= {}
          window.__missingTranslations__[namespace] ||= {}
          window.__missingTranslations__[namespace][key] ||= ""
        }
        return key // fallback
      }
    }

    if (typeof current === "string") {
      if (template) {
        return current.replace(/\{\{(\w+)\}\}/g, (_, match) => {
          return template[match]?.toString() ?? `{${match}}`
        })
      }
      return current
    }

    return key
  }

  return { t, locale }
}
