import { useState, useCallback } from "react";
import { api } from "@/lib/axios";

interface SendTypingParams {
  status: "start" | "stop";
  chatId: string;
  session?: string;
  provider?: string;
}

interface SendTypingResponse {
  success: boolean;
  result: any;
  session: string;
  provider: string;
}

export function useSendTyping() {
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<SendTypingResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const sendTyping = useCallback(async (params: SendTypingParams) => {
    setLoading(true);
    setError(null);

    try {
      const { data } = await api.post<SendTypingResponse>("/messages/send-typing", params);
      if (data.success) {
        setResponse(data);
      } else {
        setError("Gagal mengirim status typing.");
      }
    } catch (err: any) {
      console.error("Error sending typing status:", err);
      setError(err?.response?.data?.error || "<PERSON><PERSON><PERSON><PERSON> k<PERSON>.");
    } finally {
      setLoading(false);
    }
  }, []);

  return { sendTyping, loading, response, error };
}
