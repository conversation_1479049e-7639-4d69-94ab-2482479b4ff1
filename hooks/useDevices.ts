import { useEffect, useState, useCallback } from "react";
import { api } from "@/lib/axios";

export interface Device {
  me: {
    pushName:string
  };
  id: string;
  name: string;
  platform: string;
  status: string;
}

export function useDevices() {
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchDevices = useCallback(async () => {
    setLoading(true);
    try {
      const { data } = await api.get<{ success: boolean; devices: Device[] }>("/devices");
      if (data.success) {
        setDevices(data.devices || []);
      }
    } catch (err) {
      console.error("Error fetching devices:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchDevices();
  }, [fetchDevices]);

  return { devices, loading, fetchDevices };
}
