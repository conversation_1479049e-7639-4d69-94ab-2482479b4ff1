import { api } from "@/lib/axios";
import { useState, useEffect } from "react";

export interface Message {
  status_presence: any;
  isTyping: boolean;
  sender: string;
  body: string;
  ack: number;
  _data: any;
  fromMe: any;
  id: string;
}

interface UseReceiveMessagesOptions {
  provider?: string;
  chatId: string;
}

const PAGE_SIZE = 10;

export function useReceiveMessages({
  provider,
  chatId,
}: UseReceiveMessagesOptions) {
  const [messages, setMessages] = useState<Message[] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLastChats, setIsLastChats] = useState(false);
  const [page, setPage] = useState(0);

  const fetchMessages = async () => {
    setLoading(true);
    setError(null);

    const params = new URLSearchParams();
    const offset = page * PAGE_SIZE;

    if (provider) params.append("provider", provider);
    if (chatId !== undefined) params.append("chatId", String(chatId));
    if (PAGE_SIZE !== undefined) params.append("limit", String(PAGE_SIZE));
    if (offset !== undefined) params.append("offset", String(offset));

    try {
      const { data } = await api.get<{
        success: boolean;
        messages?: Message[];
        error?: string;
      }>(`/messages/receive?${params.toString()}`, {});

      if (!data.success) throw new Error(data.error);

      if (data.messages) {
        setMessages((prev: any) =>
          page == 0
            ? data.messages || []
            : [...(data.messages || []), ...(prev || [])]
        );
        setIsLastChats(data?.messages?.length <= 0);
      }
    } catch (err: any) {
      setError(err.message);
      setMessages(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!chatId) return;
    fetchMessages();
  }, [chatId, page]);

  return {
    messages,
    isLastChats,
    page,
    setPage,
    loading,
    error,
    refetch: fetchMessages,
  };
}
