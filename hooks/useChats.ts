import { Dispatch, SetStateAction, useEffect, useState } from "react";
import axios from "axios";
import { api } from "@/lib/axios";
import { usePusherSocketId } from "./usePusherSocketId";

export type Chat = any; // Ganti dengan tipe data chat sebenarnya jika ada

interface UseChatsOptions {
  provider?: string;
  filter?: string[];
}

interface UseChatsResponse {
  data: Chat[] | null;
  setData: Dispatch<SetStateAction<any[] | null>>;
  loading: boolean;
  error: string | null;
  provider?: string;
  session?: string;
  refetch: () => void;
  isLastChats: boolean;
  page: number;
  setPage: Dispatch<SetStateAction<number>>;
}

const PAGE_SIZE = 8;

export function useChats(options: UseChatsOptions = {}): UseChatsResponse {
  const [data, setData] = useState<Chat[] | null>(null);
  const [provider, setProvider] = useState<string>();
  const [session, setSession] = useState<string>();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const [isLastChats, setIsLastChats] = useState(false);
  const [page, setPage] = useState(0);

  const fetchChats = async () => {
    setLoading(true);
    setError(null);

    const params = new URLSearchParams();
    const offset = page * PAGE_SIZE;

    if (options.provider) params.append("provider", options.provider);
    if (PAGE_SIZE !== undefined) params.append("limit", String(PAGE_SIZE));
    if (offset !== undefined) params.append("offset", String(offset));
    if (options.filter !== undefined) params.append("filter", String([""]));

    try {
      const response = await api.get<{
        success: boolean;
        chats: Chat[];
        provider: string;
        session: string;
      }>(`/chats?${params.toString()}`);
      const json = response.data;

      if (json.chats) {
        setData((prev: any) =>
          page == 0
            ? json.chats || []
            : [...(prev || []), ...(json.chats || [])]
        );
        setIsLastChats(json.chats?.length <= 0);
      }
      setProvider(json.provider);
      setSession(json.session);
    } catch (err: any) {
      const message =
        err.response?.data?.error || err.message || "Failed to fetch chats";
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchChats();
  }, [page]);

  return {
    data,
    setData,
    loading,
    error,
    provider,
    session,
    refetch: fetchChats,
    isLastChats,
    page,
    setPage,
  };
}
