"use client";

import { useState } from "react";
import { api } from "@/lib/axios";

type SendMessageParams = {
  chatId: string;
  text: string;
  session?: string;
  provider?: string;
};

type SendMessageResponse = {
  success: boolean;
  provider?: string;
  session?: string;
  result?: any;
  error?: string;
};

export function useSendMessage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [response, setResponse] = useState<SendMessageResponse | null>(null);

  const sendMessage = async ({
    chatId,
    text,
    session,
    provider,
  }: SendMessageParams): Promise<SendMessageResponse> => {
    setLoading(true);
    setError(null);

    try {
      const res = await api.post<SendMessageResponse>("/messages/send", {
        chatId,
        text,
        session,
        provider,
      });

      const data = res.data;
      setResponse(data);

      if (!data.success) {
        setError(data.error || "Gagal mengirim pesan.");
      }

      return data;
    } catch (err: any) {
      const errorMsg =
        err.response?.data?.error || err.message || "Terjadi kesalahan saat mengirim pesan.";
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    sendMessage,
    loading,
    error,
    response,
  };
}
