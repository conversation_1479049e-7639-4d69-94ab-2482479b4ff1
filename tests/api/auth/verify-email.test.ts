//@ts-ignore
import { beforeEach, describe, expect, it, test } from 'bun:test';
import { implHandleVerifyEmail } from '@/app/api/v1/auth/verify-email/impl';
import { MongoAuthDBRepository } from '@/lib/repositories/auth';
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface";
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic";
import type { UserRegister } from "@/lib/types/base";
import { InMemoryMongoDriver } from '@/tests/InMemoryMongoDriver';
import { TestAuthDBRepositoryWrapper } from './TestDBRepositoryWrapper';

describe("Verify Email API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface;
  let dbRepository: TestAuthDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth");
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver);
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver);
    businessLogic = new AuthBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("POST /api/v1/auth/verify-email", () => {
    it("should successfully verify email with valid token", async () => {
      // Setup: Register a user (creates email verification token)
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Verify User"
      };

      await businessLogic.register(userData);

      // Get verification token from in-memory storage
      const verificationToken = await dbRepository.getFirstEmailVerificationToken() || "";

      // Test: Verify email
      const verifyEmailData = {
        token: verificationToken
      };

      const result = await implHandleVerifyEmail(verifyEmailData, businessLogic);

      // Verify API response
      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.message).toContain("Email verified successfully");

      // Verify user is now verified
      const user = await dbRepository.findUserByEmail("<EMAIL>");
      expect(user?.emailVerified).toBe(true);

      // Verify verification token was cleaned up
      expect(await dbRepository.getEmailVerificationCount()).toBe(0);
    });

    it("should fail verification with invalid token", async () => {
      const verifyEmailData = {
        token: "invalid-verification-token"
      };

      const result = await implHandleVerifyEmail(verifyEmailData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invalid or expired verification token");
    });

    it("should fail verification with missing token", async () => {
      const verifyEmailData = {};

      const result = await implHandleVerifyEmail(verifyEmailData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail verification with empty token", async () => {
      const verifyEmailData = {
        token: ""
      };

      const result = await implHandleVerifyEmail(verifyEmailData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail verification with expired token", async () => {
      // This test would require mocking time or creating an expired token
      // For now, we'll test with an invalid format that simulates expiry
      const verifyEmailData = {
        token: "expired.verification.token"
      };

      const result = await implHandleVerifyEmail(verifyEmailData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invalid or expired verification token");
    });

    it("should fail verification with already used token", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Used User"
      };

      await businessLogic.register(userData);

      // Get verification token
      const verificationToken = await dbRepository.getFirstEmailVerificationToken() || "";

      // First verification (should succeed)
      const verifyEmailData = {
        token: verificationToken
      };

      const result1 = await implHandleVerifyEmail(verifyEmailData, businessLogic);
      expect(result1.status).toBe(200);

      // Try to use same token again (should fail)
      const result2 = await implHandleVerifyEmail(verifyEmailData, businessLogic);
      expect(result2.status).toBe(400);
      expect(result2.body.error).toContain("Invalid or expired verification token");
    });

    it("should handle verification for already verified user", async () => {
      // Setup: Register and verify a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Already User"
      };

      await businessLogic.register(userData);

      // Get and use verification token
      const verificationToken = await dbRepository.getFirstEmailVerificationToken() || "";
      await implHandleVerifyEmail({ token: verificationToken }, businessLogic);

      // Verify user is verified
      const user = await dbRepository.findUserByEmail("<EMAIL>");
      expect(user?.emailVerified).toBe(true);

      // Try to verify again with invalid token (should fail gracefully)
      const result = await implHandleVerifyEmail({ token: "some-token" }, businessLogic);
      expect(result.status).toBe(400);
      expect(result.body.error).toContain("Invalid or expired verification token");
    });

    it("should maintain user data integrity during verification", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Integrity User"
      };

      const registerResult = await businessLogic.register(userData);
      const originalUser = registerResult.user;

      // Get verification token
      const verificationToken = await dbRepository.getFirstEmailVerificationToken() || "";

      // Verify email
      await implHandleVerifyEmail({ token: verificationToken }, businessLogic);

      // Check that only emailVerified changed
      const updatedUser = await dbRepository.findUserByEmail("<EMAIL>");
      expect(updatedUser?.id).toBe(originalUser.id);
      expect(updatedUser?.email).toBe(originalUser.email);
      expect(updatedUser?.name).toBe(originalUser.name);
      expect(updatedUser?.createdAt).toEqual(originalUser.createdAt);
      expect(updatedUser?.emailVerified).toBe(true); // This should be the only change
    });

    it("should handle multiple users with different verification tokens", async () => {
      // Setup: Register multiple users
      const user1: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "User 1"
      };

      const user2: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "User 2"
      };

      await businessLogic.register(user1);
      await businessLogic.register(user2);

      // Get verification tokens
      const tokens = await dbRepository.getFirstEmailVerificationToken() || "";
      expect(tokens.length).toBe(2);

      // Verify first user
      const result1 = await implHandleVerifyEmail({ token: tokens[0] }, businessLogic);
      expect(result1.status).toBe(200);

      // Verify second user
      const result2 = await implHandleVerifyEmail({ token: tokens[1] }, businessLogic);
      expect(result2.status).toBe(200);

      // Check both users are verified
      const verifiedUser1 = await dbRepository.findUserByEmail("<EMAIL>");
      const verifiedUser2 = await dbRepository.findUserByEmail("<EMAIL>");

      expect(verifiedUser1?.emailVerified).toBe(true);
      expect(verifiedUser2?.emailVerified).toBe(true);
      expect(await dbRepository.getEmailVerificationCount()).toBe(0);
    });

    it("should handle verification token case sensitivity", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Case User"
      };

      await businessLogic.register(userData);

      // Get verification token
      const verificationToken = await dbRepository.getFirstEmailVerificationToken() || "";

      // Try with different case (should fail if case sensitive)
      const verifyEmailData = {
        token: verificationToken.toUpperCase()
      };

      const result = await implHandleVerifyEmail(verifyEmailData, businessLogic);

      // Assuming tokens are case sensitive
      expect(result.status).toBe(400);
      expect(result.body.error).toContain("Invalid or expired verification token");
    });
  });
});
