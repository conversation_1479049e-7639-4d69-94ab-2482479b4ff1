//@ts-ignore
import { beforeEach, describe, expect, it, test } from 'bun:test';
import { implHandleDeleteAccount } from '@/app/api/v1/auth/delete-account/impl';
import { MongoAuthDBRepository } from '@/lib/repositories/auth';
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface";
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic";
import type { UserRegister } from "@/lib/types/base";
import { InMemoryMongoDriver } from '@/tests/InMemoryMongoDriver';
import { TestAuthDBRepositoryWrapper } from './TestDBRepositoryWrapper';

describe("Delete Account API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface;
  let dbRepository: TestAuthDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth");
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver);
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver);
    businessLogic = new AuthBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("POST /api/v1/auth/delete-account", () => {
    it("should successfully delete account with valid credentials", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Delete User"
      };

      const registerResult = await businessLogic.register(userData);
      const userId = registerResult.user.id;
      const token = registerResult.token;

      // Test: Delete account
      const deleteAccountData = {
        password: "password123",
        confirmDeletion: "DELETE" as const
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, userId);

      // Verify API response
      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.message).toContain("Account deleted successfully");

      // Verify user no longer exists
      const deletedUser = await dbRepository.findUserById(userId);
      expect(deletedUser).toBeNull();

      // Verify token no longer works
      const validatedUser = await businessLogic.validateToken(token);
      expect(validatedUser).toBeNull();

      // Verify user count decreased
      expect(await dbRepository.getUserCount()).toBe(0);
    });

    it("should fail delete account with wrong password", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "correctpassword",
        name: "Wrong Pass User"
      };

      const registerResult = await businessLogic.register(userData);

      // Test: Try to delete with wrong password
      const deleteAccountData = {
        password: "wrongpassword",
        confirmDeletion: "DELETE" as const
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, registerResult.user.id);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invalid password");

      // Verify user still exists
      const user = await dbRepository.findUserById(registerResult.user.id);
      expect(user).toBeTruthy();
    });

    it("should fail delete account with missing confirmDeletion", async () => {
      const deleteAccountData = {
        password: "password123"
        // Missing confirmDeletion
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, "user-123");

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail delete account with missing password", async () => {
      const deleteAccountData = {
        confirmDeletion: "DELETE" as const
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, "user-123");

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail delete account with non-existent user", async () => {
      const deleteAccountData = {
        password: "password123",
        confirmDeletion: "DELETE" as const
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, "non-existent-user-id");

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail delete account with empty fields", async () => {
      const deleteAccountData = {
        password: "",
        confirmDeletion: ""
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, "user-123");

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should delete all user-related data", async () => {
      // Setup: Register a user and create various tokens
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Cleanup User"
      };

      const registerResult = await businessLogic.register(userData);
      const userId = registerResult.user.id;

      // Login to create additional token
      await businessLogic.login({
        email: "<EMAIL>",
        password: "password123"
      });

      // Request password reset to create reset token
      await businessLogic.requestPasswordReset("<EMAIL>");

      // Verify data exists before deletion
      expect(await dbRepository.getUserCount()).toBe(1);
      expect(await dbRepository.getTokenCount()).toBe(2); // register + login tokens
      expect(await dbRepository.getPasswordResetCount()).toBe(1);
      expect(await dbRepository.getEmailVerificationCount()).toBe(1);

      // Delete account
      const deleteAccountData = {
        password: "password123",
        confirmDeletion: "DELETE" as const
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, userId);
      expect(result.status).toBe(200);

      // Verify all data is cleaned up
      expect(await dbRepository.getUserCount()).toBe(0);
      expect(await dbRepository.getTokenCount()).toBe(0);
      expect(await dbRepository.getPasswordResetCount()).toBe(0);
      expect(await dbRepository.getEmailVerificationCount()).toBe(0);
    });

    it("should not affect other users when deleting account", async () => {
      // Setup: Register two users
      const user1: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "User 1"
      };

      const user2: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "User 2"
      };

      const registerResult1 = await businessLogic.register(user1);
      const registerResult2 = await businessLogic.register(user2);

      // Verify both users exist
      expect(await dbRepository.getUserCount()).toBe(2);

      // Delete first user
      const deleteAccountData = {
        password: "password123",
        confirmDeletion: "DELETE" as const
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, registerResult1.user.id);
      expect(result.status).toBe(200);

      // Verify first user is deleted but second user remains
      expect(await dbRepository.getUserCount()).toBe(1);

      const deletedUser = await dbRepository.findUserById(registerResult1.user.id);
      const remainingUser = await dbRepository.findUserById(registerResult2.user.id);

      expect(deletedUser).toBeNull();
      expect(remainingUser).toBeTruthy();
      expect(remainingUser?.email).toBe("<EMAIL>");
    });

    it("should handle deletion of verified user", async () => {
      // Setup: Register and verify a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Verified User"
      };

      const registerResult = await businessLogic.register(userData);

      // Verify email
      const verificationToken = await dbRepository.getFirstEmailVerificationToken();
      expect(verificationToken).toBeDefined();

      await businessLogic.verifyEmail(verificationToken!);


      // Verify user is verified
      const verifiedUser = await dbRepository.findUserById(registerResult.user.id);
      expect(verifiedUser?.emailVerified).toBe(true);

      // Delete account
      const deleteAccountData = {
        password: "password123",
        confirmDeletion: "DELETE" as const
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, registerResult.user.id);
      expect(result.status).toBe(200);

      // Verify user is completely deleted
      const deletedUser = await dbRepository.findUserById(registerResult.user.id);
      expect(deletedUser).toBeNull();
    });

    it("should handle case sensitivity in password", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "Password123",
        name: "Case User"
      };

      const registerResult = await businessLogic.register(userData);

      // Test: Try to delete with different case password
      const deleteAccountData = {
        password: "password123", // Different case
        confirmDeletion: "DELETE" as const
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, registerResult.user.id);

      expect(result.status).toBe(400);
      expect(result.body.error).toContain("Invalid password");

      // Verify user still exists
      const user = await dbRepository.findUserById(registerResult.user.id);
      expect(user).toBeTruthy();
    });

    it("should handle deletion with special characters in password", async () => {
      // Setup: Register a user with special characters in password
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "P@ssw0rd!#$%",
        name: "Special User"
      };

      const registerResult = await businessLogic.register(userData);

      // Delete account with correct special character password
      const deleteAccountData = {
        password: "P@ssw0rd!#$%",
        confirmDeletion: "DELETE" as const
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, registerResult.user.id);
      expect(result.status).toBe(200);

      // Verify user is deleted
      const deletedUser = await dbRepository.findUserById(registerResult.user.id);
      expect(deletedUser).toBeNull();
    });
  });
});
