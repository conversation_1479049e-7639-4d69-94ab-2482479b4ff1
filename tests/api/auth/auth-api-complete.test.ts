//@ts-ignore
import { beforeEach, describe, expect, it, test } from 'bun:test';
import { implHandleChangePassword } from "@/app/api/v1/auth/change-password/impl";
import { implHandleDeleteAccount } from "@/app/api/v1/auth/delete-account/impl";
import { implHandleForgotPassword } from "@/app/api/v1/auth/forgot-password/impl";
import { implHandleLogin } from "@/app/api/v1/auth/login/impl";
import { implHandleLogout } from "@/app/api/v1/auth/logout/impl";
import { implHandleRegister } from "@/app/api/v1/auth/register/impl";
import { implHandleResendVerification } from "@/app/api/v1/auth/resend-verification/impl";
import { implHandleResetPassword } from "@/app/api/v1/auth/reset-password/impl";
import { implHandleVerifyEmail } from "@/app/api/v1/auth/verify-email/impl";
import { MongoAuthDBRepository } from "@/lib/repositories/auth";
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface";
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { TestAuthDBRepositoryWrapper } from "./TestDBRepositoryWrapper";

describe("Complete Authentication API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface;
  let dbRepository: TestAuthDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth");
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver);
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver);
    businessLogic = new AuthBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("Register API", () => {
    it("should register a new user successfully", async () => {
      const registerData = {
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      };

      const result = await implHandleRegister(registerData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.user.email).toBe(registerData.email);
      expect(result.body.data?.user.name).toBe(registerData.name);
      expect(result.body.data?.token).toBeDefined();
      expect(result.body.data?.refresh_token).toBeDefined();

      // Verify user was created in database
      const user = await dbRepository.findUserByEmail(registerData.email);
      expect(user).toBeTruthy();
      expect(user?.emailVerified).toBe(false);

      // Verify email verification token was created
      expect(await dbRepository.getEmailVerificationCount()).toBe(1);
    });

    it("should reject duplicate email registration", async () => {
      const registerData = {
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      };

      // Register first user
      await implHandleRegister(registerData, businessLogic);

      // Try to register with same email
      const result = await implHandleRegister(registerData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error?.[0]).toContain("already exists");
    });

    it("should validate registration input", async () => {
      const invalidData = {
        email: "invalid-email",
        password: "123", // Too short
        name: "Test User"
      };

      const result = await implHandleRegister(invalidData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toHaveLength(2); // Email and password errors
    });
  });

  describe("Login API", () => {
    beforeEach(async () => {
      // Create a test user
      await implHandleRegister({
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      }, businessLogic);
    });

    it("should login successfully with valid credentials", async () => {
      const loginData = {
        email: "<EMAIL>",
        password: "password123"
      };

      const result = await implHandleLogin(loginData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.user.email).toBe(loginData.email);
      expect(result.body.data?.token).toBeDefined();
      expect(result.body.data?.refresh_token).toBeDefined();
    });

    it("should reject invalid credentials", async () => {
      const loginData = {
        email: "<EMAIL>",
        password: "wrongpassword"
      };

      const result = await implHandleLogin(loginData, businessLogic);

      expect(result.status).toBe(401);
      expect(result.body.status).toBe("failed");
      expect(result.body.error?.[0]).toContain("Invalid");
    });
  });

  describe("Change Password API", () => {
    let userId: string;

    beforeEach(async () => {
      // Create and login a test user
      const registerResult = await implHandleRegister({
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      }, businessLogic);
      userId = registerResult.body.data.user.id;
    });

    it("should change password successfully", async () => {
      const changePasswordData = {
        currentPassword: "password123",
        newPassword: "newpassword123",
        confirmPassword: "newpassword123"
      };

      const result = await implHandleChangePassword(changePasswordData, businessLogic, userId);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.message).toContain("successfully");

      // Verify new password works
      const loginResult = await implHandleLogin({
        email: "<EMAIL>",
        password: "newpassword123"
      }, businessLogic);
      expect(loginResult.status).toBe(200);
    });

    it("should reject incorrect current password", async () => {
      const changePasswordData = {
        currentPassword: "wrongpassword",
        newPassword: "newpassword123",
        confirmPassword: "newpassword123"
      };

      const result = await implHandleChangePassword(changePasswordData, businessLogic, userId);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error?.[0]).toContain("incorrect");
    });

    it("should validate password confirmation", async () => {
      const changePasswordData = {
        currentPassword: "password123",
        newPassword: "newpassword123",
        confirmPassword: "differentpassword"
      };

      const result = await implHandleChangePassword(changePasswordData, businessLogic, userId);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toBeDefined();
    });
  });

  describe("Forgot Password API", () => {
    beforeEach(async () => {
      // Create a test user
      await implHandleRegister({
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      }, businessLogic);
    });

    it("should create password reset request", async () => {
      const forgotPasswordData = {
        email: "<EMAIL>"
      };

      const result = await implHandleForgotPassword(forgotPasswordData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.resetToken).toBeDefined();

      // Verify reset token was created
      expect(await dbRepository.getPasswordResetCount()).toBe(1);
    });

    it("should handle non-existent email gracefully", async () => {
      const forgotPasswordData = {
        email: "<EMAIL>"
      };

      const result = await implHandleForgotPassword(forgotPasswordData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      // Should not reveal if email exists or not
    });
  });

  describe("Reset Password API", () => {
    let resetToken: string;

    beforeEach(async () => {
      // Create a test user and request password reset
      await implHandleRegister({
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      }, businessLogic);

      const forgotResult = await implHandleForgotPassword({
        email: "<EMAIL>"
      }, businessLogic);
      resetToken = forgotResult.body.data.resetToken;
    });

    it("should reset password successfully", async () => {
      const resetPasswordData = {
        token: resetToken,
        newPassword: "newpassword123",
        confirmPassword: "newpassword123"
      };

      const result = await implHandleResetPassword(resetPasswordData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");

      // Verify new password works
      const loginResult = await implHandleLogin({
        email: "<EMAIL>",
        password: "newpassword123"
      }, businessLogic);
      expect(loginResult.status).toBe(200);

      // Verify reset token was cleaned up
      expect(await dbRepository.getPasswordResetCount()).toBe(0);
    });

    it("should reject invalid reset token", async () => {
      const resetPasswordData = {
        token: "invalid-token",
        newPassword: "newpassword123",
        confirmPassword: "newpassword123"
      };

      const result = await implHandleResetPassword(resetPasswordData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error?.[0]).toContain("Invalid");
    });
  });

  describe("Email Verification API", () => {
    let verificationToken: string;

    beforeEach(async () => {
      // Create a test user (which creates verification token)
      await implHandleRegister({
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      }, businessLogic);

      // Get verification token from in-memory storage
      verificationToken = await dbRepository.getFirstEmailVerificationToken() || "";
    });

    it("should verify email successfully", async () => {
      const verifyEmailData = {
        token: verificationToken
      };

      const result = await implHandleVerifyEmail(verifyEmailData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");

      // Verify user is now verified
      const user = await dbRepository.findUserByEmail("<EMAIL>");
      expect(user?.emailVerified).toBe(true);

      // Verify token was cleaned up
      expect(await dbRepository.getEmailVerificationCount()).toBe(0);
    });

    it("should reject invalid verification token", async () => {
      const verifyEmailData = {
        token: "invalid-token"
      };

      const result = await implHandleVerifyEmail(verifyEmailData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error?.[0]).toContain("Invalid");
    });
  });

  describe("Resend Verification API", () => {
    beforeEach(async () => {
      // Create a test user
      await implHandleRegister({
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      }, businessLogic);
    });

    it("should resend verification email", async () => {
      const resendData = {
        email: "<EMAIL>"
      };

      const result = await implHandleResendVerification(resendData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.verificationToken).toBeDefined();

      // Should still have verification token (old one replaced)
      expect(await dbRepository.getEmailVerificationCount()).toBe(1);
    });

    it("should reject already verified email", async () => {
      // First verify the email
      const verificationToken = await dbRepository.getFirstEmailVerificationToken() || "";
      await implHandleVerifyEmail({ token: verificationToken }, businessLogic);

      // Try to resend verification
      const resendData = {
        email: "<EMAIL>"
      };

      const result = await implHandleResendVerification(resendData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error?.[0]).toContain("already verified");
    });
  });

  describe("Delete Account API", () => {
    let userId: string;

    beforeEach(async () => {
      // Create a test user
      const registerResult = await implHandleRegister({
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      }, businessLogic);
      userId = registerResult.body.data.user.id;
    });

    it("should delete account successfully", async () => {
      const deleteAccountData = {
        password: "password123",
        confirmDeletion: "DELETE"
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, userId);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");

      // Verify user was deleted
      const user = await dbRepository.findUserByEmail("<EMAIL>");
      expect(user).toBeNull();

      // Verify all related data was cleaned up
      expect(await dbRepository.getTokenCount()).toBe(0);
      expect(await dbRepository.getEmailVerificationCount()).toBe(0);
    });

    it("should reject incorrect password", async () => {
      const deleteAccountData = {
        password: "wrongpassword",
        confirmDeletion: "DELETE"
      };

      const result = await implHandleDeleteAccount(deleteAccountData, businessLogic, userId);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invalid password");
    });
  });

  describe("Logout API", () => {
    let token: string;

    beforeEach(async () => {
      // Create and login a test user
      const registerResult = await implHandleRegister({
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      }, businessLogic);
      token = registerResult.body.data.token;
    });

    it("should logout successfully", async () => {
      const logoutData = {
        token: token
      };

      const result = await implHandleLogout(logoutData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");

      // Verify token was invalidated
      const user = await businessLogic.validateToken(token);
      expect(user).toBeNull();
    });
  });
});
