//@ts-ignore
import { beforeEach, describe, expect, it, test } from 'bun:test';
import { implHandleLogin } from "@/app/api/v1/auth/login/impl";
import { MongoAuthDBRepository } from '@/lib/repositories/auth';
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface";
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic";
import type { Login, UserRegister } from "@/lib/types/base";
import { InMemoryMongoDriver } from '@/tests/InMemoryMongoDriver';
import { TestAuthDBRepositoryWrapper } from './TestDBRepositoryWrapper';

describe("Login API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface;
  let dbRepository: TestAuthDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth");
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver);
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver);
    businessLogic = new AuthBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("POST /api/v1/auth/login", () => {
    it("should successfully login with valid credentials", async () => {
      // Setup: Register a user first
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Admin User"
      };

      await businessLogic.register(userData);

      // Test: Login with the registered user
      const loginData: Login = {
        email: "<EMAIL>",
        password: "password123"
      };

      const result = await implHandleLogin(loginData, businessLogic);

      // Verify API response format
      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?).toBeDefined();
      expect(result.body.data?.token).toBeDefined();
      expect(result.body.data?.refresh_token).toBeDefined();
      expect(result.body.data?.user).toBeDefined();

      // Verify user data in response
      expect(result.body.data?.user.email).toBe("<EMAIL>");
      expect(result.body.data?.user.name).toBe("Admin User");
      expect(result.body.data?.user.id).toBeDefined();
      expect(result.body.data?.user.createdAt).toBeDefined();

      // Verify token count increased
      expect(await dbRepository.getTokenCount()).toBe(2); // 1 from register + 1 from login
    });

    it("should fail login with invalid email", async () => {
      const loginData: Login = {
        email: "<EMAIL>",
        password: "password123"
      };

      const result = await implHandleLogin(loginData, businessLogic);

      expect(result.status).toBe(401);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invalid email or password");
    });

    it("should fail login with invalid password", async () => {
      // Setup: Register a user first
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Test User"
      };

      await businessLogic.register(userData);

      // Test: Login with wrong password
      const loginData: Login = {
        email: "<EMAIL>",
        password: "wrongpassword"
      };

      const result = await implHandleLogin(loginData, businessLogic);

      expect(result.status).toBe(401);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invalid email or password");
    });

    it("should fail login with missing email", async () => {
      const loginData = {
        password: "password123"
      } as Login;

      const result = await implHandleLogin(loginData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED");
    });

    it("should fail login with missing password", async () => {
      const loginData = {
        email: "<EMAIL>"
      } as Login;

      const result = await implHandleLogin(loginData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED");
    });

    it("should fail login with invalid email format", async () => {
      const loginData: Login = {
        email: "invalid-email",
        password: "password123"
      };

      const result = await implHandleLogin(loginData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED");
    });

    it("should fail login with empty credentials", async () => {
      const loginData: Login = {
        email: "",
        password: ""
      };

      const result = await implHandleLogin(loginData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED");
    });

    it("should handle multiple successful logins", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Multi Login User"
      };

      await businessLogic.register(userData);

      const loginData: Login = {
        email: "<EMAIL>",
        password: "password123"
      };

      // First login
      const result1 = await implHandleLogin(loginData, businessLogic);
      expect(result1.status).toBe(200);
      expect(result1.body.status).toBe("success");

      // Second login
      const result2 = await implHandleLogin(loginData, businessLogic);
      expect(result2.status).toBe(200);
      expect(result2.body.status).toBe("success");

      // Verify different tokens
      expect(result1.body.data.token).not.toBe(result2.body.data.token);
      expect(await dbRepository.getTokenCount()).toBe(3); // 1 from register + 2 from logins
    });
  });
});
