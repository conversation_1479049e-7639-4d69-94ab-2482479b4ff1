//@ts-ignore
import { beforeEach, describe, expect, it, test } from 'bun:test';
import { implHandleRefreshToken } from "@/app/api/v1/auth/refresh_token/impl";
import { MongoAuthDBRepository } from '@/lib/repositories/auth';
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface";
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic";
import type { RefreshTokenInput, UserRegister } from "@/lib/types/base";
import { InMemoryMongoDriver } from '@/tests/InMemoryMongoDriver';
import { TestAuthDBRepositoryWrapper } from './TestDBRepositoryWrapper';

describe("Refresh Token API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface;
  let dbRepository: TestAuthDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth");
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver);
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver);
    businessLogic = new AuthBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("POST /api/v1/auth/refresh_token", () => {
    it("should successfully refresh token with valid refresh token", async () => {
      // Setup: Register a user to get initial tokens
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Refresh User"
      };

      const registerResult = await businessLogic.register(userData);
      const originalRefreshToken = registerResult.refresh_token;

      // Test: Refresh the token
      const refreshData: RefreshTokenInput = {
        token: registerResult.token,
        refresh_token: originalRefreshToken
      };

      const result = await implHandleRefreshToken(refreshData, businessLogic);

      // Verify API response format
      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?).toBeDefined();
      expect(result.body.data?.token).toBeDefined();
      expect(result.body.data?.refresh_token).toBeDefined();
      expect(result.body.data?.user).toBeDefined();

      // Verify new tokens are different from original
      expect(result.body.data?.token).not.toBe(registerResult.token);
      expect(result.body.data?.refresh_token).not.toBe(originalRefreshToken);

      // Verify user data in response
      expect(result.body.data?.user.email).toBe("<EMAIL>");
      expect(result.body.data?.user.name).toBe("Refresh User");
      expect(result.body.data?.user.id).toBe(registerResult.user.id);

      // Verify new token is valid
      const validatedUser = await businessLogic.validateToken(result.body.data?.token);
      expect(validatedUser).toBeTruthy();
      expect(validatedUser?.email).toBe("<EMAIL>");
    });

    it("should fail refresh with invalid refresh token", async () => {
      const refreshData: RefreshTokenInput = {
        token: "some-token",
        refresh_token: "invalid-refresh-token"
      };

      const result = await implHandleRefreshToken(refreshData, businessLogic);

      expect(result.status).toBe(401);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invalid or expired refresh token");
    });

    it("should fail refresh with missing refresh token", async () => {
      const refreshData = {
        token: "some-token"
      } as RefreshTokenInput;

      const result = await implHandleRefreshToken(refreshData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED");
    });

    it("should fail refresh with empty refresh token", async () => {
      const refreshData: RefreshTokenInput = {
        token: "some-token",
        refresh_token: ""
      };

      const result = await implHandleRefreshToken(refreshData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.errorCodes).toContain("ERROR_VALIDATION_FAILED");
    });

    it("should fail refresh with expired refresh token", async () => {
      // This test would require mocking time or creating an expired token
      // For now, we'll test with an invalid format that simulates expiry
      const refreshData: RefreshTokenInput = {
        token: "some-token",
        refresh_token: "expired.refresh.token"
      };

      const result = await implHandleRefreshToken(refreshData, businessLogic);

      expect(result.status).toBe(401);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invalid or expired refresh token");
    });

    it("should invalidate old refresh token after successful refresh", async () => {
      // Setup: Register a user to get initial tokens
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Invalidate User"
      };

      const registerResult = await businessLogic.register(userData);
      const originalRefreshToken = registerResult.refresh_token;

      // First refresh
      const refreshData: RefreshTokenInput = {
        token: registerResult.token,
        refresh_token: originalRefreshToken
      };

      const result1 = await implHandleRefreshToken(refreshData, businessLogic);
      expect(result1.status).toBe(200);

      // Try to use the same refresh token again
      const result2 = await implHandleRefreshToken(refreshData, businessLogic);
      expect(result2.status).toBe(401);
      expect(result2.body.error).toContain("Invalid or expired refresh token");
    });

    it("should handle multiple refresh operations with new tokens", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Multiple User"
      };

      const registerResult = await businessLogic.register(userData);
      let currentRefreshToken = registerResult.refresh_token;

      // First refresh
      const refreshData1: RefreshTokenInput = {
        token: registerResult.token,
        refresh_token: currentRefreshToken
      };

      const result1 = await implHandleRefreshToken(refreshData1, businessLogic);
      expect(result1.status).toBe(200);
      currentRefreshToken = result1.body.data.refresh_token;

      // Second refresh with new token
      const refreshData2: RefreshTokenInput = {
        token: result1.body.data.token,
        refresh_token: currentRefreshToken
      };

      const result2 = await implHandleRefreshToken(refreshData2, businessLogic);
      expect(result2.status).toBe(200);

      // Verify all tokens are different
      expect(registerResult.refresh_token).not.toBe(result1.body.data.refresh_token);
      expect(result1.body.data.refresh_token).not.toBe(result2.body.data.refresh_token);
      expect(registerResult.token).not.toBe(result1.body.data.token);
      expect(result1.body.data.token).not.toBe(result2.body.data.token);
    });

    it("should maintain user data consistency across refreshes", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "password123",
        name: "Consistent User"
      };

      const registerResult = await businessLogic.register(userData);

      // Refresh token
      const refreshData: RefreshTokenInput = {
        token: registerResult.token,
        refresh_token: registerResult.refresh_token
      };

      const result = await implHandleRefreshToken(refreshData, businessLogic);

      // Verify user data remains consistent
      expect(result.body.data?.user.id).toBe(registerResult.user.id);
      expect(result.body.data?.user.email).toBe(registerResult.user.email);
      expect(result.body.data?.user.name).toBe(registerResult.user.name);
      expect(result.body.data?.user.createdAt).toBe(registerResult.user.createdAt);
      expect(result.body.data?.user.emailVerified).toBe(registerResult.user.emailVerified);
    });
  });
});
