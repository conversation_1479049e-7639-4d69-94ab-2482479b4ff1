//@ts-ignore
import { beforeEach, describe, expect, it, test } from 'bun:test';
import { implHandleForgotPassword } from '@/app/api/v1/auth/forgot-password/impl';
import { implHandleResetPassword } from '@/app/api/v1/auth/reset-password/impl';
import { MongoAuthDBRepository } from '@/lib/repositories/auth';
import { AuthBusinessLogicInterface } from "@/lib/repositories/auth/AuthBusinessLogicInterface";
import { AuthBusinessLogic } from "@/lib/repositories/auth/BusinessLogic";
import type { UserRegister } from "@/lib/types/base";
import { InMemoryMongoDriver } from '@/tests/InMemoryMongoDriver';
import { TestAuthDBRepositoryWrapper } from './TestDBRepositoryWrapper';

describe("Reset Password API Tests", () => {
  let businessLogic: AuthBusinessLogicInterface;
  let dbRepository: TestAuthDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Auth");
    await driver.connect()
    const originalDb = new MongoAuthDBRepository(driver);
    dbRepository = new TestAuthDBRepositoryWrapper(originalDb, driver);
    businessLogic = new AuthBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("POST /api/v1/auth/reset-password", () => {
    it("should successfully reset password with valid token", async () => {
      // Setup: Register a user and request password reset
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "oldpassword123",
        name: "Reset User"
      };

      await businessLogic.register(userData);

      const forgotResult = await implHandleForgotPassword({
        email: "<EMAIL>"
      }, businessLogic);

      const resetToken = forgotResult.body.data.resetToken;

      // Test: Reset password
      const resetPasswordData = {
        token: resetToken,
        newPassword: "newpassword456",
        confirmPassword: "newpassword456"
      };

      const result = await implHandleResetPassword(resetPasswordData, businessLogic);

      // Verify API response
      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.message).toContain("Password reset successfully");

      // Verify old password no longer works
      await expect(businessLogic.login({
        email: "<EMAIL>",
        password: "oldpassword123"
      })).rejects.toThrow();

      // Verify new password works
      const loginResult = await businessLogic.login({
        email: "<EMAIL>",
        password: "newpassword456"
      });
      expect(loginResult.user.email).toBe("<EMAIL>");

      // Verify reset token was cleaned up
      expect(await dbRepository.getPasswordResetCount()).toBe(0);
    });

    it("should fail reset with invalid token", async () => {
      const resetPasswordData = {
        token: "invalid-reset-token",
        newPassword: "newpassword456",
        confirmPassword: "newpassword456"
      };

      const result = await implHandleResetPassword(resetPasswordData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invalid or expired reset token");
    });

    it("should fail reset with missing token", async () => {
      const resetPasswordData = {
        newPassword: "newpassword456",
        confirmPassword: "newpassword456"
      };

      const result = await implHandleResetPassword(resetPasswordData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail reset with missing new password", async () => {
      const resetPasswordData = {
        token: "some-token",
        confirmPassword: "newpassword456"
      };

      const result = await implHandleResetPassword(resetPasswordData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail reset with weak new password", async () => {
      // Setup: Register a user and request password reset
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "oldpassword123",
        name: "Weak User"
      };

      await businessLogic.register(userData);

      const forgotResult = await implHandleForgotPassword({
        email: "<EMAIL>"
      }, businessLogic);

      const resetToken = forgotResult.body.data.resetToken;

      // Test: Try to reset with weak password
      const resetPasswordData = {
        token: resetToken,
        newPassword: "123", // Too short
        confirmPassword: "123"
      };

      const result = await implHandleResetPassword(resetPasswordData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail reset with empty fields", async () => {
      const resetPasswordData = {
        token: "",
        newPassword: "",
        confirmPassword: ""
      };

      const result = await implHandleResetPassword(resetPasswordData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail reset with expired token", async () => {
      // This test would require mocking time or creating an expired token
      // For now, we'll test with an invalid format that simulates expiry
      const resetPasswordData = {
        token: "expired.reset.token",
        newPassword: "newpassword456",
        confirmPassword: "newpassword456"
      };

      const result = await implHandleResetPassword(resetPasswordData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invalid or expired reset token");
    });

    it("should fail reset with already used token", async () => {
      // Setup: Register a user and request password reset
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "oldpassword123",
        name: "Used User"
      };

      await businessLogic.register(userData);

      const forgotResult = await implHandleForgotPassword({
        email: "<EMAIL>"
      }, businessLogic);

      const resetToken = forgotResult.body.data.resetToken;

      // First reset (should succeed)
      const resetPasswordData1 = {
        token: resetToken,
        newPassword: "newpassword456",
        confirmPassword: "newpassword456"
      };

      const result1 = await implHandleResetPassword(resetPasswordData1, businessLogic);
      expect(result1.status).toBe(200);

      // Try to use same token again (should fail)
      const resetPasswordData2 = {
        token: resetToken,
        newPassword: "anotherpassword789",
        confirmPassword: "anotherpassword789"
      };

      const result2 = await implHandleResetPassword(resetPasswordData2, businessLogic);
      expect(result2.status).toBe(400);
      expect(result2.body.error).toContain("Invalid or expired reset token");
    });

    it("should handle token validation after password reset", async () => {
      // Setup: Register a user and get tokens
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "oldpassword123",
        name: "Invalidate User"
      };

      const registerResult = await businessLogic.register(userData);
      const oldToken = registerResult.token;

      // Verify old token works
      const validatedUser1 = await businessLogic.validateToken(oldToken);
      expect(validatedUser1).toBeTruthy();

      // Request password reset
      const forgotResult = await implHandleForgotPassword({
        email: "<EMAIL>"
      }, businessLogic);

      const resetToken = forgotResult.body.data.resetToken;

      // Reset password
      const resetPasswordData = {
        token: resetToken,
        newPassword: "newpassword456",
        confirmPassword: "newpassword456"
      };

      await implHandleResetPassword(resetPasswordData, businessLogic);

      // Current implementation doesn't invalidate tokens on password reset
      const validatedUser2 = await businessLogic.validateToken(oldToken);
      expect(validatedUser2).toBeTruthy(); // Token still works
    });

    it("should handle multiple reset requests correctly", async () => {
      // Setup: Register a user
      const userData: UserRegister = {
        email: "<EMAIL>",
        password: "oldpassword123",
        name: "Multiple User"
      };

      await businessLogic.register(userData);

      // First reset request
      const forgotResult1 = await implHandleForgotPassword({
        email: "<EMAIL>"
      }, businessLogic);

      const resetToken1 = forgotResult1.body.data.resetToken;

      // Second reset request (should invalidate first token)
      const forgotResult2 = await implHandleForgotPassword({
        email: "<EMAIL>"
      }, businessLogic);

      const resetToken2 = forgotResult2.body.data.resetToken;

      // Try to use first token (should fail)
      const resetPasswordData1 = {
        token: resetToken1,
        newPassword: "newpassword456"
      };

      const result1 = await implHandleResetPassword(resetPasswordData1, businessLogic);
      expect(result1.status).toBe(400);

      // Use second token (should succeed)
      const resetPasswordData2 = {
        token: resetToken2,
        newPassword: "newpassword456",
        confirmPassword: "newpassword456"
      };

      const result2 = await implHandleResetPassword(resetPasswordData2, businessLogic);
      expect(result2.status).toBe(200);
    });
  });
});
