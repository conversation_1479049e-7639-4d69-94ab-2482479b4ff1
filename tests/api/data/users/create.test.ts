//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { UserBusinessLogicInterface } from "@/lib/repositories/users/interface";
import { UserBusinessLogic } from "@/lib/repositories/users/BusinessLogic";
import { MongoUserRepository } from "@/lib/repositories/users/MongoRepository";
import { TestUserDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateUser } from "@/app/api/v1/users/impl";
import {
  createFullUser,
  createMinimalUser,
  createUserWithDescription,
  createUserWithTags,
  createUserWithWhitespace,
  createDuplicateUser,
  createSecondDuplicateUser,
  createInvalidUser,
  createUserWithManyTags,
  createUserWithoutDescription,
  createUserWithEmptyTags
} from "./object_creator";

describe("Create User API Tests", () => {
  let businessLogic: UserBusinessLogicInterface;
    let dbRepository: TestUserDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("User");
      await driver.connect()
      const originalDb = new MongoUserRepository(driver);
      dbRepository = new TestUserDBRepositoryWrapper(originalDb, driver);
      businessLogic = new UserBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("POST /api/v1/users", () => {
    it("should successfully create a new users with all fields", async () => {
      const usersData = createFullUser();

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(usersData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(usersData.ARRAY_FIELD2);
      expect(result.body.data?.ARRAY_FIELD).toEqual(usersData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(usersData.variables);
      expect(result.body.data?.tags).toEqual(usersData.tags);
      expect(result.body.data?.isActive).toBe(usersData.isActive);
      expect(result.body.data?.id).toBeDefined();
      expect(result.body.data?.createdAt).toBeDefined();
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should successfully create a users with minimal required fields", async () => {
      const usersData = createMinimalUser();

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(usersData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD).toEqual(usersData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(usersData.variables);
      expect(result.body.data?.isActive).toBe(true); // Should default to true
      expect(result.body.data?.tags).toEqual([]);
    });

    it("should create user with ARRAY_FIELD2", async () => {
      const usersData = createUserWithDescription();

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.ARRAY_FIELD2).toBe(usersData.ARRAY_FIELD2);
    });

    it("should create user with tags", async () => {
      const usersData = createUserWithTags();

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.tags).toEqual(usersData.tags);
    });

    it("should trim whitespace from STRING_FIELD", async () => {
      const usersData = createUserWithWhitespace();

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed User");
    });

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first users
      const usersData1 = createDuplicateUser();
      await implHandleCreateUser(usersData1, businessLogic);

      // Try to create second users with same STRING_FIELD
      const usersData2 = createSecondDuplicateUser();
      const result = await implHandleCreateUser(usersData2, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("User with the same STRING_FIELD already exists");
    });

    it("should fail with missing STRING_FIELD", async () => {
      const usersData = {
        ARRAY_FIELD2: "+6281234567890"
      };

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing ARRAY_FIELD2", async () => {
      const usersData = createInvalidUser('missing-ARRAY_FIELD2');


      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing ARRAY_FIELD", async () => {
      const usersData = createInvalidUser('missing-ARRAY_FIELD');

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("ARRAY_FIELD");
    });

    it("should fail with missing variables", async () => {
      const usersData = createInvalidUser('missing-variables');

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("variables");
    });

    it("should fail with empty ARRAY_FIELD array", async () => {
      const usersData = createInvalidUser('empty-ARRAY_FIELD');

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with empty variables array", async () => {
      const usersData = createInvalidUser('empty-variables');

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should create user with many tags", async () => {
      const usersData = createUserWithManyTags();

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(usersData.tags);
    });

    it("should handle optional ARRAY_FIELD2", async () => {
      const usersData = createUserWithoutDescription();

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.ARRAY_FIELD2).toBe("");
    });

    it("should handle empty arrays for tags", async () => {
      const usersData = createUserWithEmptyTags();

      const result = await implHandleCreateUser(usersData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(usersData.tags);
    });
  });
});
