// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { UserBusinessLogicInterface } from "@/lib/repositories/users/interface";
import { UserBusinessLogic } from "@/lib/repositories/users/BusinessLogic";
import { MongoUserRepository } from "@/lib/repositories/users/MongoRepository";
import { TestUserDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateUser, implHandleGetUser, implHandleGetAllUsers } from "@/app/api/v1/users/impl";
import {
  createUser,
  createSimpleUsers,
  createUsersWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams
} from "./object_creator";

describe("Read User API Tests", () => {
  let businessLogic: UserBusinessLogicInterface;
  let dbRepository: TestUserDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("User");
    await driver.connect();
    const originalDb = new MongoUserRepository(driver);
    dbRepository = new TestUserDBRepositoryWrapper(originalDb, driver);
    businessLogic = new UserBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("GET /api/v1/users/:id", () => {
    it("should successfully get user by ID", async () => {
      const user = createUser(5); // John Doe User

      const createResult = await implHandleCreateUser(user, businessLogic);
      const id = createResult.body.data.id;

      const result = await implHandleGetUser(id, businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.id).toBe(id);
      expect(result.body.data?.STRING_FIELD).toBe(user.STRING_FIELD);
    });

    it("should fail to get non-existent user", async () => {
      const result = await implHandleGetUser("507f1f77bcf86cd799439011", businessLogic);
      expect(result.status).toBe(404);
    });

    it("should fail with empty user ID", async () => {
      const result = await implHandleGetUser("", businessLogic);
      expect(result.status).toBe(400);
    });

    it("should fail with whitespace-only user ID", async () => {
      const result = await implHandleGetUser("   ", businessLogic);
      expect(result.status).toBe(400);
    });
  });

  describe("GET /api/v1/users", () => {
    it("should get all users", async () => {
      const users = createSimpleUsers();
      for (const r of users) await implHandleCreateUser(r, businessLogic);

      const result = await implHandleGetAllUsers(businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(3);
    });

    it("should return empty when no users exist", async () => {
      const result = await implHandleGetAllUsers(businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });
  });

  describe("GET /api/v1/users/search", () => {
    beforeEach(async () => {
      const data = createUsersWithTags();
      for (const r of data) await implHandleCreateUser(r, businessLogic);
    });

    it("should search by STRING_FIELD", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should search by tag", async () => {
      const params = createSearchByTagParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(4);
    });
  });

  describe("GET /api/v1/users/filters", () => {
    beforeEach(async () => {
      const data = createUsersWithTags();
      for (const r of data) await implHandleCreateUser(r, businessLogic);
    });

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams();
      const result = await implHandleGetAllUsers(businessLogic, params);
      expect(result.status).toBe(400);
    });
  });
});
