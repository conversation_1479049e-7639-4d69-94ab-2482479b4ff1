//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { UserBusinessLogicInterface } from "@/lib/repositories/users/interface";
import { UserBusinessLogic } from "@/lib/repositories/users/BusinessLogic";
import { MongoUserRepository } from "@/lib/repositories/users/MongoRepository";
import { TestUserDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateUser,
  implHandleGetUser,
  implHandleBulkCreateUsers,
  implHandleBulkUpdateUsers,
  implHandleBulkDeleteUsers
} from "@/app/api/v1/users/impl";
import {
  createMultipleUsers,
  createSimpleUsers,
  createExistingUser,
  createDuplicateUsersForBulk,
  createUsersForBulkUpdate,
  createBulkUpdateData,
  createUsersForBulkDelete
} from "./object_creator";

describe("User Bulk Operations Tests", () => {
  let businessLogic: UserBusinessLogicInterface;
  let dbRepository: TestUserDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("User");
    await driver.connect()
    const originalDb = new MongoUserRepository(driver);
    dbRepository = new TestUserDBRepositoryWrapper(originalDb, driver);
    businessLogic = new UserBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("Bulk Create", () => {
    it("should successfully create multiple users", async () => {
      const usersData = createMultipleUsers();

      const result = await implHandleBulkCreateUsers(usersData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(usersData.length);
      expect(result.body.data[0].STRING_FIELD).toBe(usersData[0].STRING_FIELD);
      expect(result.body.data[1].STRING_FIELD).toBe(usersData[1].STRING_FIELD);
      expect(result.body.data[2].STRING_FIELD).toBe(usersData[2].STRING_FIELD);
      expect(await dbRepository.getUserCount()).toBe(usersData.length);
    });

    it("should fail if any user has duplicate STRING_FIELD", async () => {
      const existingUser = createExistingUser();
      const createResult = await implHandleCreateUser(existingUser, businessLogic);
      expect(createResult.status).toBe(201);

      const usersData = createDuplicateUsersForBulk();

      const result = await implHandleBulkCreateUsers(usersData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate STRING_FIELD found: Existing User");
      expect(await dbRepository.getUserCount()).toBe(1);
    });

    it("should handle simple users creation", async () => {
      const usersData = createSimpleUsers();

      const result = await implHandleBulkCreateUsers(usersData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(usersData.length);
      expect(await dbRepository.getUserCount()).toBe(usersData.length);
    });
  });

  describe("Bulk Update", () => {
    it("should successfully update multiple users", async () => {
      const usersData = createUsersForBulkUpdate();
      const createResult1 = await implHandleCreateUser(usersData[0], businessLogic);
      const createResult2 = await implHandleCreateUser(usersData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult1.body.data.id, data: updateData[0] },
        { id: createResult2.body.data.id, data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateUsers(updates, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.updatedCount).toBe(2);

      const getResult1 = await implHandleGetUser(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetUser(createResult2.body.data.id, businessLogic);

      expect(getResult1.body.data?.STRING_FIELD).toBe(updateData[0].STRING_FIELD);
      expect(getResult1.body.data?.updatedBy).toBe(updateData[0].updatedBy);
      expect(getResult2.body.data?.STRING_FIELD).toBe(updateData[1].STRING_FIELD);
      expect(getResult2.body.data?.updatedBy).toBe(updateData[1].updatedBy);
    });

    it("should fail if any user doesn't exist", async () => {
      const userData = createUsersForBulkUpdate()[0];
      const createResult = await implHandleCreateUser(userData, businessLogic);
      expect(createResult.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult.body.data.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateUsers(updates, businessLogic);

      expect(result.status).toBe(500);
      expect(result.body.status).toBe("failed");
    });

    it("should fail if any update would create duplicate STRING_FIELD", async () => {
      const usersData = createUsersForBulkUpdate();
      const createResult1 = await implHandleCreateUser(usersData[0], businessLogic);
      const createResult2 = await implHandleCreateUser(usersData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updates = [
        {
          id: createResult2.body.data.id,
          data: { STRING_FIELD: usersData[0].STRING_FIELD, updatedBy: "admin" }, // Try to update second user with first user's STRING_FIELD
        },
      ];

      const result = await implHandleBulkUpdateUsers(updates, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate STRING_FIELD in update: User 1");
    });
  });

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple users", async () => {
      const usersData = createUsersForBulkDelete();
      const createResult1 = await implHandleCreateUser(usersData[0], businessLogic);
      const createResult2 = await implHandleCreateUser(usersData[1], businessLogic);
      const createResult3 = await implHandleCreateUser(usersData[2], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);
      expect(createResult3.status).toBe(201);

      const result = await implHandleBulkDeleteUsers([createResult1.body.data.id, createResult2.body.data.id], businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getUserCount()).toBe(1); // Only non-deleted

      const getResult1 = await implHandleGetUser(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetUser(createResult2.body.data.id, businessLogic);
      const getResult3 = await implHandleGetUser(createResult3.body.data.id, businessLogic);

      expect(getResult1.status).toBe(404);
      expect(getResult2.status).toBe(404);
      expect(getResult3.status).toBe(200);
    });

    it("should successfully hard delete multiple users", async () => {
      const usersData = createUsersForBulkDelete();
      const createResult1 = await implHandleCreateUser(usersData[0], businessLogic);
      const createResult2 = await implHandleCreateUser(usersData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const result = await implHandleBulkDeleteUsers([createResult1.body.data.id, createResult2.body.data.id], businessLogic, true);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getUserCount()).toBe(0);
    });

    it("should fail if any user doesn't exist", async () => {
      const userData = createUsersForBulkDelete()[0];
      const createResult = await implHandleCreateUser(userData, businessLogic);
      expect(createResult.status).toBe(201);

      const result = await implHandleBulkDeleteUsers([createResult.body.data.id, "non-existent-id"], businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty user IDs", async () => {
      const result = await implHandleBulkDeleteUsers(["", "valid-id"], businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ID at index 0 is required");
    });
  });
});
