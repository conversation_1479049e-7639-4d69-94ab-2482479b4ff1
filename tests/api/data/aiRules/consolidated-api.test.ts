//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { AiRuleBusinessLogicInterface } from "@/lib/repositories/aiRules/interface";
import { AiRuleBusinessLogic } from "@/lib/repositories/aiRules/BusinessLogic";
import { MongoAiRuleRepository } from "@/lib/repositories/aiRules/MongoRepository";
import { TestAiRuleDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateAiRule, implHandleGetAllAiRules, implHandleDeleteAiRule } from "@/app/api/v1/ai-rules/impl";
import {
  createMultipleAiRules,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams
} from "./object_creator";

describe("Consolidated AiRule API Tests", () => {
  let businessLogic: AiRuleBusinessLogicInterface;
  let dbRepository: TestAiRuleDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("AiRule");
    await driver.connect()
    const originalDb = new MongoAiRuleRepository(driver);
    dbRepository = new TestAiRuleDBRepositoryWrapper(originalDb, driver);
    businessLogic = new AiRuleBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  const testAiRules = createMultipleAiRules();

  describe("implHandleGetAllAiRules - Consolidated Function", () => {
    beforeEach(async () => {
      for (const aiRulesData of testAiRules) {
        await implHandleCreateAiRule(aiRulesData, businessLogic);
      }
    });

    it("should get all aiRules when no parameters provided", async () => {
      const result = await implHandleGetAllAiRules(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);
      expect(result.body.data?.total).toBe(4);
    });

    it("should search aiRules by name", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Rule and Bob Johnson Rule

      const names = result.body.data?.items.map((c: any) => c.name);
      expect(names).toContain(testAiRules[0].name); // John Doe Rule
      expect(names).toContain(testAiRules[2].name); // Bob Johnson Rule
    });

    it("should search aiRules by description", async () => {
      const params = createSearchByDescriptionParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4); // All rules have "processing" in description
    });

    it("should filter aiRules by tag", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Rule and Bob Johnson Rule

      const names = result.body.data?.items.map((c: any) => c.name);
      expect(names).toContain(testAiRules[0].name); // John Doe Rule
      expect(names).toContain(testAiRules[2].name); // Bob Johnson Rule
    });

    it("should filter aiRules by Customer tag", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Rule and Jane Smith Rule

      const names = result.body.data?.items.map((c: any) => c.name);
      expect(names).toContain(testAiRules[0].name); // John Doe Rule
      expect(names).toContain(testAiRules[1].name); // Jane Smith Rule
    });

    it("should handle pagination", async () => {
      const params = createPaginationParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(4);
    });

    it("should handle sorting by name", async () => {
      const params = createSortByNameAscParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);

      const names = result.body.data?.items.map((c: any) => c.name);
      expect(names![0]).toBe(testAiRules[3].name); // Alice Brown Rule
      expect(names![1]).toBe(testAiRules[2].name); // Bob Johnson Rule
      expect(names![2]).toBe(testAiRules[1].name); // Jane Smith Rule
      expect(names![3]).toBe(testAiRules[0].name); // John Doe Rule
    });

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // VIP aiRules (tag filter applied)
    });

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams();
      const result = await implHandleGetAllAiRules(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should include soft deleted aiRules when specified", async () => {
      // Get all aiRules first to get one to delete
      const allResult = await implHandleGetAllAiRules(businessLogic);
      expect(allResult.status).toBe(200);
      const aiRulesToDelete = allResult.body.data?.items[0];
      expect(aiRulesToDelete).toBeDefined();

      // Soft delete one aiRules
      const deleteResult = await implHandleDeleteAiRule(aiRulesToDelete!.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Get all without including deleted
      const resultWithoutDeleted = await implHandleGetAllAiRules(businessLogic);
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3);

      // Get all including deleted
      const params = createIncludeDeletedParams();
      const resultWithDeleted = await implHandleGetAllAiRules(businessLogic, params);
      expect(resultWithDeleted.body.data?.items).toHaveLength(4);
    });
  });
});
