//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { AiRuleBusinessLogicInterface } from "@/lib/repositories/aiRules/interface";
import { AiRuleBusinessLogic } from "@/lib/repositories/aiRules/BusinessLogic";
import { MongoAiRuleRepository } from "@/lib/repositories/aiRules/MongoRepository";
import { TestAiRuleDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateAiRule } from "@/app/api/v1/ai-rules/impl";
import {
  createFullAiRule,
  createMinimalAiRule,
  createAiRuleWithDescription,
  createAiRuleWithTags,
  createAiRuleWithWhitespace,
  createDuplicateAiRule,
  createSecondDuplicateAiRule,
  createInvalidAiRule,
  createAiRuleWithManyTags,
  createAiRuleWithoutDescription,
  createAiRuleWithEmptyTags
} from "./object_creator";

describe("Create AiRule API Tests", () => {
  let businessLogic: AiRuleBusinessLogicInterface;
    let dbRepository: TestAiRuleDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("AiRule");
      await driver.connect()
      const originalDb = new MongoAiRuleRepository(driver);
      dbRepository = new TestAiRuleDBRepositoryWrapper(originalDb, driver);
      businessLogic = new AiRuleBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("POST /api/v1/ai-rules", () => {
    it("should successfully create a new aiRules with all fields", async () => {
      const aiRulesData = createFullAiRule();

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.name).toBe(aiRulesData.name);
      expect(result.body.data?.description).toBe(aiRulesData.description);
      expect(result.body.data?.conditions).toEqual(aiRulesData.conditions);
      expect(result.body.data?.actions).toEqual(aiRulesData.actions);
      expect(result.body.data?.tags).toEqual(aiRulesData.tags);
      expect(result.body.data?.isActive).toBe(aiRulesData.isActive);
      expect(result.body.data?.id).toBeDefined();
      expect(result.body.data?.createdAt).toBeDefined();
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should successfully create a aiRules with minimal required fields", async () => {
      const aiRulesData = createMinimalAiRule();

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.name).toBe(aiRulesData.name);
      expect(result.body.data?.conditions).toEqual(aiRulesData.conditions);
      expect(result.body.data?.actions).toEqual(aiRulesData.actions);
      expect(result.body.data?.isActive).toBe(true); // Should default to true
      expect(result.body.data?.tags).toEqual([]);
    });

    it("should create rule with description", async () => {
      const aiRulesData = createAiRuleWithDescription();

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.description).toBe(aiRulesData.description);
    });

    it("should create rule with tags", async () => {
      const aiRulesData = createAiRuleWithTags();

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.tags).toEqual(aiRulesData.tags);
    });

    it("should trim whitespace from name", async () => {
      const aiRulesData = createAiRuleWithWhitespace();

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.name).toBe("Trimmed Rule");
    });

    it("should fail with duplicate name", async () => {
      // Create first aiRules
      const aiRulesData1 = createDuplicateAiRule();
      await implHandleCreateAiRule(aiRulesData1, businessLogic);

      // Try to create second aiRules with same name
      const aiRulesData2 = createSecondDuplicateAiRule();
      const result = await implHandleCreateAiRule(aiRulesData2, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("AI Rule with the same name already exists");
    });

    it("should fail with missing name", async () => {
      const aiRulesData = {
        phone: "+6281234567890"
      };

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing phone", async () => {
      const aiRulesData = createInvalidAiRule('missing-phone');


      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing conditions", async () => {
      const aiRulesData = createInvalidAiRule('missing-conditions');

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("conditions");
    });

    it("should fail with missing actions", async () => {
      const aiRulesData = createInvalidAiRule('missing-actions');

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("actions");
    });

    it("should fail with empty conditions array", async () => {
      const aiRulesData = createInvalidAiRule('empty-conditions');

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with empty actions array", async () => {
      const aiRulesData = createInvalidAiRule('empty-actions');

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should create rule with many tags", async () => {
      const aiRulesData = createAiRuleWithManyTags();

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(aiRulesData.tags);
    });

    it("should handle optional description", async () => {
      const aiRulesData = createAiRuleWithoutDescription();

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.description).toBe("");
    });

    it("should handle empty arrays for tags", async () => {
      const aiRulesData = createAiRuleWithEmptyTags();

      const result = await implHandleCreateAiRule(aiRulesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(aiRulesData.tags);
    });
  });
});
