//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { AiRuleBusinessLogicInterface } from "@/lib/repositories/aiRules/interface";
import { AiRuleBusinessLogic } from "@/lib/repositories/aiRules/BusinessLogic";
import { MongoAiRuleRepository } from "@/lib/repositories/aiRules/MongoRepository";
import { TestAiRuleDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateAiRule,
  implHandleGetAiRule,
  implHandleDeleteAiRule,
  implHandleUpdateAiRule,
  implHandleGetAllAiRules,
  implHandleRestoreAiRule
} from "@/app/api/v1/ai-rules/impl";
import { createAiRule, createAiRuleUpdate, createAiRuleWithDescription, createTestRule, createTestRule2 } from "./object_creator";

describe("AiRule Soft Delete Tests", () => {
  let businessLogic: AiRuleBusinessLogicInterface;
    let dbRepository: TestAiRuleDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("AiRule");
      await driver.connect()
      const originalDb = new MongoAiRuleRepository(driver);
      dbRepository = new TestAiRuleDBRepositoryWrapper(originalDb, driver);
      businessLogic = new AiRuleBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("Soft Delete", () => {
    it("should soft delete a aiRules by default", async () => {
      const aiRuleData = createAiRuleWithDescription();
      const createResult = await implHandleCreateAiRule(aiRuleData, businessLogic);
      expect(createResult.status).toBe(201);

      const deleteResult = await implHandleDeleteAiRule(createResult.body.data.id, businessLogic);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");

      // AiRule should not be accessible by default
      const getResult = await implHandleGetAiRule(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);

      // But should be accessible when including deleted
      const getDeletedResult = await implHandleGetAiRule(createResult.body.data.id, businessLogic, true);
      expect(getDeletedResult.status).toBe(200);
      expect(getDeletedResult.body.data).not.toBeNull();

      // Count should exclude soft deleted
      expect(await dbRepository.getAiRuleCount()).toBe(0);
      expect(await dbRepository.getAiRuleCount(true)).toBe(1);
    });

    it("should hard delete when specified", async () => {
      const aiRuleData = createAiRuleWithDescription();
      const createResult = await implHandleCreateAiRule(aiRuleData, businessLogic);
      expect(createResult.status).toBe(201);

      // Hard delete using impl function
      const deleteResult = await implHandleDeleteAiRule(createResult.body.data.id, businessLogic, true);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");

      // AiRule should not be accessible at all
      const getResult = await implHandleGetAiRule(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);
      const getDeletedResult = await implHandleGetAiRule(createResult.body.data.id, businessLogic, true);
      expect(getDeletedResult.status).toBe(404);

      // Count should be 0 in both cases
      expect(await dbRepository.getAiRuleCount()).toBe(0);
      expect(await dbRepository.getAiRuleCount(true)).toBe(0);
    });

    it("should not include soft deleted aiRules in getAll by default", async () => {
      const aiRuleData1 = createAiRule(1);
      const aiRuleData2 = createAiRule(2);

      const createResult1 = await implHandleCreateAiRule(aiRuleData1, businessLogic);
      const createResult2 = await implHandleCreateAiRule(aiRuleData2, businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      // Soft delete one aiRules
      const deleteResult = await implHandleDeleteAiRule(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const result = await implHandleGetAllAiRules(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.items).toHaveLength(1);
      expect(result.body.data?.total).toBe(1);
      expect(result.body.data?.items[0].id).toBe(createResult2.body.data.id);
    });

    it("should include soft deleted aiRules when specified", async () => {
      const aiRuleData1 = createAiRule(1);
      const aiRuleData2 = createAiRule(2);

      const createResult1 = await implHandleCreateAiRule(aiRuleData1, businessLogic);
      const createResult2 = await implHandleCreateAiRule(aiRuleData2, businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      // Soft delete one aiRules
      const deleteResult = await implHandleDeleteAiRule(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const result = await implHandleGetAllAiRules(businessLogic, { includeDeleted: true });

      expect(result.status).toBe(200);
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(2);

      const deletedAiRule = result.body.data?.items.find(c => c.id === createResult1.body.data.id);
      expect(deletedAiRule).not.toBeNull();
      expect(deletedAiRule?.deletedAt).toBeDefined();
    });

    it("should not allow updating soft deleted aiRules", async () => {
      const aiRuleData = createAiRule(3);
      const createResult = await implHandleCreateAiRule(aiRuleData, businessLogic);
      expect(createResult.status).toBe(201);

      const deleteResult = await implHandleDeleteAiRule(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const aiRuleUpdate = createAiRuleUpdate(1)

      const result = await implHandleUpdateAiRule(createResult.body.data.id, aiRuleUpdate, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should not include soft deleted aiRules in search", async () => {
      const aiRuleData1 = createAiRule(3); // "Test Rule"
      const createResult = await implHandleCreateAiRule(aiRuleData1, businessLogic);
      expect(createResult.status).toBe(201);

      // Soft delete the rule
      const deleteResult = await implHandleDeleteAiRule(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Search should not find the soft deleted rule
      const searchResult = await implHandleGetAllAiRules(businessLogic, { search: "Test" });
      expect(searchResult.status).toBe(200);
      expect(searchResult.body.data).toHaveLength(0);
    });
  });

  describe("Restore", () => {
    it("should restore a soft deleted aiRules", async () => {
      const aiRuleData = createAiRuleWithDescription();
      const createResult = await implHandleCreateAiRule(aiRuleData, businessLogic);
      expect(createResult.status).toBe(201);

      // Soft delete the aiRules
      const deleteResult = await implHandleDeleteAiRule(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const getResult = await implHandleGetAiRule(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);

      // Restore the aiRules
      const restoreResult = await implHandleRestoreAiRule(createResult.body.data.id, businessLogic);

      expect(restoreResult.status).toBe(200);
      expect(restoreResult.body.status).toBe("success");

      // AiRule should be accessible again
      const restoredResult = await implHandleGetAiRule(createResult.body.data.id, businessLogic);
      expect(restoredResult.status).toBe(200);
      expect(restoredResult.body.data?.deletedAt).toBeUndefined();

      // Count should include the restored aiRules
      expect(await dbRepository.getAiRuleCount()).toBe(1);
    });

    it("should fail to restore a non-existent aiRules", async () => {
      const restoreResult = await implHandleRestoreAiRule("507f1f77bcf86cd799439011", businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail to restore a aiRules that was never deleted", async () => {
      const aiRuleData = createAiRule(3);
      const createResult = await implHandleCreateAiRule(aiRuleData, businessLogic);
      expect(createResult.status).toBe(201);

      const restoreResult = await implHandleRestoreAiRule(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail to restore a hard deleted aiRules", async () => {
      const aiRuleData = createAiRule(3);
      const createResult = await implHandleCreateAiRule(aiRuleData, businessLogic);
      expect(createResult.status).toBe(201);

      // Hard delete the aiRules
      const deleteResult = await implHandleDeleteAiRule(createResult.body.data.id, businessLogic, true);
      expect(deleteResult.status).toBe(200);

      const restoreResult = await implHandleRestoreAiRule(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail with empty aiRules ID", async () => {
      const restoreResult = await implHandleRestoreAiRule("", businessLogic);
      expect(restoreResult.status).toBe(400);
      expect(restoreResult.body.status).toBe("failed");
      expect(restoreResult.body.error).toContain("AiRule ID is required");
    });

    it("should fail with whitespace-only aiRules ID", async () => {
      const restoreResult = await implHandleRestoreAiRule("   ", businessLogic);
      expect(restoreResult.status).toBe(400);
      expect(restoreResult.body.status).toBe("failed");
      expect(restoreResult.body.error).toContain("AiRule ID is required");
    });

    it("should update updatedAt when restoring", async () => {
      const aiRuleData = createAiRule(3);
      const createResult = await implHandleCreateAiRule(aiRuleData, businessLogic);
      expect(createResult.status).toBe(201);

      const originalUpdatedAt = createResult.body.data.updatedAt;

      // Wait a bit to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      // Soft delete and restore
      const deleteResult = await implHandleDeleteAiRule(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const restoreResult = await implHandleRestoreAiRule(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(200);

      const getResult = await implHandleGetAiRule(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(200);
      expect(getResult.body.data?.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating aiRules with name of soft deleted aiRules", async () => {
      // Create and soft delete a aiRules
      const aiRuleData1 = createTestRule();
      const createResult1 = await implHandleCreateAiRule(aiRuleData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const deleteResult = await implHandleDeleteAiRule(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Should be able to create new aiRules with same name
      const aiRuleData2 = createTestRule2();
      const createResult2 = await implHandleCreateAiRule(aiRuleData2, businessLogic);

      expect(createResult2.status).toBe(201);
      expect(createResult2.body.data.name).toBe(aiRuleData2.name);
      expect(await dbRepository.getAiRuleCount()).toBe(1);
    });

    it("should prevent creating aiRules with name of active aiRules", async () => {
      const aiRuleData1 = createTestRule();
      const createResult1 = await implHandleCreateAiRule(aiRuleData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const aiRuleData2 = createTestRule2();
      const createResult2 = await implHandleCreateAiRule(aiRuleData2, businessLogic);

      expect(createResult2.status).toBe(409);
      expect(createResult2.body.status).toBe("failed");
      expect(createResult2.body.error).toContain("AI Rule with the same name already exists");
    });

    it("should prevent restoring aiRules if name is now taken", async () => {
      // Create and soft delete a aiRules
      const aiRuleData1 = createTestRule();
      const createResult1 = await implHandleCreateAiRule(aiRuleData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const deleteResult = await implHandleDeleteAiRule(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Create new aiRules with same name
      const aiRuleData2 = createTestRule2();
      const createResult2 = await implHandleCreateAiRule(aiRuleData2, businessLogic);
      expect(createResult2.status).toBe(201);

      // Should not be able to restore the first aiRules
      const restoreResult = await implHandleRestoreAiRule(createResult1.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });
  });
});
