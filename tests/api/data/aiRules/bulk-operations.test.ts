//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { AiRuleBusinessLogicInterface } from "@/lib/repositories/aiRules/interface";
import { AiRuleBusinessLogic } from "@/lib/repositories/aiRules/BusinessLogic";
import { MongoAiRuleRepository } from "@/lib/repositories/aiRules/MongoRepository";
import { TestAiRuleDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateAiRule,
  implHandleGetAiRule,
  implHandleBulkCreateAiRules,
  implHandleBulkUpdateAiRules,
  implHandleBulkDeleteAiRules
} from "@/app/api/v1/ai-rules/impl";
import {
  createMultipleAiRules,
  createSimpleAiRules,
  createExistingRule,
  createDuplicateRulesForBulk,
  createRulesForBulkUpdate,
  createBulkUpdateData,
  createRulesForBulkDelete
} from "./object_creator";

describe("AiRule Bulk Operations Tests", () => {
  let businessLogic: AiRuleBusinessLogicInterface;
  let dbRepository: TestAiRuleDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("AiRule");
    await driver.connect()
    const originalDb = new MongoAiRuleRepository(driver);
    dbRepository = new TestAiRuleDBRepositoryWrapper(originalDb, driver);
    businessLogic = new AiRuleBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("Bulk Create", () => {
    it("should successfully create multiple aiRules", async () => {
      const aiRulesData = createMultipleAiRules();

      const result = await implHandleBulkCreateAiRules(aiRulesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(aiRulesData.length);
      expect(result.body.data[0].name).toBe(aiRulesData[0].name);
      expect(result.body.data[1].name).toBe(aiRulesData[1].name);
      expect(result.body.data[2].name).toBe(aiRulesData[2].name);
      expect(await dbRepository.getAiRuleCount()).toBe(aiRulesData.length);
    });

    it("should fail if any aiRule has duplicate name", async () => {
      const existingRule = createExistingRule();
      const createResult = await implHandleCreateAiRule(existingRule, businessLogic);
      expect(createResult.status).toBe(201);

      const aiRulesData = createDuplicateRulesForBulk();

      const result = await implHandleBulkCreateAiRules(aiRulesData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate name found: Existing Rule");
      expect(await dbRepository.getAiRuleCount()).toBe(1);
    });

    it("should handle simple rules creation", async () => {
      const aiRulesData = createSimpleAiRules();

      const result = await implHandleBulkCreateAiRules(aiRulesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(aiRulesData.length);
      expect(await dbRepository.getAiRuleCount()).toBe(aiRulesData.length);
    });
  });

  describe("Bulk Update", () => {
    it("should successfully update multiple aiRules", async () => {
      const rulesData = createRulesForBulkUpdate();
      const createResult1 = await implHandleCreateAiRule(rulesData[0], businessLogic);
      const createResult2 = await implHandleCreateAiRule(rulesData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult1.body.data.id, data: updateData[0] },
        { id: createResult2.body.data.id, data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateAiRules(updates, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.updatedCount).toBe(2);

      const getResult1 = await implHandleGetAiRule(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetAiRule(createResult2.body.data.id, businessLogic);

      expect(getResult1.body.data?.name).toBe(updateData[0].name);
      expect(getResult1.body.data?.updatedBy).toBe(updateData[0].updatedBy);
      expect(getResult2.body.data?.name).toBe(updateData[1].name);
      expect(getResult2.body.data?.updatedBy).toBe(updateData[1].updatedBy);
    });

    it("should fail if any aiRule doesn't exist", async () => {
      const ruleData = createRulesForBulkUpdate()[0];
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic);
      expect(createResult.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult.body.data.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateAiRules(updates, businessLogic);

      expect(result.status).toBe(500);
      expect(result.body.status).toBe("failed");
    });

    it("should fail if any update would create duplicate name", async () => {
      const rulesData = createRulesForBulkUpdate();
      const createResult1 = await implHandleCreateAiRule(rulesData[0], businessLogic);
      const createResult2 = await implHandleCreateAiRule(rulesData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updates = [
        {
          id: createResult2.body.data.id,
          data: { name: rulesData[0].name, updatedBy: "admin" }, // Try to update second rule with first rule's name
        },
      ];

      const result = await implHandleBulkUpdateAiRules(updates, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate name in update: Rule 1");
    });
  });

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple aiRules", async () => {
      const rulesData = createRulesForBulkDelete();
      const createResult1 = await implHandleCreateAiRule(rulesData[0], businessLogic);
      const createResult2 = await implHandleCreateAiRule(rulesData[1], businessLogic);
      const createResult3 = await implHandleCreateAiRule(rulesData[2], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);
      expect(createResult3.status).toBe(201);

      const result = await implHandleBulkDeleteAiRules([createResult1.body.data.id, createResult2.body.data.id], businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getAiRuleCount()).toBe(1); // Only non-deleted

      const getResult1 = await implHandleGetAiRule(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetAiRule(createResult2.body.data.id, businessLogic);
      const getResult3 = await implHandleGetAiRule(createResult3.body.data.id, businessLogic);

      expect(getResult1.status).toBe(404);
      expect(getResult2.status).toBe(404);
      expect(getResult3.status).toBe(200);
    });

    it("should successfully hard delete multiple aiRules", async () => {
      const rulesData = createRulesForBulkDelete();
      const createResult1 = await implHandleCreateAiRule(rulesData[0], businessLogic);
      const createResult2 = await implHandleCreateAiRule(rulesData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const result = await implHandleBulkDeleteAiRules([createResult1.body.data.id, createResult2.body.data.id], businessLogic, true);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getAiRuleCount()).toBe(0);
    });

    it("should fail if any aiRule doesn't exist", async () => {
      const ruleData = createRulesForBulkDelete()[0];
      const createResult = await implHandleCreateAiRule(ruleData, businessLogic);
      expect(createResult.status).toBe(201);

      const result = await implHandleBulkDeleteAiRules([createResult.body.data.id, "non-existent-id"], businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty aiRule IDs", async () => {
      const result = await implHandleBulkDeleteAiRules(["", "valid-id"], businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ID at index 0 is required");
    });
  });
});
