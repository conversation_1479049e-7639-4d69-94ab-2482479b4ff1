import { AiRuleCreateInput, AiRuleUpdateInput } from "@/lib/repositories/aiRules/interface";

/**
 * Factory functions for creating test AiRule objects
 * This allows for consistent test data across all test files
 * and easy modification of test objects in one place
 */

// Base creator functions for different scenarios
export function createAiRule(variant: number): AiRuleCreateInput {
  const baseRules: Record<number, AiRuleCreateInput> = {
    1: {
      name: "Customer Support Rule",
      description: "Rule for handling customer support requests",
      conditions: ["user_message_contains('help')", "time_between('09:00', '17:00')"],
      actions: ["assign_to_support", "send_acknowledgment"],
      tags: ["Customer", "VIP"],
      isActive: true,
      createdBy: "admin"
    },
    2: {
      name: "Simple Rule",
      conditions: ["always_true"],
      actions: ["log_message"],
      createdBy: "admin"
    },
    3: {
      name: "Test Rule",
      description: "A test rule with description",
      conditions: ["test_condition"],
      actions: ["test_action"],
      createdBy: "admin"
    },
    4: {
      name: "Tagged Rule",
      conditions: ["test_condition"],
      actions: ["test_action"],
      tags: ["urgent", "customer-service"],
      createdBy: "admin"
    },
    5: {
      name: "John Doe Rule",
      description: "Rule for John Doe processing",
      conditions: ["user_name_contains('john')", "time_between('09:00', '17:00')"],
      actions: ["assign_to_support", "send_email"],
      tags: ["Customer", "VIP"],
      createdBy: "admin"
    },
    6: {
      name: "Jane Smith Rule",
      description: "Rule for Jane Smith processing",
      conditions: ["user_name_contains('jane')", "priority_high"],
      actions: ["escalate", "notify_manager"],
      tags: ["Customer"],
      createdBy: "admin"
    },
    7: {
      name: "Bob Johnson Rule",
      description: "Rule for Bob Johnson processing",
      conditions: ["user_name_contains('bob')", "vip_customer"],
      actions: ["priority_handling", "send_notification"],
      tags: ["VIP", "Premium"],
      createdBy: "admin"
    },
    8: {
      name: "Alice Brown Rule",
      description: "Rule for Alice Brown processing",
      conditions: ["user_name_contains('alice')", "lead_qualification"],
      actions: ["assign_to_sales", "track_conversion"],
      tags: ["Premium"],
      createdBy: "admin"
    }
  };

  if (!baseRules[variant]) {
    throw new Error(`AiRule variant ${variant} not found. Available variants: ${Object.keys(baseRules).join(', ')}`);
  }

  return { ...baseRules[variant] };
}

// Specialized creator functions for specific test scenarios
export function createMinimalAiRule(): AiRuleCreateInput {
  return createAiRule(2);
}

export function createFullAiRule(): AiRuleCreateInput {
  return createAiRule(1);
}

export function createAiRuleWithDescription(): AiRuleCreateInput {
  return createAiRule(3);
}

export function createAiRuleWithTags(): AiRuleCreateInput {
  return createAiRule(4);
}

// Creator for multiple rules (useful for bulk operations and search tests)
export function createMultipleAiRules(): AiRuleCreateInput[] {
  return [
    createAiRule(5), // John Doe Rule
    createAiRule(6), // Jane Smith Rule
    createAiRule(7), // Bob Johnson Rule
    createAiRule(8)  // Alice Brown Rule
  ];
}

// Creator for simple test rules (useful for basic CRUD operations)
export function createSimpleAiRules(): AiRuleCreateInput[] {
  return [
    { name: "A", conditions: ["1"], actions: ["a"], createdBy: "admin" },
    { name: "B", conditions: ["2"], actions: ["b"], createdBy: "admin" },
    { name: "C", conditions: ["3"], actions: ["c"], createdBy: "admin" }
  ];
}

// Creator for rules with specific tags (useful for filtering tests)
export function createAiRulesWithTags(): AiRuleCreateInput[] {
  return [
    { name: "John Doe", conditions: ["x"], actions: ["a"], createdBy: "admin", tags: ["Customer", "VIP"] },
    { name: "Jane Smith", conditions: ["y"], actions: ["b"], createdBy: "admin", tags: ["Lead", "Potential"] },
    { name: "Bob Johnson", conditions: ["z"], actions: ["c"], createdBy: "admin", tags: ["Customer"] },
    { name: "Alice Brown", conditions: ["a"], actions: ["d"], createdBy: "admin", tags: ["VIP"] }
  ];
}

// Update data creators
export function createAiRuleUpdate(variant: number): AiRuleUpdateInput {
  const baseUpdates: Record<number, AiRuleUpdateInput> = {
    1: {
      name: "Updated Rule",
      description: "Updated description",
      conditions: ["updated_condition"],
      actions: ["updated_action"],
      tags: ["VIP", "Premium"],
      isActive: false,
      updatedBy: "admin"
    },
    2: {
      name: "New Name",
      updatedBy: "admin"
    },
    3: {
      description: "Updated description only",
      updatedBy: "admin"
    },
    4: {
      tags: ["new-tag", "updated-tag"],
      updatedBy: "admin"
    },
    5: {
      isActive: false,
      updatedBy: "admin"
    }
  };

  if (!baseUpdates[variant]) {
    throw new Error(`AiRule update variant ${variant} not found. Available variants: ${Object.keys(baseUpdates).join(', ')}`);
  }

  return { ...baseUpdates[variant] };
}

// Specialized update creators
export function createFullAiRuleUpdate(): AiRuleUpdateInput {
  return createAiRuleUpdate(1);
}

export function createNameOnlyUpdate(): AiRuleUpdateInput {
  return createAiRuleUpdate(2);
}

export function createDescriptionOnlyUpdate(): AiRuleUpdateInput {
  return createAiRuleUpdate(3);
}

export function createTagsOnlyUpdate(): AiRuleUpdateInput {
  return createAiRuleUpdate(4);
}

export function createStatusOnlyUpdate(): AiRuleUpdateInput {
  return createAiRuleUpdate(5);
}

// Invalid update data creators for validation tests
export function createInvalidUpdate(type: 'empty-name' | 'empty-conditions' | 'empty-actions' | 'empty-object'): any {
  const invalidUpdates = {
    'empty-name': {
      name: "",
      updatedBy: "admin"
    },
    'empty-conditions': {
      conditions: [],
      updatedBy: "admin"
    },
    'empty-actions': {
      actions: [],
      updatedBy: "admin"
    },
    'empty-object': {}
  };

  return invalidUpdates[type];
}

// Update with whitespace for trimming tests
export function createUpdateWithWhitespace(): AiRuleUpdateInput {
  return {
    name: "   Trimmed Name   ",
    updatedBy: "admin"
  };
}

// Update for duplicate name testing
export function createDuplicateNameUpdate(existingName: string): AiRuleUpdateInput {
  return {
    name: existingName,
    updatedBy: "admin"
  };
}

// Update with same name (no change scenario)
export function createSameNameUpdate(): AiRuleUpdateInput {
  return {
    name: "Simple Rule", // Same as createMinimalAiRule
    description: "Updated description",
    updatedBy: "admin"
  };
}

// Rule for soft delete testing
export function createRuleForSoftDelete(): AiRuleCreateInput {
  return {
    name: "To Be Deleted",
    conditions: ["cond"],
    actions: ["act"],
    createdBy: "admin"
  };
}

// Update for soft deleted rule testing
export function createUpdateForSoftDeleted(): AiRuleUpdateInput {
  return {
    name: "Should Not Work",
    updatedBy: "admin"
  };
}

// Update with whitespace in all fields for comprehensive trimming test
export function createUpdateWithAllFieldsWhitespace(): AiRuleUpdateInput {
  return {
    name: "   Trimmed Name   ",
    description: "   Trimmed Description   ",
    conditions: ["   trimmed_condition   "],
    actions: ["   trimmed_action   "],
    tags: ["   tag1   ", "   tag2   "],
    updatedBy: "admin"
  };
}

// Rule for trimming test
export function createRuleForTrimming(): AiRuleCreateInput {
  return {
    name: "Original Rule",
    conditions: ["cond"],
    actions: ["act"],
    createdBy: "admin"
  };
}

// Rule for active status testing
export function createActiveRule(): AiRuleCreateInput {
  return {
    name: "Active Rule",
    conditions: ["cond"],
    actions: ["act"],
    isActive: true,
    createdBy: "admin"
  };
}

// Update for status change testing
export function createStatusChangeUpdate(): AiRuleUpdateInput {
  return {
    isActive: false,
    updatedBy: "admin"
  };
}

// ========================================
// PARAMS CREATORS FOR implHandleGetAllAiRules
// ========================================

// Search params
export function createSearchByNameParams() {
  return { search: "John" };
}

export function createSearchByDescriptionParams() {
  return { search: "processing" };
}

export function createEmptySearchParams() {
  return { search: "" };
}

export function createWhitespaceSearchParams() {
  return { search: "   " };
}

export function createNonExistentSearchParams() {
  return { search: "NonExistent" };
}

// Filter params
export function createVipTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "VIP" }]
  };
}

export function createCustomerTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "Customer" }]
  };
}

// Pagination params
export function createPaginationParams() {
  return {
    page: 1,
    limit: 2
  };
}

// Sorting params
export function createSortByNameAscParams() {
  return {
    sorts: [{ field: "name", direction: "asc" as const }]
  };
}

// Combined params
export function createSearchAndTagParams() {
  return {
    search: "John",
    tag: "VIP"
  };
}

// Include deleted params
export function createIncludeDeletedParams() {
  return { includeDeleted: true };
}

// Legacy tag params (converted to filters format)
export function createEmptyTagParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceTagParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

export function createNonExistentTagParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

// Additional search params for read.test.ts
export function createSearchByTagParams() {
  return { search: "VIP" };
}

export function createUnmatchedSearchParams() {
  return { search: "nonexistent" };
}

export function createUndefinedSearchParams() {
  return { search: undefined };
}

// Additional filter params for read.test.ts
export function createNonExistentFilterParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

export function createEmptyFilterFieldParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceFilterFieldParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

// ========================================
// CREATORS FOR DELETE TESTS
// ========================================

// Rule for retry delete testing
export function createRetryDeleteRule(): AiRuleCreateInput {
  return {
    name: "Retry Delete",
    conditions: ["attempt"],
    actions: ["log"],
    createdBy: "admin"
  };
}

// ========================================
// CREATORS FOR SPECIAL CASES (AiRule-specific)
// ========================================

// Rule with special characters and unicode
export function createSpecialCharacterRule(): AiRuleCreateInput {
  return {
    name: "José María O'Connor",
    description: "Handles unicode 🎉 & symbols",
    conditions: ["name.includes('José')"],
    actions: ["notify", "log"],
    tags: ["Special", "🚀", "Test@Tag"],
    createdBy: "admin"
  };
}

// Rule with very long content (AiRule-specific test)
export function createLongContentRule(): AiRuleCreateInput {
  return {
    name: "Very Long Rule Name That Exceeds Normal Length Expectations And Tests System Limits",
    description: "This is a very long description that tests how the system handles extensive text content in rule descriptions. It includes multiple sentences and should test the limits of what the system can handle in terms of content length and processing.",
    conditions: [
      "user.message.length > 1000",
      "user.message.includes('very long query with lots of details')",
      "user.session.duration > 3600"
    ],
    actions: [
      "log_extensive_details",
      "notify_admin_of_long_interaction",
      "create_detailed_report",
      "escalate_to_specialist"
    ],
    tags: ["LongContent", "EdgeCase", "SystemLimits", "Performance"],
    createdBy: "admin"
  };
}

// Rule with edge case conditions (AiRule-specific)
export function createEdgeCaseConditionsRule(): AiRuleCreateInput {
  return {
    name: "Edge Case Conditions",
    description: "Tests complex condition parsing",
    conditions: [
      "user.age >= 18 && user.age <= 65",
      "user.location.country === 'US' || user.location.country === 'CA'",
      "user.preferences.notifications === true"
    ],
    actions: [
      "apply_regional_rules",
      "send_age_appropriate_content"
    ],
    tags: ["EdgeCase", "Complex"],
    createdBy: "admin"
  };
}

// Rule with complex actions (AiRule-specific)
export function createComplexActionsRule(): AiRuleCreateInput {
  return {
    name: "Complex Actions Rule",
    description: "Tests complex action execution",
    conditions: ["trigger_complex_workflow"],
    actions: [
      "webhook.call('https://api.example.com/notify')",
      "database.update('user_stats', {last_interaction: now()})",
      "email.send(template='complex_notification', to=user.email)",
      "analytics.track('complex_rule_triggered', {rule_id: this.id})"
    ],
    tags: ["Complex", "Integration"],
    createdBy: "admin"
  };
}

// Rule with empty optional fields (AiRule-specific edge case)
export function createEmptyOptionalFieldsRule(): AiRuleCreateInput {
  return {
    name: "Empty Optional Fields",
    conditions: ["basic_condition"],
    actions: ["basic_action"],
    description: "",
    tags: [],
    createdBy: "admin"
  };
}

// Rule for testing AI-specific business logic
export function createAiLogicRule(): AiRuleCreateInput {
  return {
    name: "AI Decision Rule",
    description: "Tests AI-specific decision making logic",
    conditions: [
      "ai.confidence > 0.8",
      "ai.model === 'gpt-4'",
      "ai.context.length > 100"
    ],
    actions: [
      "ai.respond_with_confidence",
      "ai.log_decision_path",
      "ai.update_learning_model"
    ],
    tags: ["AI", "MachineLearning", "Confidence"],
    createdBy: "admin"
  };
}

// Creators for delete test scenarios
export function createComplexAiRule(): AiRuleCreateInput {
  return {
    name: "Complex Rule",
    description: "Full field test",
    conditions: ["user.role == 'admin'"],
    actions: ["grant_access", "log_activity"],
    tags: ["admin", "security"],
    isActive: true,
    createdBy: "admin"
  };
}

export function createMinimalDeleteRule(): AiRuleCreateInput {
  return {
    name: "Minimal Rule",
    conditions: ["is.loggedIn"],
    actions: ["alert"],
    createdBy: "admin"
  };
}

// Rules for testing deletion effects on other rules
export function createRulesForDeletionTest(): AiRuleCreateInput[] {
  return [
    { name: "Keep This One", conditions: ["x"], actions: ["a"], createdBy: "admin" },
    { name: "Delete This One", conditions: ["y"], actions: ["b"], createdBy: "admin" },
    { name: "Keep This Too", conditions: ["z"], actions: ["c"], createdBy: "admin" }
  ];
}

// Creators for bulk operations testing
export function createExistingRule(): AiRuleCreateInput {
  return {
    name: "Existing Rule",
    description: "An existing rule",
    conditions: ["User says test"],
    actions: ["Show test response"],
    createdBy: "admin"
  };
}

export function createDuplicateRulesForBulk(): AiRuleCreateInput[] {
  return [
    {
      name: "Existing Rule", // Duplicate name
      description: "Another rule with same name",
      conditions: ["User says hello"],
      actions: ["Show greeting"],
      createdBy: "admin"
    },
    {
      name: "New Rule",
      description: "A new rule",
      conditions: ["User says goodbye"],
      actions: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// Rules for bulk update testing
export function createRulesForBulkUpdate(): AiRuleCreateInput[] {
  return [
    {
      name: "Rule 1",
      description: "First rule",
      conditions: ["User says hello"],
      actions: ["Show greeting"],
      createdBy: "admin"
    },
    {
      name: "Rule 2",
      description: "Second rule",
      conditions: ["User says goodbye"],
      actions: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// Bulk update data
export function createBulkUpdateData(): any[] {
  return [
    {
      name: "Updated Rule 1",
      description: "Updated first rule",
      updatedBy: "admin"
    },
    {
      name: "Updated Rule 2",
      description: "Updated second rule",
      updatedBy: "admin"
    }
  ];
}

// Rules for bulk delete testing
export function createRulesForBulkDelete(): AiRuleCreateInput[] {
  return [
    {
      name: "Rule 1",
      description: "First rule",
      conditions: ["User says hello"],
      actions: ["Show greeting"],
      createdBy: "admin"
    },
    {
      name: "Rule 2",
      description: "Second rule",
      conditions: ["User says goodbye"],
      actions: ["Show farewell"],
      createdBy: "admin"
    },
    {
      name: "Rule 3",
      description: "Third rule",
      conditions: ["User asks question"],
      actions: ["Show help"],
      createdBy: "admin"
    }
  ];
}

// Invalid data creators for validation tests
export function createInvalidAiRule(type: 'missing-name' | 'missing-conditions' | 'missing-actions' | 'empty-conditions' | 'empty-actions' | 'missing-phone'): any {
  const invalidRules = {
    'missing-phone': {
      name: "John Doe"
    },
    'missing-name': {
      conditions: ["test_condition"],
      actions: ["test_action"],
      createdBy: "admin"
    },
    'missing-conditions': {
      name: "Invalid Rule",
      actions: ["test_action"],
      createdBy: "admin"
    },
    'missing-actions': {
      name: "Invalid Rule",
      conditions: ["test_condition"],
      createdBy: "admin"
    },
    'empty-conditions': {
      name: "Invalid Rule",
      conditions: [],
      actions: ["test_action"],
      createdBy: "admin"
    },
    'empty-actions': {
      name: "Invalid Rule",
      conditions: ["test_condition"],
      actions: [],
      createdBy: "admin"
    }
  };

  return invalidRules[type];
}

// Creator for rules with special characteristics
export function createAiRuleWithWhitespace(): AiRuleCreateInput {
  return {
    name: "  Trimmed Rule  ",
    conditions: ["test_condition"],
    actions: ["test_action"],
    createdBy: "admin"
  };
}

export function createAiRuleWithManyTags(): AiRuleCreateInput {
  return {
    name: "Multi-tag Rule",
    conditions: ["test_condition"],
    actions: ["test_action"],
    tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    createdBy: "admin"
  };
}

export function createAiRuleWithoutDescription(): AiRuleCreateInput {
  return {
    name: "Rule without description",
    conditions: ["test_condition"],
    actions: ["test_action"],
    createdBy: "admin"
  };
}

export function createAiRuleWithEmptyTags(): AiRuleCreateInput {
  return {
    name: "Rule with empty tags",
    conditions: ["test_condition"],
    actions: ["test_action"],
    tags: [],
    createdBy: "admin"
  };
}

// Duplicate rule creator for conflict testing
export function createDuplicateAiRule(): AiRuleCreateInput {
  return {
    name: "Duplicate Rule",
    conditions: ["condition1"],
    actions: ["action1"],
    createdBy: "admin"
  };
}

export function createSecondDuplicateAiRule(): AiRuleCreateInput {
  return {
    name: "Duplicate Rule", // Same name as above
    conditions: ["condition2"],
    actions: ["action2"],
    createdBy: "admin"
  };
}

// Test rule with specific name for soft delete tests
export function createTestRule(): AiRuleCreateInput {
  return {
    name: "Test Rule",
    conditions: ["condition1"],
    actions: ["action1"],
    createdBy: "admin"
  };
}

export function createTestRule2(): AiRuleCreateInput {
  return {
    name: "Test Rule",
    conditions: ["condition2"],
    actions: ["action2"],
    createdBy: "admin"
  };
}
