//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface";
import { ContactBusinessLogic } from "@/lib/repositories/contacts/BusinessLogic";
import { MongoContactRepository } from "@/lib/repositories/contacts/MongoRepository";
import { TestContactDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  createMultipleContacts,
  createSimpleContacts,
  createExistingContact,
  createDuplicateContactsForBulk,
  createContactsForBulkUpdate,
  createBulkUpdateData,
  createContactsForBulkDelete
} from "./object_creator";

describe("Contact Bulk Operations Tests", () => {
  let businessLogic: ContactBusinessLogicInterface;
  let dbRepository: TestContactDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Contact");
    await driver.connect()
    const originalDb = new MongoContactRepository(driver);
    dbRepository = new TestContactDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ContactBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("Bulk Create", () => {
    it("should successfully create multiple contacts", async () => {
      const contactsData = createMultipleContacts();

      const result = await businessLogic.bulkCreate(contactsData);

      expect(result).toHaveLength(contactsData.length);
      expect(result[0].name).toBe(contactsData[0].name);
      expect(result[1].name).toBe(contactsData[1].name);
      expect(result[2].name).toBe(contactsData[2].name);
      expect(await dbRepository.getContactCount()).toBe(contactsData.length);
    });

    it("should fail if any contact has duplicate name", async () => {
      const existingContact = createExistingContact();
      await businessLogic.create(existingContact);

      const contactsData = createDuplicateContactsForBulk();

      await expect(businessLogic.bulkCreate(contactsData)).rejects.toThrow(
        "Duplicate name found: Existing Contact"
      );

      expect(await dbRepository.getContactCount()).toBe(1);
    });

    it("should handle simple contacts creation", async () => {
      const contactsData = createSimpleContacts();

      const result = await businessLogic.bulkCreate(contactsData);

      expect(result).toHaveLength(contactsData.length);
      expect(await dbRepository.getContactCount()).toBe(contactsData.length);
    });
  });

  describe("Bulk Update", () => {
    it("should successfully update multiple contacts", async () => {
      const contactsData = createContactsForBulkUpdate();
      const contact1 = await businessLogic.create(contactsData[0]);
      const contact2 = await businessLogic.create(contactsData[1]);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: contact1.id, data: updateData[0] },
        { id: contact2.id, data: updateData[1] },
      ];

      const updatedCount = await businessLogic.bulkUpdate(updates);

      expect(updatedCount).toBe(2);

      const updatedContact1 = await businessLogic.getById(contact1.id);
      const updatedContact2 = await businessLogic.getById(contact2.id);

      expect(updatedContact1?.name).toBe(updateData[0].name);
      expect(updatedContact1?.updatedBy).toBe(updateData[0].updatedBy);
      expect(updatedContact2?.name).toBe(updateData[1].name);
      expect(updatedContact2?.updatedBy).toBe(updateData[1].updatedBy);
    });

    it("should fail if any contact doesn't exist", async () => {
      const contactData = createContactsForBulkUpdate()[0];
      const contact1 = await businessLogic.create(contactData);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: contact1.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ];

      await expect(businessLogic.bulkUpdate(updates)).rejects.toThrow();
    });

    it("should fail if any update would create duplicate name", async () => {
      const contactsData = createContactsForBulkUpdate();
      await businessLogic.create(contactsData[0]); // Create first contact
      const contact2 = await businessLogic.create(contactsData[1]); // Create second contact

      const updates = [
        {
          id: contact2.id,
          data: { name: contactsData[0].name, updatedBy: "admin" }, // Try to update second contact with first contact's name
        },
      ];

      await expect(businessLogic.bulkUpdate(updates)).rejects.toThrow(
        "Duplicate name in update: Contact 1"
      );
    });
  });

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple contacts", async () => {
      const contactsData = createContactsForBulkDelete();
      const contact1 = await businessLogic.create(contactsData[0]);
      const contact2 = await businessLogic.create(contactsData[1]);
      const contact3 = await businessLogic.create(contactsData[2]);

      const deletedCount = await businessLogic.bulkDelete([contact1.id, contact2.id]);

      expect(deletedCount).toBe(2);
      expect(await dbRepository.getContactCount()).toBe(1); // Only non-deleted

      expect(await businessLogic.getById(contact1.id)).toBeNull();
      expect(await businessLogic.getById(contact2.id)).toBeNull();
      expect(await businessLogic.getById(contact3.id)).not.toBeNull();
    });

    it("should successfully hard delete multiple contacts", async () => {
      const contactsData = createContactsForBulkDelete();
      const contact1 = await businessLogic.create(contactsData[0]);
      const contact2 = await businessLogic.create(contactsData[1]);

      const deletedCount = await businessLogic.bulkDelete([contact1.id, contact2.id], true);

      expect(deletedCount).toBe(2);
      expect(await dbRepository.getContactCount()).toBe(0);
    });

    it("should fail if any contact doesn't exist", async () => {
      const contactData = createContactsForBulkDelete()[0];
      const contact1 = await businessLogic.create(contactData);

      await expect(businessLogic.bulkDelete([contact1.id, "non-existent-id"])).rejects.toThrow();
    });

    it("should fail with empty contact IDs", async () => {
      await expect(businessLogic.bulkDelete(["", "valid-id"])).rejects.toThrow(
        "ID is required"
      );
    });
  });
});
