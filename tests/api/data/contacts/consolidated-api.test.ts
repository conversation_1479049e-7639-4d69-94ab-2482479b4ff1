//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface";
import { ContactBusinessLogic } from "@/lib/repositories/contacts/BusinessLogic";
import { MongoContactRepository } from "@/lib/repositories/contacts/MongoRepository";
import { TestContactDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateContact, implHandleGetAllContacts } from "@/app/api/v1/contacts/impl";
import {
  createMultipleContacts,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams
} from "./object_creator";

describe("Consolidated Contact API Tests", () => {
  let businessLogic: ContactBusinessLogicInterface;
  let dbRepository: TestContactDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Contact");
    await driver.connect()
    const originalDb = new MongoContactRepository(driver);
    dbRepository = new TestContactDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ContactBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  const testContacts = createMultipleContacts();

  describe("implHandleGetAllContacts - Consolidated Function", () => {
    beforeEach(async () => {
      for (const contactsData of testContacts) {
        await implHandleCreateContact(contactsData, businessLogic);
      }
    });

    it("should get all contacts when no parameters provided", async () => {
      const result = await implHandleGetAllContacts(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);
      expect(result.body.data?.total).toBe(4);
    });

    it("should search contacts by name", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Contact and Bob Johnson Contact

      const names = result.body.data?.items.map((c: any) => c.name);
      expect(names).toContain(testContacts[0].name); // John Doe Contact
      expect(names).toContain(testContacts[2].name); // Bob Johnson Contact
    });

    it("should search contacts by description", async () => {
      const params = createSearchByDescriptionParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4); // All contacts have "processing" in description
    });

    it("should filter contacts by tag", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Contact and Bob Johnson Contact

      const names = result.body.data?.items.map((c: any) => c.name);
      expect(names).toContain(testContacts[0].name); // John Doe Contact
      expect(names).toContain(testContacts[2].name); // Bob Johnson Contact
    });

    it("should filter contacts by Customer tag", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Contact and Jane Smith Contact

      const names = result.body.data?.items.map((c: any) => c.name);
      expect(names).toContain(testContacts[0].name); // John Doe Contact
      expect(names).toContain(testContacts[1].name); // Jane Smith Contact
    });

    it("should handle pagination", async () => {
      const params = createPaginationParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(4);
    });

    it("should handle sorting by name", async () => {
      const params = createSortByNameAscParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);

      const names = result.body.data?.items.map((c: any) => c.name);
      expect(names![0]).toBe(testContacts[3].name); // Alice Brown Contact
      expect(names![1]).toBe(testContacts[2].name); // Bob Johnson Contact
      expect(names![2]).toBe(testContacts[1].name); // Jane Smith Contact
      expect(names![3]).toBe(testContacts[0].name); // John Doe Contact
    });

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // VIP contacts (tag filter applied)
    });

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams();
      const result = await implHandleGetAllContacts(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should include soft deleted contacts when specified", async () => {
      // Soft delete one contacts
      const allContacts = await businessLogic.getAll();
      const contactsToDelete = allContacts.items[0];
      await businessLogic.delete(contactsToDelete.id);

      // Get all without including deleted
      const resultWithoutDeleted = await implHandleGetAllContacts(businessLogic);
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3);

      // Get all including deleted
      const params = createIncludeDeletedParams();
      const resultWithDeleted = await implHandleGetAllContacts(businessLogic, params);
      expect(resultWithDeleted.body.data?.items).toHaveLength(4);
    });
  });
});
