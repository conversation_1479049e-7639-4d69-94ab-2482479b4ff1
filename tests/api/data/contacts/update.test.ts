//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface";
import { ContactBusinessLogic } from "@/lib/repositories/contacts/BusinessLogic";
import { MongoContactRepository } from "@/lib/repositories/contacts/MongoRepository";
import { TestContactDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateContact, implHandleUpdateContact } from "@/app/api/v1/contacts/impl";
import {
  createFullContact,
  createMinimalContact,
  createFullContactUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createContactForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createContactForTrimming,
  createActiveContact,
  createStatusChangeUpdate
} from "./object_creator";

describe("Update Contact API Tests", () => {
  let businessLogic: ContactBusinessLogicInterface;
  let dbRepository: TestContactDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Contact");
    await driver.connect();
    const originalDb = new MongoContactRepository(driver);
    dbRepository = new TestContactDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ContactBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("PUT /api/v1/contacts/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullContact();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      const updateData = createFullContactUpdate();
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.name).toBe(updateData.name);
      expect(result.body.data?.description).toBe(updateData.description);
      expect(result.body.data?.phone).toEqual(updateData.phone);
      expect(result.body.data?.email).toEqual(updateData.email);
      expect(result.body.data?.tags).toEqual(updateData.tags);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should update only the name", async () => {
      const createData = createMinimalContact();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.name).toBe(updateData.name);
      expect(result.body.data?.phone).toEqual(createData.phone);
      expect(result.body.data?.email).toEqual(createData.email);
    });

    it("should trim name when updating", async () => {
      const createData = createMinimalContact();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      const updateData = createUpdateWithWhitespace();
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.name).toBe("Trimmed Name");
    });

    it("should fail to update non-existent contact", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateContact("507f1f77bcf86cd799439011", updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Contact not found");
    });

    it("should fail with invalid input: empty name", async () => {
      const createData = createMinimalContact();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-name');
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with invalid input: empty phone", async () => {
      const createData = createMinimalContact();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-phone');
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty update object", async () => {
      const createData = createMinimalContact();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-object');
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("No data provided for update");
    });

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateContact("", updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Contact ID is required");
    });

    it("should fail with duplicate name", async () => {
      // Create first contact
      const createData1 = createMinimalContact();
      await implHandleCreateContact(createData1, businessLogic);

      // Create second contact
      const createData2 = createFullContact();
      const createResult2 = await implHandleCreateContact(createData2, businessLogic);

      // Try to update second contact with first contact's name
      const updateData = createDuplicateNameUpdate(createData1.name);
      const result = await implHandleUpdateContact(createResult2.body.data.id, updateData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Another Contact with this name exists");
    });

    it("should allow updating contact with same name (no change)", async () => {
      const createData = createMinimalContact();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      const updateData = createSameNameUpdate();
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.name).toBe(updateData.name);
      expect(result.body.data?.description).toBe(updateData.description);
    });

    it("should fail to update soft-deleted contact", async () => {
      const createData = createContactForSoftDelete();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      // Soft delete the contact
      await businessLogic.delete(contactsId);

      // Try to update the soft-deleted contact
      const updateData = createUpdateForSoftDeleted();
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Contact not found");
    });

    it("should trim all string fields when updating", async () => {
      const createData = createContactForTrimming();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      const updateData = createUpdateWithAllFieldsWhitespace();
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.name).toBe("Trimmed Name");
      expect(result.body.data?.description).toBe("Trimmed Description");
      expect(result.body.data?.phone).toEqual(["trimmed_condition"]);
      expect(result.body.data?.email).toEqual(["trimmed_action"]);
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"]);
    });

    it("should fail with invalid input: empty email", async () => {
      const createData = createMinimalContact();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-email');
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should update isActive status", async () => {
      const createData = createActiveContact();
      const createResult = await implHandleCreateContact(createData, businessLogic);
      const contactsId = createResult.body.data.id;

      const updateData = createStatusChangeUpdate();
      const result = await implHandleUpdateContact(contactsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
    });
  });
});
