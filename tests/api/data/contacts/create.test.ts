//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ContactBusinessLogicInterface } from "@/lib/repositories/contacts/interface";
import { ContactBusinessLogic } from "@/lib/repositories/contacts/BusinessLogic";
import { MongoContactRepository } from "@/lib/repositories/contacts/MongoRepository";
import { TestContactDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateContact } from "@/app/api/v1/contacts/impl";
import {
  createFullContact,
  createMinimalContact,
  createContactWithDescription,
  createContactWithTags,
  createContactWithWhitespace,
  createDuplicateContact,
  createSecondDuplicateContact,
  createInvalidContact,
  createContactWithManyTags,
  createContactWithoutDescription,
  createContactWithEmptyTags
} from "./object_creator";

describe("Create Contact API Tests", () => {
  let businessLogic: ContactBusinessLogicInterface;
    let dbRepository: TestContactDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("Contact");
      await driver.connect()
      const originalDb = new MongoContactRepository(driver);
      dbRepository = new TestContactDBRepositoryWrapper(originalDb, driver);
      businessLogic = new ContactBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("POST /api/v1/contacts", () => {
    it("should successfully create a new contacts with all fields", async () => {
      const contactsData = createFullContact();

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.name).toBe(contactsData.name);
      expect(result.body.data?.description).toBe(contactsData.description);
      expect(result.body.data?.phone).toEqual(contactsData.phone);
      expect(result.body.data?.email).toEqual(contactsData.email);
      expect(result.body.data?.tags).toEqual(contactsData.tags);
      expect(result.body.data?.isActive).toBe(contactsData.isActive);
      expect(result.body.data?.id).toBeDefined();
      expect(result.body.data?.createdAt).toBeDefined();
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should successfully create a contacts with minimal required fields", async () => {
      const contactsData = createMinimalContact();

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.name).toBe(contactsData.name);
      expect(result.body.data?.phone).toEqual(contactsData.phone);
      expect(result.body.data?.email).toEqual(contactsData.email);
      expect(result.body.data?.isActive).toBe(true); // Should default to true
      expect(result.body.data?.tags).toEqual([]);
    });

    it("should create contact with description", async () => {
      const contactsData = createContactWithDescription();

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.description).toBe(contactsData.description);
    });

    it("should create contact with tags", async () => {
      const contactsData = createContactWithTags();

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.tags).toEqual(contactsData.tags);
    });

    it("should trim whitespace from name", async () => {
      const contactsData = createContactWithWhitespace();

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.name).toBe("Trimmed Contact");
    });

    it("should fail with duplicate name", async () => {
      // Create first contacts
      const contactsData1 = createDuplicateContact();
      await implHandleCreateContact(contactsData1, businessLogic);

      // Try to create second contacts with same name
      const contactsData2 = createSecondDuplicateContact();
      const result = await implHandleCreateContact(contactsData2, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Contact with the same name already exists");
    });

    it("should fail with missing name", async () => {
      const contactsData = {
        phone: "+6281234567890"
      };

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing phone", async () => {
      const contactsData = createInvalidContact('missing-phone');


      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing phone", async () => {
      const contactsData = createInvalidContact('missing-phone');

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("phone");
    });

    it("should fail with missing email", async () => {
      const contactsData = createInvalidContact('missing-email');

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("email");
    });

    it("should fail with empty phone array", async () => {
      const contactsData = createInvalidContact('empty-phone');

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with empty email array", async () => {
      const contactsData = createInvalidContact('empty-email');

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should create contact with many tags", async () => {
      const contactsData = createContactWithManyTags();

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(contactsData.tags);
    });

    it("should handle optional description", async () => {
      const contactsData = createContactWithoutDescription();

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.description).toBe("");
    });

    it("should handle empty arrays for tags", async () => {
      const contactsData = createContactWithEmptyTags();

      const result = await implHandleCreateContact(contactsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(contactsData.tags);
    });
  });
});
