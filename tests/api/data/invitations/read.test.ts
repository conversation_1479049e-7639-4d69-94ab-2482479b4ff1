// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { InvitationBusinessLogicInterface } from "@/lib/repositories/invitations/interface";
import { InvitationBusinessLogic } from "@/lib/repositories/invitations/BusinessLogic";
import { MongoInvitationRepository } from "@/lib/repositories/invitations/MongoRepository";
import { TestInvitationDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateInvitation, implHandleGetInvitation, implHandleGetAllInvitations } from "@/app/api/v1/invitations/impl";
import {
  createInvitation,
  createSimpleInvitations,
  createInvitationsWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams
} from "./object_creator";

describe("Read Invitation API Tests", () => {
  let businessLogic: InvitationBusinessLogicInterface;
  let dbRepository: TestInvitationDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Invitation");
    await driver.connect();
    const originalDb = new MongoInvitationRepository(driver);
    dbRepository = new TestInvitationDBRepositoryWrapper(originalDb, driver);
    businessLogic = new InvitationBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("GET /api/v1/invitations/:id", () => {
    it("should successfully get invitation by ID", async () => {
      const invitation = createInvitation(5); // John Doe Invitation

      const createResult = await implHandleCreateInvitation(invitation, businessLogic);
      const id = createResult.body.data.id;

      const result = await implHandleGetInvitation(id, businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.id).toBe(id);
      expect(result.body.data?.STRING_FIELD).toBe(invitation.STRING_FIELD);
    });

    it("should fail to get non-existent invitation", async () => {
      const result = await implHandleGetInvitation("507f1f77bcf86cd799439011", businessLogic);
      expect(result.status).toBe(404);
    });

    it("should fail with empty invitation ID", async () => {
      const result = await implHandleGetInvitation("", businessLogic);
      expect(result.status).toBe(400);
    });

    it("should fail with whitespace-only invitation ID", async () => {
      const result = await implHandleGetInvitation("   ", businessLogic);
      expect(result.status).toBe(400);
    });
  });

  describe("GET /api/v1/invitations", () => {
    it("should get all invitations", async () => {
      const invitations = createSimpleInvitations();
      for (const r of invitations) await implHandleCreateInvitation(r, businessLogic);

      const result = await implHandleGetAllInvitations(businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(3);
    });

    it("should return empty when no invitations exist", async () => {
      const result = await implHandleGetAllInvitations(businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });
  });

  describe("GET /api/v1/invitations/search", () => {
    beforeEach(async () => {
      const data = createInvitationsWithTags();
      for (const r of data) await implHandleCreateInvitation(r, businessLogic);
    });

    it("should search by STRING_FIELD", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should search by tag", async () => {
      const params = createSearchByTagParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(4);
    });
  });

  describe("GET /api/v1/invitations/filters", () => {
    beforeEach(async () => {
      const data = createInvitationsWithTags();
      for (const r of data) await implHandleCreateInvitation(r, businessLogic);
    });

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);
      expect(result.status).toBe(400);
    });
  });
});
