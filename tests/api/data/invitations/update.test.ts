//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { InvitationBusinessLogicInterface } from "@/lib/repositories/invitations/interface";
import { InvitationBusinessLogic } from "@/lib/repositories/invitations/BusinessLogic";
import { MongoInvitationRepository } from "@/lib/repositories/invitations/MongoRepository";
import { TestInvitationDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateInvitation, implHandleUpdateInvitation, implHandleDeleteInvitation } from "@/app/api/v1/invitations/impl";
import {
  createFullInvitation,
  createMinimalInvitation,
  createFullInvitationUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createInvitationForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createInvitationForTrimming,
  createActiveInvitation,
  createStatusChangeUpdate
} from "./object_creator";

describe("Update Invitation API Tests", () => {
  let businessLogic: InvitationBusinessLogicInterface;
  let dbRepository: TestInvitationDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Invitation");
    await driver.connect();
    const originalDb = new MongoInvitationRepository(driver);
    dbRepository = new TestInvitationDBRepositoryWrapper(originalDb, driver);
    businessLogic = new InvitationBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("PUT /api/v1/invitations/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullInvitation();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      const updateData = createFullInvitationUpdate();
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2);
      expect(result.body.data?.ARRAY_FIELD).toEqual(updateData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(updateData.variables);
      expect(result.body.data?.tags).toEqual(updateData.tags);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should update only the STRING_FIELD", async () => {
      const createData = createMinimalInvitation();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD).toEqual(createData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(createData.variables);
    });

    it("should trim STRING_FIELD when updating", async () => {
      const createData = createMinimalInvitation();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      const updateData = createUpdateWithWhitespace();
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name");
    });

    it("should fail to update non-existent invitation", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateInvitation("507f1f77bcf86cd799439011", updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invitation not found");
    });

    it("should fail with invalid input: empty STRING_FIELD", async () => {
      const createData = createMinimalInvitation();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-STRING_FIELD');
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with invalid input: empty ARRAY_FIELD", async () => {
      const createData = createMinimalInvitation();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-ARRAY_FIELD');
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty update object", async () => {
      const createData = createMinimalInvitation();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-object');
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("No data provided for update");
    });

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateInvitation("", updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invitation ID is required");
    });

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first invitation
      const createData1 = createMinimalInvitation();
      await implHandleCreateInvitation(createData1, businessLogic);

      // Create second invitation
      const createData2 = createFullInvitation();
      const createResult2 = await implHandleCreateInvitation(createData2, businessLogic);

      // Try to update second invitation with first invitation's STRING_FIELD
      const updateData = createDuplicateNameUpdate(createData1.STRING_FIELD);
      const result = await implHandleUpdateInvitation(createResult2.body.data.id, updateData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Another Invitation with this STRING_FIELD exists");
    });

    it("should allow updating invitation with same STRING_FIELD (no change)", async () => {
      const createData = createMinimalInvitation();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      const updateData = createSameNameUpdate();
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2);
    });

    it("should fail to update soft-deleted invitation", async () => {
      const createData = createInvitationForSoftDelete();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      // Soft delete the invitation
      const deleteResult = await implHandleDeleteInvitation(invitationsId, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Try to update the soft-deleted invitation
      const updateData = createUpdateForSoftDeleted();
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Invitation not found");
    });

    it("should trim all string fields when updating", async () => {
      const createData = createInvitationForTrimming();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      const updateData = createUpdateWithAllFieldsWhitespace();
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name");
      expect(result.body.data?.ARRAY_FIELD2).toBe("Trimmed Description");
      expect(result.body.data?.ARRAY_FIELD).toEqual(["trimmed_condition"]);
      expect(result.body.data?.variables).toEqual(["trimmed_action"]);
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"]);
    });

    it("should fail with invalid input: empty variables", async () => {
      const createData = createMinimalInvitation();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-variables');
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should update isActive status", async () => {
      const createData = createActiveInvitation();
      const createResult = await implHandleCreateInvitation(createData, businessLogic);
      const invitationsId = createResult.body.data.id;

      const updateData = createStatusChangeUpdate();
      const result = await implHandleUpdateInvitation(invitationsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
    });
  });
});
