//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { InvitationBusinessLogicInterface } from "@/lib/repositories/invitations/interface";
import { InvitationBusinessLogic } from "@/lib/repositories/invitations/BusinessLogic";
import { MongoInvitationRepository } from "@/lib/repositories/invitations/MongoRepository";
import { TestInvitationDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateInvitation, implHandleGetAllInvitations, implHandleDeleteInvitation } from "@/app/api/v1/invitations/impl";
import {
  createMultipleInvitations,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams
} from "./object_creator";

describe("Consolidated Invitation API Tests", () => {
  let businessLogic: InvitationBusinessLogicInterface;
  let dbRepository: TestInvitationDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Invitation");
    await driver.connect()
    const originalDb = new MongoInvitationRepository(driver);
    dbRepository = new TestInvitationDBRepositoryWrapper(originalDb, driver);
    businessLogic = new InvitationBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  const testInvitations = createMultipleInvitations();

  describe("implHandleGetAllInvitations - Consolidated Function", () => {
    beforeEach(async () => {
      for (const invitationsData of testInvitations) {
        await implHandleCreateInvitation(invitationsData, businessLogic);
      }
    });

    it("should get all invitations when no parameters provided", async () => {
      const result = await implHandleGetAllInvitations(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);
      expect(result.body.data?.total).toBe(4);
    });

    it("should search invitations by STRING_FIELD", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Invitation and Bob Johnson Invitation

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testInvitations[0].STRING_FIELD); // John Doe Invitation
      expect(STRING_FIELDs).toContain(testInvitations[2].STRING_FIELD); // Bob Johnson Invitation
    });

    it("should search invitations by ARRAY_FIELD2", async () => {
      const params = createSearchByDescriptionParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4); // All invitations have "processing" in ARRAY_FIELD2
    });

    it("should filter invitations by tag", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Invitation and Bob Johnson Invitation

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testInvitations[0].STRING_FIELD); // John Doe Invitation
      expect(STRING_FIELDs).toContain(testInvitations[2].STRING_FIELD); // Bob Johnson Invitation
    });

    it("should filter invitations by Customer tag", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Invitation and Jane Smith Invitation

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testInvitations[0].STRING_FIELD); // John Doe Invitation
      expect(STRING_FIELDs).toContain(testInvitations[1].STRING_FIELD); // Jane Smith Invitation
    });

    it("should handle pagination", async () => {
      const params = createPaginationParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(4);
    });

    it("should handle sorting by STRING_FIELD", async () => {
      const params = createSortByNameAscParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs![0]).toBe(testInvitations[3].STRING_FIELD); // Alice Brown Invitation
      expect(STRING_FIELDs![1]).toBe(testInvitations[2].STRING_FIELD); // Bob Johnson Invitation
      expect(STRING_FIELDs![2]).toBe(testInvitations[1].STRING_FIELD); // Jane Smith Invitation
      expect(STRING_FIELDs![3]).toBe(testInvitations[0].STRING_FIELD); // John Doe Invitation
    });

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // VIP invitations (tag filter applied)
    });

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams();
      const result = await implHandleGetAllInvitations(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should include soft deleted invitations when specified", async () => {
      // Get all invitations first to get one to delete
      const allResult = await implHandleGetAllInvitations(businessLogic);
      expect(allResult.status).toBe(200);
      const invitationsToDelete = allResult.body.data?.items[0];
      expect(invitationsToDelete).toBeDefined();

      // Soft delete one invitations
      const deleteResult = await implHandleDeleteInvitation(invitationsToDelete!.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Get all without including deleted
      const resultWithoutDeleted = await implHandleGetAllInvitations(businessLogic);
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3);

      // Get all including deleted
      const params = createIncludeDeletedParams();
      const resultWithDeleted = await implHandleGetAllInvitations(businessLogic, params);
      expect(resultWithDeleted.body.data?.items).toHaveLength(4);
    });
  });
});
