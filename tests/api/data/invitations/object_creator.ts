import { InvitationCreateInput, InvitationUpdateInput } from "@/lib/repositories/invitations/interface";

/**
 * Factory functions for creating test Invitation objects
 * This allows for consistent test data across all test files
 * and easy modification of test objects in one place
 */

// Base creator functions for different scenarios
export function createInvitation(variant: number): InvitationCreateInput {
  const baseInvitations: Record<number, InvitationCreateInput> = {
    1: {
      STRING_FIELD: "Customer Support Invitation",
      STRING_FIELD2: "Invitation for handling customer support requests",
      ARRAY_FIELD2: ["invitation_message_contains('help')", "time_between('09:00', '17:00')"],
      ARRAY_FIELD: ["assign_to_support", "send_acknowledgment"],
      tags: ["Customer", "VIP"],
      isActive: true,
      createdBy: "admin"
    },
    2: {
      STRING_FIELD: "Simple Invitation",
      ARRAY_FIELD2: ["always_true"],
      ARRAY_FIELD: ["log_message"],
      createdBy: "admin"
    },
    3: {
      STRING_FIELD: "Test Invitation",
      STRING_FIELD2: "A test invitation with STRING_FIELD2",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    4: {
      STRING_FIELD: "Tagged Invitation",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      tags: ["urgent", "customer-service"],
      createdBy: "admin"
    },
    5: {
      STRING_FIELD: "John Doe Invitation",
      STRING_FIELD2: "Invitation for John Doe processing",
      ARRAY_FIELD2: ["invitation_STRING_FIELD_contains('john')", "time_between('09:00', '17:00')"],
      ARRAY_FIELD: ["assign_to_support", "send_ARRAY_FIELD"],
      tags: ["Customer", "VIP"],
      createdBy: "admin"
    },
    6: {
      STRING_FIELD: "Jane Smith Invitation",
      STRING_FIELD2: "Invitation for Jane Smith processing",
      ARRAY_FIELD2: ["invitation_STRING_FIELD_contains('jane')", "priority_high"],
      ARRAY_FIELD: ["escalate", "notify_manager"],
      tags: ["Customer"],
      createdBy: "admin"
    },
    7: {
      STRING_FIELD: "Bob Johnson Invitation",
      STRING_FIELD2: "Invitation for Bob Johnson processing",
      ARRAY_FIELD2: ["invitation_STRING_FIELD_contains('bob')", "vip_customer"],
      ARRAY_FIELD: ["priority_handling", "send_notification"],
      tags: ["VIP", "Premium"],
      createdBy: "admin"
    },
    8: {
      STRING_FIELD: "Alice Brown Invitation",
      STRING_FIELD2: "Invitation for Alice Brown processing",
      ARRAY_FIELD2: ["invitation_STRING_FIELD_contains('alice')", "lead_qualification"],
      ARRAY_FIELD: ["assign_to_sales", "track_conversion"],
      tags: ["Premium"],
      createdBy: "admin"
    }
  };

  if (!baseInvitations[variant]) {
    throw new Error(`Invitation variant ${variant} not found. Available variants: ${Object.keys(baseInvitations).join(', ')}`);
  }

  return { ...baseInvitations[variant] };
}

// Specialized creator functions for specific test scenarios
export function createMinimalInvitation(): InvitationCreateInput {
  return createInvitation(2);
}

export function createFullInvitation(): InvitationCreateInput {
  return createInvitation(1);
}

export function createInvitationWithDescription(): InvitationCreateInput {
  return createInvitation(3);
}

export function createInvitationWithTags(): InvitationCreateInput {
  return createInvitation(4);
}

// Creator for multiple invitations (useful for bulk operations and search tests)
export function createMultipleInvitations(): InvitationCreateInput[] {
  return [
    createInvitation(5), // John Doe Invitation
    createInvitation(6), // Jane Smith Invitation
    createInvitation(7), // Bob Johnson Invitation
    createInvitation(8)  // Alice Brown Invitation
  ];
}

// Creator for simple test invitations (useful for basic CRUD operations)
export function createSimpleInvitations(): InvitationCreateInput[] {
  return [
    { STRING_FIELD: "A", ARRAY_FIELD2: ["1"], ARRAY_FIELD: ["a"], createdBy: "admin" },
    { STRING_FIELD: "B", ARRAY_FIELD2: ["2"], ARRAY_FIELD: ["b"], createdBy: "admin" },
    { STRING_FIELD: "C", ARRAY_FIELD2: ["3"], ARRAY_FIELD: ["c"], createdBy: "admin" }
  ];
}

// Creator for invitations with specific tags (useful for filtering tests)
export function createInvitationsWithTags(): InvitationCreateInput[] {
  return [
    { STRING_FIELD: "John Doe", ARRAY_FIELD2: ["x"], ARRAY_FIELD: ["a"], createdBy: "admin", tags: ["Customer", "VIP"] },
    { STRING_FIELD: "Jane Smith", ARRAY_FIELD2: ["y"], ARRAY_FIELD: ["b"], createdBy: "admin", tags: ["Lead", "Potential"] },
    { STRING_FIELD: "Bob Johnson", ARRAY_FIELD2: ["z"], ARRAY_FIELD: ["c"], createdBy: "admin", tags: ["Customer"] },
    { STRING_FIELD: "Alice Brown", ARRAY_FIELD2: ["a"], ARRAY_FIELD: ["d"], createdBy: "admin", tags: ["VIP"] }
  ];
}

// Update data creators
export function createInvitationUpdate(variant: number): InvitationUpdateInput {
  const baseUpdates: Record<number, InvitationUpdateInput> = {
    1: {
      STRING_FIELD: "Updated Invitation",
      STRING_FIELD2: "Updated STRING_FIELD2",
      ARRAY_FIELD2: ["updated_condition"],
      ARRAY_FIELD: ["updated_action"],
      tags: ["VIP", "Premium"],
      isActive: false,
      updatedBy: "admin"
    },
    2: {
      STRING_FIELD: "New Name",
      updatedBy: "admin"
    },
    3: {
      STRING_FIELD2: "Updated STRING_FIELD2 only",
      updatedBy: "admin"
    },
    4: {
      tags: ["new-tag", "updated-tag"],
      updatedBy: "admin"
    },
    5: {
      isActive: false,
      updatedBy: "admin"
    }
  };

  if (!baseUpdates[variant]) {
    throw new Error(`Invitation update variant ${variant} not found. Available variants: ${Object.keys(baseUpdates).join(', ')}`);
  }

  return { ...baseUpdates[variant] };
}

// Specialized update creators
export function createFullInvitationUpdate(): InvitationUpdateInput {
  return createInvitationUpdate(1);
}

export function createNameOnlyUpdate(): InvitationUpdateInput {
  return createInvitationUpdate(2);
}

export function createDescriptionOnlyUpdate(): InvitationUpdateInput {
  return createInvitationUpdate(3);
}

export function createTagsOnlyUpdate(): InvitationUpdateInput {
  return createInvitationUpdate(4);
}

export function createStatusOnlyUpdate(): InvitationUpdateInput {
  return createInvitationUpdate(5);
}

// Invalid update data creators for validation tests
export function createInvalidUpdate(type: 'empty-STRING_FIELD' | 'empty-ARRAY_FIELD2' | 'empty-ARRAY_FIELD' | 'empty-object'): any {
  const invalidUpdates = {
    'empty-STRING_FIELD': {
      STRING_FIELD: "",
      updatedBy: "admin"
    },
    'empty-ARRAY_FIELD2': {
      ARRAY_FIELD2: [],
      updatedBy: "admin"
    },
    'empty-ARRAY_FIELD': {
      ARRAY_FIELD: [],
      updatedBy: "admin"
    },
    'empty-object': {}
  };

  return invalidUpdates[type];
}

// Update with whitespace for trimming tests
export function createUpdateWithWhitespace(): InvitationUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    updatedBy: "admin"
  };
}

// Update for duplicate STRING_FIELD testing
export function createDuplicateNameUpdate(existingName: string): InvitationUpdateInput {
  return {
    STRING_FIELD: existingName,
    updatedBy: "admin"
  };
}

// Update with same STRING_FIELD (no change scenario)
export function createSameNameUpdate(): InvitationUpdateInput {
  return {
    STRING_FIELD: "Simple Invitation", // Same as createMinimalInvitation
    STRING_FIELD2: "Updated STRING_FIELD2",
    updatedBy: "admin"
  };
}

// Invitation for soft delete testing
export function createInvitationForSoftDelete(): InvitationCreateInput {
  return {
    STRING_FIELD: "To Be Deleted",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin"
  };
}

// Update for soft deleted invitation testing
export function createUpdateForSoftDeleted(): InvitationUpdateInput {
  return {
    STRING_FIELD: "Should Not Work",
    updatedBy: "admin"
  };
}

// Update with whitespace in all fields for comprehensive trimming test
export function createUpdateWithAllFieldsWhitespace(): InvitationUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    STRING_FIELD2: "   Trimmed Description   ",
    ARRAY_FIELD2: ["   trimmed_condition   "],
    ARRAY_FIELD: ["   trimmed_action   "],
    tags: ["   tag1   ", "   tag2   "],
    updatedBy: "admin"
  };
}

// Invitation for trimming test
export function createInvitationForTrimming(): InvitationCreateInput {
  return {
    STRING_FIELD: "Original Invitation",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin"
  };
}

// Invitation for active status testing
export function createActiveInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Active Invitation",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    isActive: true,
    createdBy: "admin"
  };
}

// Update for status change testing
export function createStatusChangeUpdate(): InvitationUpdateInput {
  return {
    isActive: false,
    updatedBy: "admin"
  };
}

// ========================================
// PARAMS CREATORS FOR implHandleGetAllInvitations
// ========================================

// Search params
export function createSearchByNameParams() {
  return { search: "John" };
}

export function createSearchByDescriptionParams() {
  return { search: "processing" };
}

export function createEmptySearchParams() {
  return { search: "" };
}

export function createWhitespaceSearchParams() {
  return { search: "   " };
}

export function createNonExistentSearchParams() {
  return { search: "NonExistent" };
}

// Filter params
export function createVipTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "VIP" }]
  };
}

export function createCustomerTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "Customer" }]
  };
}

// Pagination params
export function createPaginationParams() {
  return {
    page: 1,
    limit: 2
  };
}

// Sorting params
export function createSortByNameAscParams() {
  return {
    sorts: [{ field: "STRING_FIELD", direction: "asc" as const }]
  };
}

// Combined params
export function createSearchAndTagParams() {
  return {
    search: "John",
    tag: "VIP"
  };
}

// Include deleted params
export function createIncludeDeletedParams() {
  return { includeDeleted: true };
}

// Legacy tag params (converted to filters format)
export function createEmptyTagParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceTagParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

export function createNonExistentTagParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

// Additional search params for read.test.ts
export function createSearchByTagParams() {
  return { search: "VIP" };
}

export function createUnmatchedSearchParams() {
  return { search: "nonexistent" };
}

export function createUndefinedSearchParams() {
  return { search: undefined };
}

// Additional filter params for read.test.ts
export function createNonExistentFilterParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

export function createEmptyFilterFieldParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceFilterFieldParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

// ========================================
// CREATORS FOR DELETE TESTS
// ========================================

// Invitation for retry delete testing
export function createRetryDeleteInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Retry Delete",
    ARRAY_FIELD2: ["attempt"],
    ARRAY_FIELD: ["log"],
    createdBy: "admin"
  };
}

// ========================================
// CREATORS FOR SPECIAL CASES (Invitation-specific)
// ========================================

// Invitation with special characters and unicode
export function createSpecialCharacterInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "José María O'Connor",
    STRING_FIELD2: "Handles unicode 🎉 & symbols",
    ARRAY_FIELD2: ["STRING_FIELD.includes('José')"],
    ARRAY_FIELD: ["notify", "log"],
    tags: ["Special", "🚀", "Test@Tag"],
    createdBy: "admin"
  };
}

// Invitation with very long ARRAY_FIELD2 (Invitation-specific test)
export function createLongContentInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Very Long Invitation Name That Exceeds Normal Length Expectations And Tests System Limits",
    STRING_FIELD2: "This is a very long STRING_FIELD2 that tests how the system handles extensive text ARRAY_FIELD2 in invitation STRING_FIELD2s. It includes multiple sentences and should test the limits of what the system can handle in terms of ARRAY_FIELD2 length and processing.",
    ARRAY_FIELD2: [
      "invitation.message.length > 1000",
      "invitation.message.includes('very long query with lots of details')",
      "invitation.session.duration > 3600"
    ],
    ARRAY_FIELD: [
      "log_extensive_details",
      "notify_admin_of_long_interaction",
      "create_detailed_report",
      "escalate_to_specialist"
    ],
    tags: ["LongContent", "EdgeCase", "SystemLimits", "Performance"],
    createdBy: "admin"
  };
}

// Invitation with edge case ARRAY_FIELD2 (Invitation-specific)
export function createEdgeCaseConditionsInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Edge Case Conditions",
    STRING_FIELD2: "Tests complex condition parsing",
    ARRAY_FIELD2: [
      "invitation.age >= 18 && invitation.age <= 65",
      "invitation.location.country === 'US' || invitation.location.country === 'CA'",
      "invitation.preferences.notifications === true"
    ],
    ARRAY_FIELD: [
      "apply_regional_invitations",
      "send_age_appropriate_ARRAY_FIELD2"
    ],
    tags: ["EdgeCase", "Complex"],
    createdBy: "admin"
  };
}

// Invitation with complex ARRAY_FIELD (Invitation-specific)
export function createComplexActionsInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Complex Actions Invitation",
    STRING_FIELD2: "Tests complex action execution",
    ARRAY_FIELD2: ["trigger_complex_workflow"],
    ARRAY_FIELD: [
      "webhook.call('https://api.example.com/notify')",
      "database.update('invitation_stats', {last_interaction: now()})",
      "ARRAY_FIELD.send(template='complex_notification', to=invitation.ARRAY_FIELD)",
      "analytics.track('complex_invitation_triggered', {invitation_id: this.id})"
    ],
    tags: ["Complex", "Integration"],
    createdBy: "admin"
  };
}

// Invitation with empty optional fields (Invitation-specific edge case)
export function createEmptyOptionalFieldsInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Empty Optional Fields",
    ARRAY_FIELD2: ["basic_condition"],
    ARRAY_FIELD: ["basic_action"],
    STRING_FIELD2: "",
    tags: [],
    createdBy: "admin"
  };
}

// Invitation for testing AI-specific business logic
export function createAiLogicInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "AI Decision Invitation",
    STRING_FIELD2: "Tests AI-specific decision making logic",
    ARRAY_FIELD2: [
      "ai.confidence > 0.8",
      "ai.model === 'gpt-4'",
      "ai.context.length > 100"
    ],
    ARRAY_FIELD: [
      "ai.respond_with_confidence",
      "ai.log_decision_path",
      "ai.update_learning_model"
    ],
    tags: ["AI", "MachineLearning", "Confidence"],
    createdBy: "admin"
  };
}

// Creators for delete test scenarios
export function createComplexInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Complex Invitation",
    STRING_FIELD2: "Full field test",
    ARRAY_FIELD2: ["invitation.role == 'admin'"],
    ARRAY_FIELD: ["grant_access", "log_activity"],
    tags: ["admin", "security"],
    isActive: true,
    createdBy: "admin"
  };
}

export function createMinimalDeleteInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Minimal Invitation",
    ARRAY_FIELD2: ["is.loggedIn"],
    ARRAY_FIELD: ["alert"],
    createdBy: "admin"
  };
}

// Invitations for testing deletion effects on other invitations
export function createInvitationsForDeletionTest(): InvitationCreateInput[] {
  return [
    { STRING_FIELD: "Keep This One", ARRAY_FIELD2: ["x"], ARRAY_FIELD: ["a"], createdBy: "admin" },
    { STRING_FIELD: "Delete This One", ARRAY_FIELD2: ["y"], ARRAY_FIELD: ["b"], createdBy: "admin" },
    { STRING_FIELD: "Keep This Too", ARRAY_FIELD2: ["z"], ARRAY_FIELD: ["c"], createdBy: "admin" }
  ];
}

// Creators for bulk operations testing
export function createExistingInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Existing Invitation",
    STRING_FIELD2: "An existing invitation",
    ARRAY_FIELD2: ["Invitation says test"],
    ARRAY_FIELD: ["Show test response"],
    createdBy: "admin"
  };
}

export function createDuplicateInvitationsForBulk(): InvitationCreateInput[] {
  return [
    {
      STRING_FIELD: "Existing Invitation", // Duplicate STRING_FIELD
      STRING_FIELD2: "Another invitation with same STRING_FIELD",
      ARRAY_FIELD2: ["Invitation says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "New Invitation",
      STRING_FIELD2: "A new invitation",
      ARRAY_FIELD2: ["Invitation says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// Invitations for bulk update testing
export function createInvitationsForBulkUpdate(): InvitationCreateInput[] {
  return [
    {
      STRING_FIELD: "Invitation 1",
      STRING_FIELD2: "First invitation",
      ARRAY_FIELD2: ["Invitation says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "Invitation 2",
      STRING_FIELD2: "Second invitation",
      ARRAY_FIELD2: ["Invitation says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// Bulk update data
export function createBulkUpdateData(): any[] {
  return [
    {
      STRING_FIELD: "Updated Invitation 1",
      STRING_FIELD2: "Updated first invitation",
      updatedBy: "admin"
    },
    {
      STRING_FIELD: "Updated Invitation 2",
      STRING_FIELD2: "Updated second invitation",
      updatedBy: "admin"
    }
  ];
}

// Invitations for bulk delete testing
export function createInvitationsForBulkDelete(): InvitationCreateInput[] {
  return [
    {
      STRING_FIELD: "Invitation 1",
      STRING_FIELD2: "First invitation",
      ARRAY_FIELD2: ["Invitation says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "Invitation 2",
      STRING_FIELD2: "Second invitation",
      ARRAY_FIELD2: ["Invitation says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "Invitation 3",
      STRING_FIELD2: "Third invitation",
      ARRAY_FIELD2: ["Invitation asks question"],
      ARRAY_FIELD: ["Show help"],
      createdBy: "admin"
    }
  ];
}

// Invalid data creators for validation tests
export function createInvalidInvitation(type: 'missing-STRING_FIELD' | 'missing-ARRAY_FIELD2' | 'missing-ARRAY_FIELD' | 'empty-ARRAY_FIELD2' | 'empty-ARRAY_FIELD' | 'missing-ARRAY_FIELD2'): any {
  const invalidInvitations = {
    'missing-ARRAY_FIELD2': {
      STRING_FIELD: "John Doe"
    },
    'missing-STRING_FIELD': {
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'missing-ARRAY_FIELD2': {
      STRING_FIELD: "Invalid Invitation",
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'missing-ARRAY_FIELD': {
      STRING_FIELD: "Invalid Invitation",
      ARRAY_FIELD2: ["test_condition"],
      createdBy: "admin"
    },
    'empty-ARRAY_FIELD2': {
      STRING_FIELD: "Invalid Invitation",
      ARRAY_FIELD2: [],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'empty-ARRAY_FIELD': {
      STRING_FIELD: "Invalid Invitation",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: [],
      createdBy: "admin"
    }
  };

  return invalidInvitations[type];
}

// Creator for invitations with special characteristics
export function createInvitationWithWhitespace(): InvitationCreateInput {
  return {
    STRING_FIELD: "  Trimmed Invitation  ",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin"
  };
}

export function createInvitationWithManyTags(): InvitationCreateInput {
  return {
    STRING_FIELD: "Multi-tag Invitation",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    createdBy: "admin"
  };
}

export function createInvitationWithoutDescription(): InvitationCreateInput {
  return {
    STRING_FIELD: "Invitation without STRING_FIELD2",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin"
  };
}

export function createInvitationWithEmptyTags(): InvitationCreateInput {
  return {
    STRING_FIELD: "Invitation with empty tags",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: [],
    createdBy: "admin"
  };
}

// Duplicate invitation creator for conflict testing
export function createDuplicateInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Duplicate Invitation",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin"
  };
}

export function createSecondDuplicateInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Duplicate Invitation", // Same STRING_FIELD as above
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin"
  };
}

// Test invitation with specific STRING_FIELD for soft delete tests
export function createTestInvitation(): InvitationCreateInput {
  return {
    STRING_FIELD: "Test Invitation",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin"
  };
}

export function createTestInvitation2(): InvitationCreateInput {
  return {
    STRING_FIELD: "Test Invitation",
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin"
  };
}
