// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { RoleBusinessLogicInterface } from "@/lib/repositories/roles/interface";
import { RoleBusinessLogic } from "@/lib/repositories/roles/BusinessLogic";
import { MongoRoleRepository } from "@/lib/repositories/roles/MongoRepository";
import { TestRoleDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateRole, implHandleGetRole, implHandleGetAllRoles } from "@/app/api/v1/roles/impl";
import {
  createRole,
  createSimpleRoles,
  createRolesWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams
} from "./object_creator";

describe("Read Role API Tests", () => {
  let businessLogic: RoleBusinessLogicInterface;
  let dbRepository: TestRoleDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Role");
    await driver.connect();
    const originalDb = new MongoRoleRepository(driver);
    dbRepository = new TestRoleDBRepositoryWrapper(originalDb, driver);
    businessLogic = new RoleBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("GET /api/v1/roles/:id", () => {
    it("should successfully get role by ID", async () => {
      const role = createRole(5); // John Doe Role

      const createResult = await implHandleCreateRole(role, businessLogic);
      const id = createResult.body.data.id;

      const result = await implHandleGetRole(id, businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.id).toBe(id);
      expect(result.body.data?.STRING_FIELD).toBe(role.STRING_FIELD);
    });

    it("should fail to get non-existent role", async () => {
      const result = await implHandleGetRole("507f1f77bcf86cd799439011", businessLogic);
      expect(result.status).toBe(404);
    });

    it("should fail with empty role ID", async () => {
      const result = await implHandleGetRole("", businessLogic);
      expect(result.status).toBe(400);
    });

    it("should fail with whitespace-only role ID", async () => {
      const result = await implHandleGetRole("   ", businessLogic);
      expect(result.status).toBe(400);
    });
  });

  describe("GET /api/v1/roles", () => {
    it("should get all roles", async () => {
      const roles = createSimpleRoles();
      for (const r of roles) await implHandleCreateRole(r, businessLogic);

      const result = await implHandleGetAllRoles(businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(3);
    });

    it("should return empty when no roles exist", async () => {
      const result = await implHandleGetAllRoles(businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });
  });

  describe("GET /api/v1/roles/search", () => {
    beforeEach(async () => {
      const data = createRolesWithTags();
      for (const r of data) await implHandleCreateRole(r, businessLogic);
    });

    it("should search by STRING_FIELD", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should search by tag", async () => {
      const params = createSearchByTagParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(4);
    });
  });

  describe("GET /api/v1/roles/filters", () => {
    beforeEach(async () => {
      const data = createRolesWithTags();
      for (const r of data) await implHandleCreateRole(r, businessLogic);
    });

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams();
      const result = await implHandleGetAllRoles(businessLogic, params);
      expect(result.status).toBe(400);
    });
  });
});
