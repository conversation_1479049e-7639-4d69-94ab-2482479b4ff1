import { RoleCreateInput, RoleUpdateInput } from "@/lib/repositories/roles/interface";

/**
 * Factory functions for creating test Role objects
 * This allows for consistent test data across all test files
 * and easy modification of test objects in one place
 */

// Base creator functions for different scenarios
export function createRole(variant: number): RoleCreateInput {
  const baseRoles: Record<number, RoleCreateInput> = {
    1: {
      STRING_FIELD: "Customer Support Role",
      STRING_FIELD2: "Role for handling customer support requests",
      ARRAY_FIELD2: ["role_message_contains('help')", "time_between('09:00', '17:00')"],
      ARRAY_FIELD: ["assign_to_support", "send_acknowledgment"],
      tags: ["Customer", "VIP"],
      isActive: true,
      createdBy: "admin"
    },
    2: {
      STRING_FIELD: "Simple Role",
      ARRAY_FIELD2: ["always_true"],
      ARRAY_FIELD: ["log_message"],
      createdBy: "admin"
    },
    3: {
      STRING_FIELD: "Test Role",
      STRING_FIELD2: "A test role with STRING_FIELD2",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    4: {
      STRING_FIELD: "Tagged Role",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      tags: ["urgent", "customer-service"],
      createdBy: "admin"
    },
    5: {
      STRING_FIELD: "John Doe Role",
      STRING_FIELD2: "Role for John Doe processing",
      ARRAY_FIELD2: ["role_STRING_FIELD_contains('john')", "time_between('09:00', '17:00')"],
      ARRAY_FIELD: ["assign_to_support", "send_ARRAY_FIELD"],
      tags: ["Customer", "VIP"],
      createdBy: "admin"
    },
    6: {
      STRING_FIELD: "Jane Smith Role",
      STRING_FIELD2: "Role for Jane Smith processing",
      ARRAY_FIELD2: ["role_STRING_FIELD_contains('jane')", "priority_high"],
      ARRAY_FIELD: ["escalate", "notify_manager"],
      tags: ["Customer"],
      createdBy: "admin"
    },
    7: {
      STRING_FIELD: "Bob Johnson Role",
      STRING_FIELD2: "Role for Bob Johnson processing",
      ARRAY_FIELD2: ["role_STRING_FIELD_contains('bob')", "vip_customer"],
      ARRAY_FIELD: ["priority_handling", "send_notification"],
      tags: ["VIP", "Premium"],
      createdBy: "admin"
    },
    8: {
      STRING_FIELD: "Alice Brown Role",
      STRING_FIELD2: "Role for Alice Brown processing",
      ARRAY_FIELD2: ["role_STRING_FIELD_contains('alice')", "lead_qualification"],
      ARRAY_FIELD: ["assign_to_sales", "track_conversion"],
      tags: ["Premium"],
      createdBy: "admin"
    }
  };

  if (!baseRoles[variant]) {
    throw new Error(`Role variant ${variant} not found. Available variants: ${Object.keys(baseRoles).join(', ')}`);
  }

  return { ...baseRoles[variant] };
}

// Specialized creator functions for specific test scenarios
export function createMinimalRole(): RoleCreateInput {
  return createRole(2);
}

export function createFullRole(): RoleCreateInput {
  return createRole(1);
}

export function createRoleWithDescription(): RoleCreateInput {
  return createRole(3);
}

export function createRoleWithTags(): RoleCreateInput {
  return createRole(4);
}

// Creator for multiple roles (useful for bulk operations and search tests)
export function createMultipleRoles(): RoleCreateInput[] {
  return [
    createRole(5), // John Doe Role
    createRole(6), // Jane Smith Role
    createRole(7), // Bob Johnson Role
    createRole(8)  // Alice Brown Role
  ];
}

// Creator for simple test roles (useful for basic CRUD operations)
export function createSimpleRoles(): RoleCreateInput[] {
  return [
    { STRING_FIELD: "A", ARRAY_FIELD2: ["1"], ARRAY_FIELD: ["a"], createdBy: "admin" },
    { STRING_FIELD: "B", ARRAY_FIELD2: ["2"], ARRAY_FIELD: ["b"], createdBy: "admin" },
    { STRING_FIELD: "C", ARRAY_FIELD2: ["3"], ARRAY_FIELD: ["c"], createdBy: "admin" }
  ];
}

// Creator for roles with specific tags (useful for filtering tests)
export function createRolesWithTags(): RoleCreateInput[] {
  return [
    { STRING_FIELD: "John Doe", ARRAY_FIELD2: ["x"], ARRAY_FIELD: ["a"], createdBy: "admin", tags: ["Customer", "VIP"] },
    { STRING_FIELD: "Jane Smith", ARRAY_FIELD2: ["y"], ARRAY_FIELD: ["b"], createdBy: "admin", tags: ["Lead", "Potential"] },
    { STRING_FIELD: "Bob Johnson", ARRAY_FIELD2: ["z"], ARRAY_FIELD: ["c"], createdBy: "admin", tags: ["Customer"] },
    { STRING_FIELD: "Alice Brown", ARRAY_FIELD2: ["a"], ARRAY_FIELD: ["d"], createdBy: "admin", tags: ["VIP"] }
  ];
}

// Update data creators
export function createRoleUpdate(variant: number): RoleUpdateInput {
  const baseUpdates: Record<number, RoleUpdateInput> = {
    1: {
      STRING_FIELD: "Updated Role",
      STRING_FIELD2: "Updated STRING_FIELD2",
      ARRAY_FIELD2: ["updated_condition"],
      ARRAY_FIELD: ["updated_action"],
      tags: ["VIP", "Premium"],
      isActive: false,
      updatedBy: "admin"
    },
    2: {
      STRING_FIELD: "New Name",
      updatedBy: "admin"
    },
    3: {
      STRING_FIELD2: "Updated STRING_FIELD2 only",
      updatedBy: "admin"
    },
    4: {
      tags: ["new-tag", "updated-tag"],
      updatedBy: "admin"
    },
    5: {
      isActive: false,
      updatedBy: "admin"
    }
  };

  if (!baseUpdates[variant]) {
    throw new Error(`Role update variant ${variant} not found. Available variants: ${Object.keys(baseUpdates).join(', ')}`);
  }

  return { ...baseUpdates[variant] };
}

// Specialized update creators
export function createFullRoleUpdate(): RoleUpdateInput {
  return createRoleUpdate(1);
}

export function createNameOnlyUpdate(): RoleUpdateInput {
  return createRoleUpdate(2);
}

export function createDescriptionOnlyUpdate(): RoleUpdateInput {
  return createRoleUpdate(3);
}

export function createTagsOnlyUpdate(): RoleUpdateInput {
  return createRoleUpdate(4);
}

export function createStatusOnlyUpdate(): RoleUpdateInput {
  return createRoleUpdate(5);
}

// Invalid update data creators for validation tests
export function createInvalidUpdate(type: 'empty-STRING_FIELD' | 'empty-ARRAY_FIELD2' | 'empty-ARRAY_FIELD' | 'empty-object'): any {
  const invalidUpdates = {
    'empty-STRING_FIELD': {
      STRING_FIELD: "",
      updatedBy: "admin"
    },
    'empty-ARRAY_FIELD2': {
      ARRAY_FIELD2: [],
      updatedBy: "admin"
    },
    'empty-ARRAY_FIELD': {
      ARRAY_FIELD: [],
      updatedBy: "admin"
    },
    'empty-object': {}
  };

  return invalidUpdates[type];
}

// Update with whitespace for trimming tests
export function createUpdateWithWhitespace(): RoleUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    updatedBy: "admin"
  };
}

// Update for duplicate STRING_FIELD testing
export function createDuplicateNameUpdate(existingName: string): RoleUpdateInput {
  return {
    STRING_FIELD: existingName,
    updatedBy: "admin"
  };
}

// Update with same STRING_FIELD (no change scenario)
export function createSameNameUpdate(): RoleUpdateInput {
  return {
    STRING_FIELD: "Simple Role", // Same as createMinimalRole
    STRING_FIELD2: "Updated STRING_FIELD2",
    updatedBy: "admin"
  };
}

// Role for soft delete testing
export function createRoleForSoftDelete(): RoleCreateInput {
  return {
    STRING_FIELD: "To Be Deleted",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin"
  };
}

// Update for soft deleted role testing
export function createUpdateForSoftDeleted(): RoleUpdateInput {
  return {
    STRING_FIELD: "Should Not Work",
    updatedBy: "admin"
  };
}

// Update with whitespace in all fields for comprehensive trimming test
export function createUpdateWithAllFieldsWhitespace(): RoleUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    STRING_FIELD2: "   Trimmed Description   ",
    ARRAY_FIELD2: ["   trimmed_condition   "],
    ARRAY_FIELD: ["   trimmed_action   "],
    tags: ["   tag1   ", "   tag2   "],
    updatedBy: "admin"
  };
}

// Role for trimming test
export function createRoleForTrimming(): RoleCreateInput {
  return {
    STRING_FIELD: "Original Role",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin"
  };
}

// Role for active status testing
export function createActiveRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Active Role",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    isActive: true,
    createdBy: "admin"
  };
}

// Update for status change testing
export function createStatusChangeUpdate(): RoleUpdateInput {
  return {
    isActive: false,
    updatedBy: "admin"
  };
}

// ========================================
// PARAMS CREATORS FOR implHandleGetAllRoles
// ========================================

// Search params
export function createSearchByNameParams() {
  return { search: "John" };
}

export function createSearchByDescriptionParams() {
  return { search: "processing" };
}

export function createEmptySearchParams() {
  return { search: "" };
}

export function createWhitespaceSearchParams() {
  return { search: "   " };
}

export function createNonExistentSearchParams() {
  return { search: "NonExistent" };
}

// Filter params
export function createVipTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "VIP" }]
  };
}

export function createCustomerTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "Customer" }]
  };
}

// Pagination params
export function createPaginationParams() {
  return {
    page: 1,
    limit: 2
  };
}

// Sorting params
export function createSortByNameAscParams() {
  return {
    sorts: [{ field: "STRING_FIELD", direction: "asc" as const }]
  };
}

// Combined params
export function createSearchAndTagParams() {
  return {
    search: "John",
    tag: "VIP"
  };
}

// Include deleted params
export function createIncludeDeletedParams() {
  return { includeDeleted: true };
}

// Legacy tag params (converted to filters format)
export function createEmptyTagParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceTagParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

export function createNonExistentTagParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

// Additional search params for read.test.ts
export function createSearchByTagParams() {
  return { search: "VIP" };
}

export function createUnmatchedSearchParams() {
  return { search: "nonexistent" };
}

export function createUndefinedSearchParams() {
  return { search: undefined };
}

// Additional filter params for read.test.ts
export function createNonExistentFilterParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

export function createEmptyFilterFieldParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceFilterFieldParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

// ========================================
// CREATORS FOR DELETE TESTS
// ========================================

// Role for retry delete testing
export function createRetryDeleteRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Retry Delete",
    ARRAY_FIELD2: ["attempt"],
    ARRAY_FIELD: ["log"],
    createdBy: "admin"
  };
}

// ========================================
// CREATORS FOR SPECIAL CASES (Role-specific)
// ========================================

// Role with special characters and unicode
export function createSpecialCharacterRole(): RoleCreateInput {
  return {
    STRING_FIELD: "José María O'Connor",
    STRING_FIELD2: "Handles unicode 🎉 & symbols",
    ARRAY_FIELD2: ["STRING_FIELD.includes('José')"],
    ARRAY_FIELD: ["notify", "log"],
    tags: ["Special", "🚀", "Test@Tag"],
    createdBy: "admin"
  };
}

// Role with very long ARRAY_FIELD2 (Role-specific test)
export function createLongContentRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Very Long Role Name That Exceeds Normal Length Expectations And Tests System Limits",
    STRING_FIELD2: "This is a very long STRING_FIELD2 that tests how the system handles extensive text ARRAY_FIELD2 in role STRING_FIELD2s. It includes multiple sentences and should test the limits of what the system can handle in terms of ARRAY_FIELD2 length and processing.",
    ARRAY_FIELD2: [
      "role.message.length > 1000",
      "role.message.includes('very long query with lots of details')",
      "role.session.duration > 3600"
    ],
    ARRAY_FIELD: [
      "log_extensive_details",
      "notify_admin_of_long_interaction",
      "create_detailed_report",
      "escalate_to_specialist"
    ],
    tags: ["LongContent", "EdgeCase", "SystemLimits", "Performance"],
    createdBy: "admin"
  };
}

// Role with edge case ARRAY_FIELD2 (Role-specific)
export function createEdgeCaseConditionsRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Edge Case Conditions",
    STRING_FIELD2: "Tests complex condition parsing",
    ARRAY_FIELD2: [
      "role.age >= 18 && role.age <= 65",
      "role.location.country === 'US' || role.location.country === 'CA'",
      "role.preferences.notifications === true"
    ],
    ARRAY_FIELD: [
      "apply_regional_roles",
      "send_age_appropriate_ARRAY_FIELD2"
    ],
    tags: ["EdgeCase", "Complex"],
    createdBy: "admin"
  };
}

// Role with complex ARRAY_FIELD (Role-specific)
export function createComplexActionsRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Complex Actions Role",
    STRING_FIELD2: "Tests complex action execution",
    ARRAY_FIELD2: ["trigger_complex_workflow"],
    ARRAY_FIELD: [
      "webhook.call('https://api.example.com/notify')",
      "database.update('role_stats', {last_interaction: now()})",
      "ARRAY_FIELD.send(template='complex_notification', to=role.ARRAY_FIELD)",
      "analytics.track('complex_role_triggered', {role_id: this.id})"
    ],
    tags: ["Complex", "Integration"],
    createdBy: "admin"
  };
}

// Role with empty optional fields (Role-specific edge case)
export function createEmptyOptionalFieldsRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Empty Optional Fields",
    ARRAY_FIELD2: ["basic_condition"],
    ARRAY_FIELD: ["basic_action"],
    STRING_FIELD2: "",
    tags: [],
    createdBy: "admin"
  };
}

// Role for testing AI-specific business logic
export function createAiLogicRole(): RoleCreateInput {
  return {
    STRING_FIELD: "AI Decision Role",
    STRING_FIELD2: "Tests AI-specific decision making logic",
    ARRAY_FIELD2: [
      "ai.confidence > 0.8",
      "ai.model === 'gpt-4'",
      "ai.context.length > 100"
    ],
    ARRAY_FIELD: [
      "ai.respond_with_confidence",
      "ai.log_decision_path",
      "ai.update_learning_model"
    ],
    tags: ["AI", "MachineLearning", "Confidence"],
    createdBy: "admin"
  };
}

// Creators for delete test scenarios
export function createComplexRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Complex Role",
    STRING_FIELD2: "Full field test",
    ARRAY_FIELD2: ["role.role == 'admin'"],
    ARRAY_FIELD: ["grant_access", "log_activity"],
    tags: ["admin", "security"],
    isActive: true,
    createdBy: "admin"
  };
}

export function createMinimalDeleteRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Minimal Role",
    ARRAY_FIELD2: ["is.loggedIn"],
    ARRAY_FIELD: ["alert"],
    createdBy: "admin"
  };
}

// Roles for testing deletion effects on other roles
export function createRolesForDeletionTest(): RoleCreateInput[] {
  return [
    { STRING_FIELD: "Keep This One", ARRAY_FIELD2: ["x"], ARRAY_FIELD: ["a"], createdBy: "admin" },
    { STRING_FIELD: "Delete This One", ARRAY_FIELD2: ["y"], ARRAY_FIELD: ["b"], createdBy: "admin" },
    { STRING_FIELD: "Keep This Too", ARRAY_FIELD2: ["z"], ARRAY_FIELD: ["c"], createdBy: "admin" }
  ];
}

// Creators for bulk operations testing
export function createExistingRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Existing Role",
    STRING_FIELD2: "An existing role",
    ARRAY_FIELD2: ["Role says test"],
    ARRAY_FIELD: ["Show test response"],
    createdBy: "admin"
  };
}

export function createDuplicateRolesForBulk(): RoleCreateInput[] {
  return [
    {
      STRING_FIELD: "Existing Role", // Duplicate STRING_FIELD
      STRING_FIELD2: "Another role with same STRING_FIELD",
      ARRAY_FIELD2: ["Role says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "New Role",
      STRING_FIELD2: "A new role",
      ARRAY_FIELD2: ["Role says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// Roles for bulk update testing
export function createRolesForBulkUpdate(): RoleCreateInput[] {
  return [
    {
      STRING_FIELD: "Role 1",
      STRING_FIELD2: "First role",
      ARRAY_FIELD2: ["Role says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "Role 2",
      STRING_FIELD2: "Second role",
      ARRAY_FIELD2: ["Role says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// Bulk update data
export function createBulkUpdateData(): any[] {
  return [
    {
      STRING_FIELD: "Updated Role 1",
      STRING_FIELD2: "Updated first role",
      updatedBy: "admin"
    },
    {
      STRING_FIELD: "Updated Role 2",
      STRING_FIELD2: "Updated second role",
      updatedBy: "admin"
    }
  ];
}

// Roles for bulk delete testing
export function createRolesForBulkDelete(): RoleCreateInput[] {
  return [
    {
      STRING_FIELD: "Role 1",
      STRING_FIELD2: "First role",
      ARRAY_FIELD2: ["Role says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "Role 2",
      STRING_FIELD2: "Second role",
      ARRAY_FIELD2: ["Role says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "Role 3",
      STRING_FIELD2: "Third role",
      ARRAY_FIELD2: ["Role asks question"],
      ARRAY_FIELD: ["Show help"],
      createdBy: "admin"
    }
  ];
}

// Invalid data creators for validation tests
export function createInvalidRole(type: 'missing-STRING_FIELD' | 'missing-ARRAY_FIELD2' | 'missing-ARRAY_FIELD' | 'empty-ARRAY_FIELD2' | 'empty-ARRAY_FIELD' | 'missing-ARRAY_FIELD2'): any {
  const invalidRoles = {
    'missing-ARRAY_FIELD2': {
      STRING_FIELD: "John Doe"
    },
    'missing-STRING_FIELD': {
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'missing-ARRAY_FIELD2': {
      STRING_FIELD: "Invalid Role",
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'missing-ARRAY_FIELD': {
      STRING_FIELD: "Invalid Role",
      ARRAY_FIELD2: ["test_condition"],
      createdBy: "admin"
    },
    'empty-ARRAY_FIELD2': {
      STRING_FIELD: "Invalid Role",
      ARRAY_FIELD2: [],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'empty-ARRAY_FIELD': {
      STRING_FIELD: "Invalid Role",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: [],
      createdBy: "admin"
    }
  };

  return invalidRoles[type];
}

// Creator for roles with special characteristics
export function createRoleWithWhitespace(): RoleCreateInput {
  return {
    STRING_FIELD: "  Trimmed Role  ",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin"
  };
}

export function createRoleWithManyTags(): RoleCreateInput {
  return {
    STRING_FIELD: "Multi-tag Role",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    createdBy: "admin"
  };
}

export function createRoleWithoutDescription(): RoleCreateInput {
  return {
    STRING_FIELD: "Role without STRING_FIELD2",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin"
  };
}

export function createRoleWithEmptyTags(): RoleCreateInput {
  return {
    STRING_FIELD: "Role with empty tags",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: [],
    createdBy: "admin"
  };
}

// Duplicate role creator for conflict testing
export function createDuplicateRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Duplicate Role",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin"
  };
}

export function createSecondDuplicateRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Duplicate Role", // Same STRING_FIELD as above
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin"
  };
}

// Test role with specific STRING_FIELD for soft delete tests
export function createTestRole(): RoleCreateInput {
  return {
    STRING_FIELD: "Test Role",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin"
  };
}

export function createTestRole2(): RoleCreateInput {
  return {
    STRING_FIELD: "Test Role",
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin"
  };
}
