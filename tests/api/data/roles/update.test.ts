//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { RoleBusinessLogicInterface } from "@/lib/repositories/roles/interface";
import { RoleBusinessLogic } from "@/lib/repositories/roles/BusinessLogic";
import { MongoRoleRepository } from "@/lib/repositories/roles/MongoRepository";
import { TestRoleDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateRole, implHandleUpdateRole, implHandleDeleteRole } from "@/app/api/v1/roles/impl";
import {
  createFullRole,
  createMinimalRole,
  createFullRoleUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createRoleForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createRoleForTrimming,
  createActiveRole,
  createStatusChangeUpdate
} from "./object_creator";

describe("Update Role API Tests", () => {
  let businessLogic: RoleBusinessLogicInterface;
  let dbRepository: TestRoleDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Role");
    await driver.connect();
    const originalDb = new MongoRoleRepository(driver);
    dbRepository = new TestRoleDBRepositoryWrapper(originalDb, driver);
    businessLogic = new RoleBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("PUT /api/v1/roles/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullRole();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      const updateData = createFullRoleUpdate();
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2);
      expect(result.body.data?.ARRAY_FIELD).toEqual(updateData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(updateData.variables);
      expect(result.body.data?.tags).toEqual(updateData.tags);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should update only the STRING_FIELD", async () => {
      const createData = createMinimalRole();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD).toEqual(createData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(createData.variables);
    });

    it("should trim STRING_FIELD when updating", async () => {
      const createData = createMinimalRole();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      const updateData = createUpdateWithWhitespace();
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name");
    });

    it("should fail to update non-existent role", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateRole("507f1f77bcf86cd799439011", updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Role not found");
    });

    it("should fail with invalid input: empty STRING_FIELD", async () => {
      const createData = createMinimalRole();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-STRING_FIELD');
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with invalid input: empty ARRAY_FIELD", async () => {
      const createData = createMinimalRole();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-ARRAY_FIELD');
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty update object", async () => {
      const createData = createMinimalRole();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-object');
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("No data provided for update");
    });

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateRole("", updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Role ID is required");
    });

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first role
      const createData1 = createMinimalRole();
      await implHandleCreateRole(createData1, businessLogic);

      // Create second role
      const createData2 = createFullRole();
      const createResult2 = await implHandleCreateRole(createData2, businessLogic);

      // Try to update second role with first role's STRING_FIELD
      const updateData = createDuplicateNameUpdate(createData1.STRING_FIELD);
      const result = await implHandleUpdateRole(createResult2.body.data.id, updateData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Another Role with this STRING_FIELD exists");
    });

    it("should allow updating role with same STRING_FIELD (no change)", async () => {
      const createData = createMinimalRole();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      const updateData = createSameNameUpdate();
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2);
    });

    it("should fail to update soft-deleted role", async () => {
      const createData = createRoleForSoftDelete();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      // Soft delete the role
      const deleteResult = await implHandleDeleteRole(rolesId, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Try to update the soft-deleted role
      const updateData = createUpdateForSoftDeleted();
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Role not found");
    });

    it("should trim all string fields when updating", async () => {
      const createData = createRoleForTrimming();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      const updateData = createUpdateWithAllFieldsWhitespace();
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name");
      expect(result.body.data?.ARRAY_FIELD2).toBe("Trimmed Description");
      expect(result.body.data?.ARRAY_FIELD).toEqual(["trimmed_condition"]);
      expect(result.body.data?.variables).toEqual(["trimmed_action"]);
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"]);
    });

    it("should fail with invalid input: empty variables", async () => {
      const createData = createMinimalRole();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-variables');
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should update isActive status", async () => {
      const createData = createActiveRole();
      const createResult = await implHandleCreateRole(createData, businessLogic);
      const rolesId = createResult.body.data.id;

      const updateData = createStatusChangeUpdate();
      const result = await implHandleUpdateRole(rolesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
    });
  });
});
