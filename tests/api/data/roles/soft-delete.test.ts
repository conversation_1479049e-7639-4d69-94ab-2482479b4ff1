//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { RoleBusinessLogicInterface } from "@/lib/repositories/roles/interface";
import { RoleBusinessLogic } from "@/lib/repositories/roles/BusinessLogic";
import { MongoRoleRepository } from "@/lib/repositories/roles/MongoRepository";
import { TestRoleDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateRole,
  implHandleGetRole,
  implHandleDeleteRole,
  implHandleUpdateRole,
  implHandleGetAllRoles,
  implHandleRestoreRole
} from "@/app/api/v1/roles/impl";
import { createRole, createRoleUpdate, createRoleWithDescription, createTestRole, createTestRole2 } from "./object_creator";

describe("Role Soft Delete Tests", () => {
  let businessLogic: RoleBusinessLogicInterface;
    let dbRepository: TestRoleDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("Role");
      await driver.connect()
      const originalDb = new MongoRoleRepository(driver);
      dbRepository = new TestRoleDBRepositoryWrapper(originalDb, driver);
      businessLogic = new RoleBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("Soft Delete", () => {
    it("should soft delete a roles by default", async () => {
      const roleData = createRoleWithDescription();
      const createResult = await implHandleCreateRole(roleData, businessLogic);
      expect(createResult.status).toBe(201);

      const deleteResult = await implHandleDeleteRole(createResult.body.data.id, businessLogic);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");

      // Role should not be accessible by default
      const getResult = await implHandleGetRole(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);

      // But should be accessible when including deleted
      const getDeletedResult = await implHandleGetRole(createResult.body.data.id, businessLogic, true);
      expect(getDeletedResult.status).toBe(200);
      expect(getDeletedResult.body.data).not.toBeNull();

      // Count should exclude soft deleted
      expect(await dbRepository.getRoleCount()).toBe(0);
      expect(await dbRepository.getRoleCount(true)).toBe(1);
    });

    it("should hard delete when specified", async () => {
      const roleData = createRoleWithDescription();
      const createResult = await implHandleCreateRole(roleData, businessLogic);
      expect(createResult.status).toBe(201);

      // Hard delete using impl function
      const deleteResult = await implHandleDeleteRole(createResult.body.data.id, businessLogic, true);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");

      // Role should not be accessible at all
      const getResult = await implHandleGetRole(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);
      const getDeletedResult = await implHandleGetRole(createResult.body.data.id, businessLogic, true);
      expect(getDeletedResult.status).toBe(404);

      // Count should be 0 in both cases
      expect(await dbRepository.getRoleCount()).toBe(0);
      expect(await dbRepository.getRoleCount(true)).toBe(0);
    });

    it("should not include soft deleted roles in getAll by default", async () => {
      const roleData1 = createRole(1);
      const roleData2 = createRole(2);

      const createResult1 = await implHandleCreateRole(roleData1, businessLogic);
      const createResult2 = await implHandleCreateRole(roleData2, businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      // Soft delete one roles
      const deleteResult = await implHandleDeleteRole(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const result = await implHandleGetAllRoles(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.items).toHaveLength(1);
      expect(result.body.data?.total).toBe(1);
      expect(result.body.data?.items[0].id).toBe(createResult2.body.data.id);
    });

    it("should include soft deleted roles when specified", async () => {
      const roleData1 = createRole(1);
      const roleData2 = createRole(2);

      const createResult1 = await implHandleCreateRole(roleData1, businessLogic);
      const createResult2 = await implHandleCreateRole(roleData2, businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      // Soft delete one roles
      const deleteResult = await implHandleDeleteRole(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const result = await implHandleGetAllRoles(businessLogic, { includeDeleted: true });

      expect(result.status).toBe(200);
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(2);

      const deletedRole = result.body.data?.items.find((c: any) => c.id === createResult1.body.data.id);
      expect(deletedRole).toBeDefined();
      expect(deletedRole?.deletedAt).toBeDefined();
    });

    it("should not allow updating soft deleted roles", async () => {
      const roleData = createRole(3);
      const createResult = await implHandleCreateRole(roleData, businessLogic);
      expect(createResult.status).toBe(201);

      const deleteResult = await implHandleDeleteRole(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const roleUpdate = createRoleUpdate(1)

      const result = await implHandleUpdateRole(createResult.body.data.id, roleUpdate, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should not include soft deleted roles in search", async () => {
      const roleData1 = createRole(3); // "Test Role"
      const createResult = await implHandleCreateRole(roleData1, businessLogic);
      expect(createResult.status).toBe(201);

      // Soft delete the role
      const deleteResult = await implHandleDeleteRole(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Search should not find the soft deleted role
      const searchResult = await implHandleGetAllRoles(businessLogic, { search: "Test" });
      expect(searchResult.status).toBe(200);
      expect(searchResult.body.data).toHaveLength(0);
    });
  });

  describe("Restore", () => {
    it("should restore a soft deleted roles", async () => {
      const roleData = createRoleWithDescription();
      const createResult = await implHandleCreateRole(roleData, businessLogic);
      expect(createResult.status).toBe(201);

      // Soft delete the roles
      const deleteResult = await implHandleDeleteRole(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const getResult = await implHandleGetRole(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);

      // Restore the roles
      const restoreResult = await implHandleRestoreRole(createResult.body.data.id, businessLogic);

      expect(restoreResult.status).toBe(200);
      expect(restoreResult.body.status).toBe("success");

      // Role should be accessible again
      const restoredResult = await implHandleGetRole(createResult.body.data.id, businessLogic);
      expect(restoredResult.status).toBe(200);
      expect(restoredResult.body.data?.deletedAt).toBeUndefined();

      // Count should include the restored roles
      expect(await dbRepository.getRoleCount()).toBe(1);
    });

    it("should fail to restore a non-existent roles", async () => {
      const restoreResult = await implHandleRestoreRole("507f1f77bcf86cd799439011", businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail to restore a roles that was never deleted", async () => {
      const roleData = createRole(3);
      const createResult = await implHandleCreateRole(roleData, businessLogic);
      expect(createResult.status).toBe(201);

      const restoreResult = await implHandleRestoreRole(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail to restore a hard deleted roles", async () => {
      const roleData = createRole(3);
      const createResult = await implHandleCreateRole(roleData, businessLogic);
      expect(createResult.status).toBe(201);

      // Hard delete the roles
      const deleteResult = await implHandleDeleteRole(createResult.body.data.id, businessLogic, true);
      expect(deleteResult.status).toBe(200);

      const restoreResult = await implHandleRestoreRole(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail with empty roles ID", async () => {
      const restoreResult = await implHandleRestoreRole("", businessLogic);
      expect(restoreResult.status).toBe(400);
      expect(restoreResult.body.status).toBe("failed");
      expect(restoreResult.body.error).toContain("Role ID is required");
    });

    it("should fail with whitespace-only roles ID", async () => {
      const restoreResult = await implHandleRestoreRole("   ", businessLogic);
      expect(restoreResult.status).toBe(400);
      expect(restoreResult.body.status).toBe("failed");
      expect(restoreResult.body.error).toContain("Role ID is required");
    });

    it("should update updatedAt when restoring", async () => {
      const roleData = createRole(3);
      const createResult = await implHandleCreateRole(roleData, businessLogic);
      expect(createResult.status).toBe(201);

      const originalUpdatedAt = createResult.body.data.updatedAt;

      // Wait a bit to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      // Soft delete and restore
      const deleteResult = await implHandleDeleteRole(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const restoreResult = await implHandleRestoreRole(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(200);

      const getResult = await implHandleGetRole(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(200);
      expect(getResult.body.data?.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating roles with STRING_FIELD of soft deleted roles", async () => {
      // Create and soft delete a roles
      const roleData1 = createTestRole();
      const createResult1 = await implHandleCreateRole(roleData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const deleteResult = await implHandleDeleteRole(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Should be able to create new roles with same STRING_FIELD
      const roleData2 = createTestRole2();
      const createResult2 = await implHandleCreateRole(roleData2, businessLogic);

      expect(createResult2.status).toBe(201);
      expect(createResult2.body.data.STRING_FIELD).toBe(roleData2.STRING_FIELD);
      expect(await dbRepository.getRoleCount()).toBe(1);
    });

    it("should prevent creating roles with STRING_FIELD of active roles", async () => {
      const roleData1 = createTestRole();
      const createResult1 = await implHandleCreateRole(roleData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const roleData2 = createTestRole2();
      const createResult2 = await implHandleCreateRole(roleData2, businessLogic);

      expect(createResult2.status).toBe(409);
      expect(createResult2.body.status).toBe("failed");
      expect(createResult2.body.error).toContain("Role with the same STRING_FIELD already exists");
    });

    it("should prevent restoring roles if STRING_FIELD is now taken", async () => {
      // Create and soft delete a roles
      const roleData1 = createTestRole();
      const createResult1 = await implHandleCreateRole(roleData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const deleteResult = await implHandleDeleteRole(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Create new roles with same STRING_FIELD
      const roleData2 = createTestRole2();
      const createResult2 = await implHandleCreateRole(roleData2, businessLogic);
      expect(createResult2.status).toBe(201);

      // Should not be able to restore the first roles
      const restoreResult = await implHandleRestoreRole(createResult1.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });
  });
});
