//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/interface";
import { TEMPLATE_CAPITALIZEDBusinessLogic } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/BusinessLogic";
import { MongoTEMPLATE_CAPITALIZEDRepository } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/MongoRepository";
import { TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateTEMPLATE_CAPITALIZED } from "@/app/api/v1/TEMPLATE_API_PATHs/impl";
import {
  createFullTEMPLATE_CAPITALIZED,
  createMinimalTEMPLA<PERSON>_CAPITALIZED,
  createTEMPLATE_CAPITALIZEDWithDescription,
  createTEMPLATE_CAPITALIZEDWithTags,
  createTEMPLATE_CAPITALIZEDWithWhitespace,
  createDuplicateTEMPLATE_CAPITALIZED,
  createSecondDuplicateTEMPLATE_CAPITALIZED,
  createInvalidTEMPLATE_CAPITALIZED,
  createTEMPLATE_CAPITALIZEDWithManyTags,
  createTEMPLATE_CAPITALIZEDWithoutDescription,
  createTEMPLATE_CAPITALIZEDWithEmptyTags
} from "./object_creator";

describe("Create TEMPLATE_CAPITALIZED API Tests", () => {
  let businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface;
    let dbRepository: TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("TEMPLATE_CAPITALIZED");
      await driver.connect()
      const originalDb = new MongoTEMPLATE_CAPITALIZEDRepository(driver);
      dbRepository = new TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper(originalDb, driver);
      businessLogic = new TEMPLATE_CAPITALIZEDBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("POST /api/v1/TEMPLATE_API_PATHs", () => {
    it("should successfully create a new TEMPLATE_CAMELCASEDs with all fields", async () => {
      const TEMPLATE_CAMELCASEDsData = createFullTEMPLATE_CAPITALIZED();

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(TEMPLATE_CAMELCASEDsData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(TEMPLATE_CAMELCASEDsData.ARRAY_FIELD2);
      expect(result.body.data?.ARRAY_FIELD).toEqual(TEMPLATE_CAMELCASEDsData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(TEMPLATE_CAMELCASEDsData.variables);
      expect(result.body.data?.tags).toEqual(TEMPLATE_CAMELCASEDsData.tags);
      expect(result.body.data?.isActive).toBe(TEMPLATE_CAMELCASEDsData.isActive);
      expect(result.body.data?.id).toBeDefined();
      expect(result.body.data?.createdAt).toBeDefined();
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should successfully create a TEMPLATE_CAMELCASEDs with minimal required fields", async () => {
      const TEMPLATE_CAMELCASEDsData = createMinimalTEMPLATE_CAPITALIZED();

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(TEMPLATE_CAMELCASEDsData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD).toEqual(TEMPLATE_CAMELCASEDsData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(TEMPLATE_CAMELCASEDsData.variables);
      expect(result.body.data?.isActive).toBe(true); // Should default to true
      expect(result.body.data?.tags).toEqual([]);
    });

    it("should create TEMPLATE_CAMELCASED with ARRAY_FIELD2", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZEDWithDescription();

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.ARRAY_FIELD2).toBe(TEMPLATE_CAMELCASEDsData.ARRAY_FIELD2);
    });

    it("should create TEMPLATE_CAMELCASED with tags", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZEDWithTags();

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.tags).toEqual(TEMPLATE_CAMELCASEDsData.tags);
    });

    it("should trim whitespace from STRING_FIELD", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZEDWithWhitespace();

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed TEMPLATE_CAPITALIZED");
    });

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first TEMPLATE_CAMELCASEDs
      const TEMPLATE_CAMELCASEDsData1 = createDuplicateTEMPLATE_CAPITALIZED();
      await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData1, businessLogic);

      // Try to create second TEMPLATE_CAMELCASEDs with same STRING_FIELD
      const TEMPLATE_CAMELCASEDsData2 = createSecondDuplicateTEMPLATE_CAPITALIZED();
      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData2, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("TEMPLATE_CAPITALIZED with the same STRING_FIELD already exists");
    });

    it("should fail with missing STRING_FIELD", async () => {
      const TEMPLATE_CAMELCASEDsData = {
        ARRAY_FIELD2: "+6281234567890"
      };

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing ARRAY_FIELD2", async () => {
      const TEMPLATE_CAMELCASEDsData = createInvalidTEMPLATE_CAPITALIZED('missing-ARRAY_FIELD2');


      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing ARRAY_FIELD", async () => {
      const TEMPLATE_CAMELCASEDsData = createInvalidTEMPLATE_CAPITALIZED('missing-ARRAY_FIELD');

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("ARRAY_FIELD");
    });

    it("should fail with missing variables", async () => {
      const TEMPLATE_CAMELCASEDsData = createInvalidTEMPLATE_CAPITALIZED('missing-variables');

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("variables");
    });

    it("should fail with empty ARRAY_FIELD array", async () => {
      const TEMPLATE_CAMELCASEDsData = createInvalidTEMPLATE_CAPITALIZED('empty-ARRAY_FIELD');

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with empty variables array", async () => {
      const TEMPLATE_CAMELCASEDsData = createInvalidTEMPLATE_CAPITALIZED('empty-variables');

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should create TEMPLATE_CAMELCASED with many tags", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZEDWithManyTags();

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(TEMPLATE_CAMELCASEDsData.tags);
    });

    it("should handle optional ARRAY_FIELD2", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZEDWithoutDescription();

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.ARRAY_FIELD2).toBe("");
    });

    it("should handle empty arrays for tags", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZEDWithEmptyTags();

      const result = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(TEMPLATE_CAMELCASEDsData.tags);
    });
  });
});
