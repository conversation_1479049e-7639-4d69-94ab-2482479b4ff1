//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/interface";
import { TEMPLATE_CAPITALIZEDBusinessLogic } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/BusinessLogic";
import { MongoTEMPLATE_CAPITALIZEDRepository } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/MongoRepository";
import { TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateTEMPLATE_CAPITALIZED, implHandleGetAllTEMPLATE_CAPITALIZEDs, implHandleDeleteTEMPLATE_CAPITALIZED } from "@/app/api/v1/TEMPLATE_API_PATHs/impl";
import {
  createMultipleTEMPLATE_CAPITALIZEDs,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams
} from "./object_creator";

describe("Consolidated TEMPLATE_CAPITALIZED API Tests", () => {
  let businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface;
  let dbRepository: TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("TEMPLATE_CAPITALIZED");
    await driver.connect()
    const originalDb = new MongoTEMPLATE_CAPITALIZEDRepository(driver);
    dbRepository = new TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper(originalDb, driver);
    businessLogic = new TEMPLATE_CAPITALIZEDBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  const testTEMPLATE_CAPITALIZEDs = createMultipleTEMPLATE_CAPITALIZEDs();

  describe("implHandleGetAllTEMPLATE_CAPITALIZEDs - Consolidated Function", () => {
    beforeEach(async () => {
      for (const TEMPLATE_CAMELCASEDsData of testTEMPLATE_CAPITALIZEDs) {
        await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData, businessLogic);
      }
    });

    it("should get all TEMPLATE_CAMELCASEDs when no parameters provided", async () => {
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);
      expect(result.body.data?.total).toBe(4);
    });

    it("should search TEMPLATE_CAMELCASEDs by STRING_FIELD", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe TEMPLATE_CAPITALIZED and Bob Johnson TEMPLATE_CAPITALIZED

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testTEMPLATE_CAPITALIZEDs[0].STRING_FIELD); // John Doe TEMPLATE_CAPITALIZED
      expect(STRING_FIELDs).toContain(testTEMPLATE_CAPITALIZEDs[2].STRING_FIELD); // Bob Johnson TEMPLATE_CAPITALIZED
    });

    it("should search TEMPLATE_CAMELCASEDs by ARRAY_FIELD2", async () => {
      const params = createSearchByDescriptionParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4); // All TEMPLATE_CAMELCASEDs have "processing" in ARRAY_FIELD2
    });

    it("should filter TEMPLATE_CAMELCASEDs by tag", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe TEMPLATE_CAPITALIZED and Bob Johnson TEMPLATE_CAPITALIZED

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testTEMPLATE_CAPITALIZEDs[0].STRING_FIELD); // John Doe TEMPLATE_CAPITALIZED
      expect(STRING_FIELDs).toContain(testTEMPLATE_CAPITALIZEDs[2].STRING_FIELD); // Bob Johnson TEMPLATE_CAPITALIZED
    });

    it("should filter TEMPLATE_CAMELCASEDs by Customer tag", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe TEMPLATE_CAPITALIZED and Jane Smith TEMPLATE_CAPITALIZED

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testTEMPLATE_CAPITALIZEDs[0].STRING_FIELD); // John Doe TEMPLATE_CAPITALIZED
      expect(STRING_FIELDs).toContain(testTEMPLATE_CAPITALIZEDs[1].STRING_FIELD); // Jane Smith TEMPLATE_CAPITALIZED
    });

    it("should handle pagination", async () => {
      const params = createPaginationParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(4);
    });

    it("should handle sorting by STRING_FIELD", async () => {
      const params = createSortByNameAscParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs![0]).toBe(testTEMPLATE_CAPITALIZEDs[3].STRING_FIELD); // Alice Brown TEMPLATE_CAPITALIZED
      expect(STRING_FIELDs![1]).toBe(testTEMPLATE_CAPITALIZEDs[2].STRING_FIELD); // Bob Johnson TEMPLATE_CAPITALIZED
      expect(STRING_FIELDs![2]).toBe(testTEMPLATE_CAPITALIZEDs[1].STRING_FIELD); // Jane Smith TEMPLATE_CAPITALIZED
      expect(STRING_FIELDs![3]).toBe(testTEMPLATE_CAPITALIZEDs[0].STRING_FIELD); // John Doe TEMPLATE_CAPITALIZED
    });

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // VIP TEMPLATE_CAMELCASEDs (tag filter applied)
    });

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams();
      const result = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should include soft deleted TEMPLATE_CAMELCASEDs when specified", async () => {
      // Get all TEMPLATE_CAMELCASEDs first to get one to delete
      const allResult = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic);
      expect(allResult.status).toBe(200);
      const TEMPLATE_CAMELCASEDsToDelete = allResult.body.data?.items[0];
      expect(TEMPLATE_CAMELCASEDsToDelete).toBeDefined();

      // Soft delete one TEMPLATE_CAMELCASEDs
      const deleteResult = await implHandleDeleteTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsToDelete!.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Get all without including deleted
      const resultWithoutDeleted = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic);
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3);

      // Get all including deleted
      const params = createIncludeDeletedParams();
      const resultWithDeleted = await implHandleGetAllTEMPLATE_CAPITALIZEDs(businessLogic, params);
      expect(resultWithDeleted.body.data?.items).toHaveLength(4);
    });
  });
});
