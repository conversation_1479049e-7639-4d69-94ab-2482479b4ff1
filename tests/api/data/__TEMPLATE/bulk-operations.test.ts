//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { TEMPLATE_CAPITALIZEDBusinessLogicInterface } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/interface";
import { TEMPLATE_CAPITALIZEDBusinessLogic } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/BusinessLogic";
import { MongoTEMPLATE_CAPITALIZEDRepository } from "@/lib/repositories/TEMPLATE_CAMELCASEDs/MongoRepository";
import { TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateTEMPLATE_CAPITALIZED,
  implHandleGetTEMPLATE_CAPITALIZED,
  implHandleBulkCreateTEMPLATE_CAPITALIZEDs,
  implHandleBulkUpdateTEMPLATE_CAPITALIZEDs,
  implHandleBulkDeleteTEMPLATE_CAPITALIZEDs
} from "@/app/api/v1/TEMPLATE_API_PATHs/impl";
import {
  createMultipleTEMPLATE_CAPITALIZEDs,
  createSimpleTEMPLATE_CAPITALIZEDs,
  createExistingTEMPLATE_CAPITALIZED,
  createDuplicateTEMPLATE_CAPITALIZEDsForBulk,
  createTEMPLATE_CAPITALIZEDsForBulkUpdate,
  createBulkUpdateData,
  createTEMPLATE_CAPITALIZEDsForBulkDelete
} from "./object_creator";

describe("TEMPLATE_CAPITALIZED Bulk Operations Tests", () => {
  let businessLogic: TEMPLATE_CAPITALIZEDBusinessLogicInterface;
  let dbRepository: TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("TEMPLATE_CAPITALIZED");
    await driver.connect()
    const originalDb = new MongoTEMPLATE_CAPITALIZEDRepository(driver);
    dbRepository = new TestTEMPLATE_CAPITALIZEDDBRepositoryWrapper(originalDb, driver);
    businessLogic = new TEMPLATE_CAPITALIZEDBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("Bulk Create", () => {
    it("should successfully create multiple TEMPLATE_CAMELCASEDs", async () => {
      const TEMPLATE_CAMELCASEDsData = createMultipleTEMPLATE_CAPITALIZEDs();

      const result = await implHandleBulkCreateTEMPLATE_CAPITALIZEDs(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(TEMPLATE_CAMELCASEDsData.length);
      expect(result.body.data[0].STRING_FIELD).toBe(TEMPLATE_CAMELCASEDsData[0].STRING_FIELD);
      expect(result.body.data[1].STRING_FIELD).toBe(TEMPLATE_CAMELCASEDsData[1].STRING_FIELD);
      expect(result.body.data[2].STRING_FIELD).toBe(TEMPLATE_CAMELCASEDsData[2].STRING_FIELD);
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(TEMPLATE_CAMELCASEDsData.length);
    });

    it("should fail if any TEMPLATE_CAMELCASED has duplicate STRING_FIELD", async () => {
      const existingTEMPLATE_CAPITALIZED = createExistingTEMPLATE_CAPITALIZED();
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(existingTEMPLATE_CAPITALIZED, businessLogic);
      expect(createResult.status).toBe(201);

      const TEMPLATE_CAMELCASEDsData = createDuplicateTEMPLATE_CAPITALIZEDsForBulk();

      const result = await implHandleBulkCreateTEMPLATE_CAPITALIZEDs(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate STRING_FIELD found: Existing TEMPLATE_CAPITALIZED");
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(1);
    });

    it("should handle simple TEMPLATE_CAMELCASEDs creation", async () => {
      const TEMPLATE_CAMELCASEDsData = createSimpleTEMPLATE_CAPITALIZEDs();

      const result = await implHandleBulkCreateTEMPLATE_CAPITALIZEDs(TEMPLATE_CAMELCASEDsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(TEMPLATE_CAMELCASEDsData.length);
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(TEMPLATE_CAMELCASEDsData.length);
    });
  });

  describe("Bulk Update", () => {
    it("should successfully update multiple TEMPLATE_CAMELCASEDs", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZEDsForBulkUpdate();
      const createResult1 = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData[0], businessLogic);
      const createResult2 = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult1.body.data.id, data: updateData[0] },
        { id: createResult2.body.data.id, data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateTEMPLATE_CAPITALIZEDs(updates, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.updatedCount).toBe(2);

      const getResult1 = await implHandleGetTEMPLATE_CAPITALIZED(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetTEMPLATE_CAPITALIZED(createResult2.body.data.id, businessLogic);

      expect(getResult1.body.data?.STRING_FIELD).toBe(updateData[0].STRING_FIELD);
      expect(getResult1.body.data?.updatedBy).toBe(updateData[0].updatedBy);
      expect(getResult2.body.data?.STRING_FIELD).toBe(updateData[1].STRING_FIELD);
      expect(getResult2.body.data?.updatedBy).toBe(updateData[1].updatedBy);
    });

    it("should fail if any TEMPLATE_CAMELCASED doesn't exist", async () => {
      const TEMPLATE_CAMELCASEDData = createTEMPLATE_CAPITALIZEDsForBulkUpdate()[0];
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDData, businessLogic);
      expect(createResult.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult.body.data.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateTEMPLATE_CAPITALIZEDs(updates, businessLogic);

      expect(result.status).toBe(500);
      expect(result.body.status).toBe("failed");
    });

    it("should fail if any update would create duplicate STRING_FIELD", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZEDsForBulkUpdate();
      const createResult1 = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData[0], businessLogic);
      const createResult2 = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updates = [
        {
          id: createResult2.body.data.id,
          data: { STRING_FIELD: TEMPLATE_CAMELCASEDsData[0].STRING_FIELD, updatedBy: "admin" }, // Try to update second TEMPLATE_CAMELCASED with first TEMPLATE_CAMELCASED's STRING_FIELD
        },
      ];

      const result = await implHandleBulkUpdateTEMPLATE_CAPITALIZEDs(updates, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate STRING_FIELD in update: TEMPLATE_CAPITALIZED 1");
    });
  });

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple TEMPLATE_CAMELCASEDs", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZEDsForBulkDelete();
      const createResult1 = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData[0], businessLogic);
      const createResult2 = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData[1], businessLogic);
      const createResult3 = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData[2], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);
      expect(createResult3.status).toBe(201);

      const result = await implHandleBulkDeleteTEMPLATE_CAPITALIZEDs([createResult1.body.data.id, createResult2.body.data.id], businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(1); // Only non-deleted

      const getResult1 = await implHandleGetTEMPLATE_CAPITALIZED(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetTEMPLATE_CAPITALIZED(createResult2.body.data.id, businessLogic);
      const getResult3 = await implHandleGetTEMPLATE_CAPITALIZED(createResult3.body.data.id, businessLogic);

      expect(getResult1.status).toBe(404);
      expect(getResult2.status).toBe(404);
      expect(getResult3.status).toBe(200);
    });

    it("should successfully hard delete multiple TEMPLATE_CAMELCASEDs", async () => {
      const TEMPLATE_CAMELCASEDsData = createTEMPLATE_CAPITALIZEDsForBulkDelete();
      const createResult1 = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData[0], businessLogic);
      const createResult2 = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDsData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const result = await implHandleBulkDeleteTEMPLATE_CAPITALIZEDs([createResult1.body.data.id, createResult2.body.data.id], businessLogic, true);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getTEMPLATE_CAPITALIZEDCount()).toBe(0);
    });

    it("should fail if any TEMPLATE_CAMELCASED doesn't exist", async () => {
      const TEMPLATE_CAMELCASEDData = createTEMPLATE_CAPITALIZEDsForBulkDelete()[0];
      const createResult = await implHandleCreateTEMPLATE_CAPITALIZED(TEMPLATE_CAMELCASEDData, businessLogic);
      expect(createResult.status).toBe(201);

      const result = await implHandleBulkDeleteTEMPLATE_CAPITALIZEDs([createResult.body.data.id, "non-existent-id"], businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty TEMPLATE_CAMELCASED IDs", async () => {
      const result = await implHandleBulkDeleteTEMPLATE_CAPITALIZEDs(["", "valid-id"], businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ID at index 0 is required");
    });
  });
});
