//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { MessageTemplateBusinessLogicInterface } from "@/lib/repositories/messageTemplates/interface";
import { MessageTemplateBusinessLogic } from "@/lib/repositories/messageTemplates/BusinessLogic";
import { MongoMessageTemplateRepository } from "@/lib/repositories/messageTemplates/MongoRepository";
import { TestMessageTemplateDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateMessageTemplate } from "@/app/api/v1/message-templates/impl";
import {
  createFullMessageTemplate,
  createMinimalMessageTemplate,
  createMessageTemplateWithDescription,
  createMessageTemplateWithTags,
  createMessageTemplateWithWhitespace,
  createDuplicateMessageTemplate,
  createSecondDuplicateMessageTemplate,
  createInvalidMessageTemplate,
  createMessageTemplateWithManyTags,
  createMessageTemplateWithoutDescription,
  createMessageTemplateWithEmptyTags
} from "./object_creator";

describe("Create MessageTemplate API Tests", () => {
  let businessLogic: MessageTemplateBusinessLogicInterface;
    let dbRepository: TestMessageTemplateDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("MessageTemplate");
      await driver.connect()
      const originalDb = new MongoMessageTemplateRepository(driver);
      dbRepository = new TestMessageTemplateDBRepositoryWrapper(originalDb, driver);
      businessLogic = new MessageTemplateBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("POST /api/v1/message-templates", () => {
    it("should successfully create a new messageTemplates with all fields", async () => {
      const messageTemplatesData = createFullMessageTemplate();

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.title).toBe(messageTemplatesData.title);
      expect(result.body.data?.content).toBe(messageTemplatesData.content);
      expect(result.body.data?.category).toEqual(messageTemplatesData.category);
      expect(result.body.data?.variables).toEqual(messageTemplatesData.variables);
      expect(result.body.data?.tags).toEqual(messageTemplatesData.tags);
      expect(result.body.data?.isActive).toBe(messageTemplatesData.isActive);
      expect(result.body.data?.id).toBeDefined();
      expect(result.body.data?.createdAt).toBeDefined();
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should successfully create a messageTemplates with minimal required fields", async () => {
      const messageTemplatesData = createMinimalMessageTemplate();

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.title).toBe(messageTemplatesData.title);
      expect(result.body.data?.category).toEqual(messageTemplatesData.category);
      expect(result.body.data?.variables).toEqual(messageTemplatesData.variables);
      expect(result.body.data?.isActive).toBe(true); // Should default to true
      expect(result.body.data?.tags).toEqual([]);
    });

    it("should create messageTemplate with content", async () => {
      const messageTemplatesData = createMessageTemplateWithDescription();

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.content).toBe(messageTemplatesData.content);
    });

    it("should create messageTemplate with tags", async () => {
      const messageTemplatesData = createMessageTemplateWithTags();

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.tags).toEqual(messageTemplatesData.tags);
    });

    it("should trim whitespace from title", async () => {
      const messageTemplatesData = createMessageTemplateWithWhitespace();

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.title).toBe("Trimmed MessageTemplate");
    });

    it("should fail with duplicate title", async () => {
      // Create first messageTemplates
      const messageTemplatesData1 = createDuplicateMessageTemplate();
      await implHandleCreateMessageTemplate(messageTemplatesData1, businessLogic);

      // Try to create second messageTemplates with same title
      const messageTemplatesData2 = createSecondDuplicateMessageTemplate();
      const result = await implHandleCreateMessageTemplate(messageTemplatesData2, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("MessageTemplate with the same title already exists");
    });

    it("should fail with missing title", async () => {
      const messageTemplatesData = {
        content: "+6281234567890"
      };

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing content", async () => {
      const messageTemplatesData = createInvalidMessageTemplate('missing-content');


      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing category", async () => {
      const messageTemplatesData = createInvalidMessageTemplate('missing-category');

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("category");
    });

    it("should fail with missing variables", async () => {
      const messageTemplatesData = createInvalidMessageTemplate('missing-variables');

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("variables");
    });

    it("should fail with empty category array", async () => {
      const messageTemplatesData = createInvalidMessageTemplate('empty-category');

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with empty variables array", async () => {
      const messageTemplatesData = createInvalidMessageTemplate('empty-variables');

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should create messageTemplate with many tags", async () => {
      const messageTemplatesData = createMessageTemplateWithManyTags();

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(messageTemplatesData.tags);
    });

    it("should handle optional content", async () => {
      const messageTemplatesData = createMessageTemplateWithoutDescription();

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.content).toBe("");
    });

    it("should handle empty arrays for tags", async () => {
      const messageTemplatesData = createMessageTemplateWithEmptyTags();

      const result = await implHandleCreateMessageTemplate(messageTemplatesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(messageTemplatesData.tags);
    });
  });
});
