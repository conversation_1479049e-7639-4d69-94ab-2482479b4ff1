import { MessageTemplateCreateInput, MessageTemplateUpdateInput } from "@/lib/repositories/messageTemplates/interface";

/**
 * Factory functions for creating test MessageTemplate objects
 * This allows for consistent test data across all test files
 * and easy modification of test objects in one place
 */

// Base creator functions for different scenarios
export function createMessageTemplate(variant: number): MessageTemplateCreateInput {
  const baseMessageTemplates: Record<number, MessageTemplateCreateInput> = {
    1: {
      title: "Customer Support MessageTemplate",
      description: "MessageTemplate for handling customer support requests",
      content: ["user_message_contains('help')", "time_between('09:00', '17:00')"],
      category: ["assign_to_support", "send_acknowledgment"],
      tags: ["Customer", "VIP"],
      isActive: true,
      createdBy: "admin"
    },
    2: {
      title: "Simple MessageTemplate",
      content: ["always_true"],
      category: ["log_message"],
      createdBy: "admin"
    },
    3: {
      title: "Test MessageTemplate",
      description: "A test messageTemplate with description",
      content: ["test_condition"],
      category: ["test_action"],
      createdBy: "admin"
    },
    4: {
      title: "Tagged MessageTemplate",
      content: ["test_condition"],
      category: ["test_action"],
      tags: ["urgent", "customer-service"],
      createdBy: "admin"
    },
    5: {
      title: "John Doe MessageTemplate",
      description: "MessageTemplate for John Doe processing",
      content: ["user_title_contains('john')", "time_between('09:00', '17:00')"],
      category: ["assign_to_support", "send_category"],
      tags: ["Customer", "VIP"],
      createdBy: "admin"
    },
    6: {
      title: "Jane Smith MessageTemplate",
      description: "MessageTemplate for Jane Smith processing",
      content: ["user_title_contains('jane')", "priority_high"],
      category: ["escalate", "notify_manager"],
      tags: ["Customer"],
      createdBy: "admin"
    },
    7: {
      title: "Bob Johnson MessageTemplate",
      description: "MessageTemplate for Bob Johnson processing",
      content: ["user_title_contains('bob')", "vip_customer"],
      category: ["priority_handling", "send_notification"],
      tags: ["VIP", "Premium"],
      createdBy: "admin"
    },
    8: {
      title: "Alice Brown MessageTemplate",
      description: "MessageTemplate for Alice Brown processing",
      content: ["user_title_contains('alice')", "lead_qualification"],
      category: ["assign_to_sales", "track_conversion"],
      tags: ["Premium"],
      createdBy: "admin"
    }
  };

  if (!baseMessageTemplates[variant]) {
    throw new Error(`MessageTemplate variant ${variant} not found. Available variants: ${Object.keys(baseMessageTemplates).join(', ')}`);
  }

  return { ...baseMessageTemplates[variant] };
}

// Specialized creator functions for specific test scenarios
export function createMinimalMessageTemplate(): MessageTemplateCreateInput {
  return createMessageTemplate(2);
}

export function createFullMessageTemplate(): MessageTemplateCreateInput {
  return createMessageTemplate(1);
}

export function createMessageTemplateWithDescription(): MessageTemplateCreateInput {
  return createMessageTemplate(3);
}

export function createMessageTemplateWithTags(): MessageTemplateCreateInput {
  return createMessageTemplate(4);
}

// Creator for multiple messageTemplates (useful for bulk operations and search tests)
export function createMultipleMessageTemplates(): MessageTemplateCreateInput[] {
  return [
    createMessageTemplate(5), // John Doe MessageTemplate
    createMessageTemplate(6), // Jane Smith MessageTemplate
    createMessageTemplate(7), // Bob Johnson MessageTemplate
    createMessageTemplate(8)  // Alice Brown MessageTemplate
  ];
}

// Creator for simple test messageTemplates (useful for basic CRUD operations)
export function createSimpleMessageTemplates(): MessageTemplateCreateInput[] {
  return [
    { title: "A", content: ["1"], category: ["a"], createdBy: "admin" },
    { title: "B", content: ["2"], category: ["b"], createdBy: "admin" },
    { title: "C", content: ["3"], category: ["c"], createdBy: "admin" }
  ];
}

// Creator for messageTemplates with specific tags (useful for filtering tests)
export function createMessageTemplatesWithTags(): MessageTemplateCreateInput[] {
  return [
    { title: "John Doe", content: ["x"], category: ["a"], createdBy: "admin", tags: ["Customer", "VIP"] },
    { title: "Jane Smith", content: ["y"], category: ["b"], createdBy: "admin", tags: ["Lead", "Potential"] },
    { title: "Bob Johnson", content: ["z"], category: ["c"], createdBy: "admin", tags: ["Customer"] },
    { title: "Alice Brown", content: ["a"], category: ["d"], createdBy: "admin", tags: ["VIP"] }
  ];
}

// Update data creators
export function createMessageTemplateUpdate(variant: number): MessageTemplateUpdateInput {
  const baseUpdates: Record<number, MessageTemplateUpdateInput> = {
    1: {
      title: "Updated MessageTemplate",
      description: "Updated description",
      content: ["updated_condition"],
      category: ["updated_action"],
      tags: ["VIP", "Premium"],
      isActive: false,
      updatedBy: "admin"
    },
    2: {
      title: "New Name",
      updatedBy: "admin"
    },
    3: {
      description: "Updated description only",
      updatedBy: "admin"
    },
    4: {
      tags: ["new-tag", "updated-tag"],
      updatedBy: "admin"
    },
    5: {
      isActive: false,
      updatedBy: "admin"
    }
  };

  if (!baseUpdates[variant]) {
    throw new Error(`MessageTemplate update variant ${variant} not found. Available variants: ${Object.keys(baseUpdates).join(', ')}`);
  }

  return { ...baseUpdates[variant] };
}

// Specialized update creators
export function createFullMessageTemplateUpdate(): MessageTemplateUpdateInput {
  return createMessageTemplateUpdate(1);
}

export function createNameOnlyUpdate(): MessageTemplateUpdateInput {
  return createMessageTemplateUpdate(2);
}

export function createDescriptionOnlyUpdate(): MessageTemplateUpdateInput {
  return createMessageTemplateUpdate(3);
}

export function createTagsOnlyUpdate(): MessageTemplateUpdateInput {
  return createMessageTemplateUpdate(4);
}

export function createStatusOnlyUpdate(): MessageTemplateUpdateInput {
  return createMessageTemplateUpdate(5);
}

// Invalid update data creators for validation tests
export function createInvalidUpdate(type: 'empty-title' | 'empty-content' | 'empty-category' | 'empty-object'): any {
  const invalidUpdates = {
    'empty-title': {
      title: "",
      updatedBy: "admin"
    },
    'empty-content': {
      content: [],
      updatedBy: "admin"
    },
    'empty-category': {
      category: [],
      updatedBy: "admin"
    },
    'empty-object': {}
  };

  return invalidUpdates[type];
}

// Update with whitespace for trimming tests
export function createUpdateWithWhitespace(): MessageTemplateUpdateInput {
  return {
    title: "   Trimmed Name   ",
    updatedBy: "admin"
  };
}

// Update for duplicate title testing
export function createDuplicateNameUpdate(existingName: string): MessageTemplateUpdateInput {
  return {
    title: existingName,
    updatedBy: "admin"
  };
}

// Update with same title (no change scenario)
export function createSameNameUpdate(): MessageTemplateUpdateInput {
  return {
    title: "Simple MessageTemplate", // Same as createMinimalMessageTemplate
    description: "Updated description",
    updatedBy: "admin"
  };
}

// MessageTemplate for soft delete testing
export function createMessageTemplateForSoftDelete(): MessageTemplateCreateInput {
  return {
    title: "To Be Deleted",
    content: ["cond"],
    category: ["act"],
    createdBy: "admin"
  };
}

// Update for soft deleted messageTemplate testing
export function createUpdateForSoftDeleted(): MessageTemplateUpdateInput {
  return {
    title: "Should Not Work",
    updatedBy: "admin"
  };
}

// Update with whitespace in all fields for comprehensive trimming test
export function createUpdateWithAllFieldsWhitespace(): MessageTemplateUpdateInput {
  return {
    title: "   Trimmed Name   ",
    description: "   Trimmed Description   ",
    content: ["   trimmed_condition   "],
    category: ["   trimmed_action   "],
    tags: ["   tag1   ", "   tag2   "],
    updatedBy: "admin"
  };
}

// MessageTemplate for trimming test
export function createMessageTemplateForTrimming(): MessageTemplateCreateInput {
  return {
    title: "Original MessageTemplate",
    content: ["cond"],
    category: ["act"],
    createdBy: "admin"
  };
}

// MessageTemplate for active status testing
export function createActiveMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Active MessageTemplate",
    content: ["cond"],
    category: ["act"],
    isActive: true,
    createdBy: "admin"
  };
}

// Update for status change testing
export function createStatusChangeUpdate(): MessageTemplateUpdateInput {
  return {
    isActive: false,
    updatedBy: "admin"
  };
}

// ========================================
// PARAMS CREATORS FOR implHandleGetAllMessageTemplates
// ========================================

// Search params
export function createSearchByNameParams() {
  return { search: "John" };
}

export function createSearchByDescriptionParams() {
  return { search: "processing" };
}

export function createEmptySearchParams() {
  return { search: "" };
}

export function createWhitespaceSearchParams() {
  return { search: "   " };
}

export function createNonExistentSearchParams() {
  return { search: "NonExistent" };
}

// Filter params
export function createVipTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "VIP" }]
  };
}

export function createCustomerTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "Customer" }]
  };
}

// Pagination params
export function createPaginationParams() {
  return {
    page: 1,
    limit: 2
  };
}

// Sorting params
export function createSortByNameAscParams() {
  return {
    sorts: [{ field: "title", direction: "asc" as const }]
  };
}

// Combined params
export function createSearchAndTagParams() {
  return {
    search: "John",
    tag: "VIP"
  };
}

// Include deleted params
export function createIncludeDeletedParams() {
  return { includeDeleted: true };
}

// Legacy tag params (converted to filters format)
export function createEmptyTagParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceTagParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

export function createNonExistentTagParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

// Additional search params for read.test.ts
export function createSearchByTagParams() {
  return { search: "VIP" };
}

export function createUnmatchedSearchParams() {
  return { search: "nonexistent" };
}

export function createUndefinedSearchParams() {
  return { search: undefined };
}

// Additional filter params for read.test.ts
export function createNonExistentFilterParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

export function createEmptyFilterFieldParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceFilterFieldParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

// ========================================
// CREATORS FOR DELETE TESTS
// ========================================

// MessageTemplate for retry delete testing
export function createRetryDeleteMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Retry Delete",
    content: ["attempt"],
    category: ["log"],
    createdBy: "admin"
  };
}

// ========================================
// CREATORS FOR SPECIAL CASES (MessageTemplate-specific)
// ========================================

// MessageTemplate with special characters and unicode
export function createSpecialCharacterMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "José María O'Connor",
    description: "Handles unicode 🎉 & symbols",
    content: ["title.includes('José')"],
    category: ["notify", "log"],
    tags: ["Special", "🚀", "Test@Tag"],
    createdBy: "admin"
  };
}

// MessageTemplate with very long content (MessageTemplate-specific test)
export function createLongContentMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Very Long MessageTemplate Name That Exceeds Normal Length Expectations And Tests System Limits",
    description: "This is a very long description that tests how the system handles extensive text content in messageTemplate descriptions. It includes multiple sentences and should test the limits of what the system can handle in terms of content length and processing.",
    content: [
      "user.message.length > 1000",
      "user.message.includes('very long query with lots of details')",
      "user.session.duration > 3600"
    ],
    category: [
      "log_extensive_details",
      "notify_admin_of_long_interaction",
      "create_detailed_report",
      "escalate_to_specialist"
    ],
    tags: ["LongContent", "EdgeCase", "SystemLimits", "Performance"],
    createdBy: "admin"
  };
}

// MessageTemplate with edge case content (MessageTemplate-specific)
export function createEdgeCaseConditionsMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Edge Case Conditions",
    description: "Tests complex condition parsing",
    content: [
      "user.age >= 18 && user.age <= 65",
      "user.location.country === 'US' || user.location.country === 'CA'",
      "user.preferences.notifications === true"
    ],
    category: [
      "apply_regional_messageTemplates",
      "send_age_appropriate_content"
    ],
    tags: ["EdgeCase", "Complex"],
    createdBy: "admin"
  };
}

// MessageTemplate with complex category (MessageTemplate-specific)
export function createComplexActionsMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Complex Actions MessageTemplate",
    description: "Tests complex action execution",
    content: ["trigger_complex_workflow"],
    category: [
      "webhook.call('https://api.example.com/notify')",
      "database.update('user_stats', {last_interaction: now()})",
      "category.send(template='complex_notification', to=user.category)",
      "analytics.track('complex_messageTemplate_triggered', {messageTemplate_id: this.id})"
    ],
    tags: ["Complex", "Integration"],
    createdBy: "admin"
  };
}

// MessageTemplate with empty optional fields (MessageTemplate-specific edge case)
export function createEmptyOptionalFieldsMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Empty Optional Fields",
    content: ["basic_condition"],
    category: ["basic_action"],
    description: "",
    tags: [],
    createdBy: "admin"
  };
}

// MessageTemplate for testing AI-specific business logic
export function createAiLogicMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "AI Decision MessageTemplate",
    description: "Tests AI-specific decision making logic",
    content: [
      "ai.confidence > 0.8",
      "ai.model === 'gpt-4'",
      "ai.context.length > 100"
    ],
    category: [
      "ai.respond_with_confidence",
      "ai.log_decision_path",
      "ai.update_learning_model"
    ],
    tags: ["AI", "MachineLearning", "Confidence"],
    createdBy: "admin"
  };
}

// Creators for delete test scenarios
export function createComplexMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Complex MessageTemplate",
    description: "Full field test",
    content: ["user.role == 'admin'"],
    category: ["grant_access", "log_activity"],
    tags: ["admin", "security"],
    isActive: true,
    createdBy: "admin"
  };
}

export function createMinimalDeleteMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Minimal MessageTemplate",
    content: ["is.loggedIn"],
    category: ["alert"],
    createdBy: "admin"
  };
}

// MessageTemplates for testing deletion effects on other messageTemplates
export function createMessageTemplatesForDeletionTest(): MessageTemplateCreateInput[] {
  return [
    { title: "Keep This One", content: ["x"], category: ["a"], createdBy: "admin" },
    { title: "Delete This One", content: ["y"], category: ["b"], createdBy: "admin" },
    { title: "Keep This Too", content: ["z"], category: ["c"], createdBy: "admin" }
  ];
}

// Creators for bulk operations testing
export function createExistingMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Existing MessageTemplate",
    description: "An existing messageTemplate",
    content: ["User says test"],
    category: ["Show test response"],
    createdBy: "admin"
  };
}

export function createDuplicateMessageTemplatesForBulk(): MessageTemplateCreateInput[] {
  return [
    {
      title: "Existing MessageTemplate", // Duplicate title
      description: "Another messageTemplate with same title",
      content: ["User says hello"],
      category: ["Show greeting"],
      createdBy: "admin"
    },
    {
      title: "New MessageTemplate",
      description: "A new messageTemplate",
      content: ["User says goodbye"],
      category: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// MessageTemplates for bulk update testing
export function createMessageTemplatesForBulkUpdate(): MessageTemplateCreateInput[] {
  return [
    {
      title: "MessageTemplate 1",
      description: "First messageTemplate",
      content: ["User says hello"],
      category: ["Show greeting"],
      createdBy: "admin"
    },
    {
      title: "MessageTemplate 2",
      description: "Second messageTemplate",
      content: ["User says goodbye"],
      category: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// Bulk update data
export function createBulkUpdateData(): any[] {
  return [
    {
      title: "Updated MessageTemplate 1",
      description: "Updated first messageTemplate",
      updatedBy: "admin"
    },
    {
      title: "Updated MessageTemplate 2",
      description: "Updated second messageTemplate",
      updatedBy: "admin"
    }
  ];
}

// MessageTemplates for bulk delete testing
export function createMessageTemplatesForBulkDelete(): MessageTemplateCreateInput[] {
  return [
    {
      title: "MessageTemplate 1",
      description: "First messageTemplate",
      content: ["User says hello"],
      category: ["Show greeting"],
      createdBy: "admin"
    },
    {
      title: "MessageTemplate 2",
      description: "Second messageTemplate",
      content: ["User says goodbye"],
      category: ["Show farewell"],
      createdBy: "admin"
    },
    {
      title: "MessageTemplate 3",
      description: "Third messageTemplate",
      content: ["User asks question"],
      category: ["Show help"],
      createdBy: "admin"
    }
  ];
}

// Invalid data creators for validation tests
export function createInvalidMessageTemplate(type: 'missing-title' | 'missing-content' | 'missing-category' | 'empty-content' | 'empty-category' | 'missing-content'): any {
  const invalidMessageTemplates = {
    'missing-content': {
      title: "John Doe"
    },
    'missing-title': {
      content: ["test_condition"],
      category: ["test_action"],
      createdBy: "admin"
    },
    'missing-content': {
      title: "Invalid MessageTemplate",
      category: ["test_action"],
      createdBy: "admin"
    },
    'missing-category': {
      title: "Invalid MessageTemplate",
      content: ["test_condition"],
      createdBy: "admin"
    },
    'empty-content': {
      title: "Invalid MessageTemplate",
      content: [],
      category: ["test_action"],
      createdBy: "admin"
    },
    'empty-category': {
      title: "Invalid MessageTemplate",
      content: ["test_condition"],
      category: [],
      createdBy: "admin"
    }
  };

  return invalidMessageTemplates[type];
}

// Creator for messageTemplates with special characteristics
export function createMessageTemplateWithWhitespace(): MessageTemplateCreateInput {
  return {
    title: "  Trimmed MessageTemplate  ",
    content: ["test_condition"],
    category: ["test_action"],
    createdBy: "admin"
  };
}

export function createMessageTemplateWithManyTags(): MessageTemplateCreateInput {
  return {
    title: "Multi-tag MessageTemplate",
    content: ["test_condition"],
    category: ["test_action"],
    tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    createdBy: "admin"
  };
}

export function createMessageTemplateWithoutDescription(): MessageTemplateCreateInput {
  return {
    title: "MessageTemplate without description",
    content: ["test_condition"],
    category: ["test_action"],
    createdBy: "admin"
  };
}

export function createMessageTemplateWithEmptyTags(): MessageTemplateCreateInput {
  return {
    title: "MessageTemplate with empty tags",
    content: ["test_condition"],
    category: ["test_action"],
    tags: [],
    createdBy: "admin"
  };
}

// Duplicate messageTemplate creator for conflict testing
export function createDuplicateMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Duplicate MessageTemplate",
    content: ["condition1"],
    category: ["action1"],
    createdBy: "admin"
  };
}

export function createSecondDuplicateMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Duplicate MessageTemplate", // Same title as above
    content: ["condition2"],
    category: ["action2"],
    createdBy: "admin"
  };
}

// Test messageTemplate with specific title for soft delete tests
export function createTestMessageTemplate(): MessageTemplateCreateInput {
  return {
    title: "Test MessageTemplate",
    content: ["condition1"],
    category: ["action1"],
    createdBy: "admin"
  };
}

export function createTestMessageTemplate2(): MessageTemplateCreateInput {
  return {
    title: "Test MessageTemplate",
    content: ["condition2"],
    category: ["action2"],
    createdBy: "admin"
  };
}
