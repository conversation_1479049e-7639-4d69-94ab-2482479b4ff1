// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatBusinessLogicInterface } from "@/lib/repositories/chats/interface";
import { ChatBusinessLogic } from "@/lib/repositories/chats/BusinessLogic";
import { MongoChatRepository } from "@/lib/repositories/chats/MongoRepository";
import { TestChatDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChat, implHandleGetChat, implHandleDeleteChat } from "@/app/api/v1/chats/impl";
import {
  createChat,
  createSimpleChats,
  createComplexChat,
  createMinimalDeleteChat,
  createChatsForDeletionTest,
  createRetryDeleteChat
} from "./object_creator";

describe("Delete Chat API Tests", () => {
  let businessLogic: ChatBusinessLogicInterface;
  let dbRepository: TestChatDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Chat");
    await driver.connect();
    const originalDb = new MongoChatRepository(driver);
    dbRepository = new TestChatDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("DELETE /api/v1/chats/:id", () => {
    it("should successfully delete an existing chat", async () => {
      const chatsData = createChat(5); // John Doe Chat
      const createResult = await implHandleCreateChat(chatsData, businessLogic);
      const chatsId = createResult.body.data.id;

      const getResult = await implHandleGetChat(chatsId, businessLogic);
      expect(getResult.status).toBe(200);

      const deleteResult = await implHandleDeleteChat(chatsId, businessLogic);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");
      expect(deleteResult.body.data.message).toBe("Chat deleted successfully");

      const getAfterDelete = await implHandleGetChat(chatsId, businessLogic);
      expect(getAfterDelete.status).toBe(404);
    });

    it("should verify chat count decreases after deletion", async () => {
      const chatsData = createSimpleChats();

      const chatsIds: string[] = [];
      for (const data of chatsData) {
        const result = await implHandleCreateChat(data, businessLogic);
        chatsIds.push(result.body.data?.id);
      }

      expect(await dbRepository.getChatCount()).toBe(3);

      const deleteResult = await implHandleDeleteChat(chatsIds[1], businessLogic);
      expect(deleteResult.status).toBe(200);
      expect(await dbRepository.getChatCount()).toBe(2);

      const getDeleted = await implHandleGetChat(chatsIds[1], businessLogic);
      expect(getDeleted.status).toBe(404);
    });

    it("should fail to delete non-existent chat", async () => {
      const result = await implHandleDeleteChat("507f1f77bcf86cd799439011", businessLogic);
      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Chat not found");
    });

    it("should fail with empty chat ID", async () => {
      const result = await implHandleDeleteChat("", businessLogic);
      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Chat ID is required");
    });

    it("should handle deletion with all fields", async () => {
      const chatsData = createComplexChat();
      const result = await implHandleCreateChat(chatsData, businessLogic);
      const id = result.body.data?.id;

      const deleteResult = await implHandleDeleteChat(id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const getAfter = await implHandleGetChat(id, businessLogic);
      expect(getAfter.status).toBe(404);
    });

    it("should handle deletion with minimal fields", async () => {
      const chatsData = createMinimalDeleteChat();
      const result = await implHandleCreateChat(chatsData, businessLogic);
      const id = result.body.data?.id;

      const deleteResult = await implHandleDeleteChat(id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const getAfter = await implHandleGetChat(id, businessLogic);
      expect(getAfter.status).toBe(404);
    });

    it("should allow deletion of multiple chats", async () => {
      const chatsData = createSimpleChats();

      const ids: string[] = [];
      for (const data of chatsData) {
        const res = await implHandleCreateChat(data, businessLogic);
        ids.push(res.body.data.id);
      }

      for (const id of ids) {
        const res = await implHandleDeleteChat(id, businessLogic);
        expect(res.status).toBe(200);
      }

      expect(await dbRepository.getChatCount()).toBe(0);
    });

    it("should not affect other chats when deleting one", async () => {
      const chatsData = createChatsForDeletionTest();

      const ids: string[] = [];
      for (const data of chatsData) {
        const res = await implHandleCreateChat(data, businessLogic);
        ids.push(res.body.data.id);
      }

      await implHandleDeleteChat(ids[1], businessLogic);

      const getDeleted = await implHandleGetChat(ids[1], businessLogic);
      expect(getDeleted.status).toBe(404);

      const first = await implHandleGetChat(ids[0], businessLogic);
      expect(first.status).toBe(200);

      const third = await implHandleGetChat(ids[2], businessLogic);
      expect(third.status).toBe(200);

      expect(await dbRepository.getChatCount()).toBe(2);
    });

    it("should handle attempting to delete the same chat twice", async () => {
      const chatsData = createRetryDeleteChat();
      const result = await implHandleCreateChat(chatsData, businessLogic);
      const id = result.body.data?.id;

      const first = await implHandleDeleteChat(id, businessLogic);
      expect(first.status).toBe(200);

      const second = await implHandleDeleteChat(id, businessLogic);
      expect(second.status).toBe(404);
    });
  });
});
