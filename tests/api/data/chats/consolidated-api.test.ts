//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatBusinessLogicInterface } from "@/lib/repositories/chats/interface";
import { ChatBusinessLogic } from "@/lib/repositories/chats/BusinessLogic";
import { MongoChatRepository } from "@/lib/repositories/chats/MongoRepository";
import { TestChatDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChat, implHandleGetAllChats, implHandleDeleteChat } from "@/app/api/v1/chats/impl";
import {
  createMultipleChats,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams
} from "./object_creator";

describe("Consolidated Chat API Tests", () => {
  let businessLogic: ChatBusinessLogicInterface;
  let dbRepository: TestChatDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Chat");
    await driver.connect()
    const originalDb = new MongoChatRepository(driver);
    dbRepository = new TestChatDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  const testChats = createMultipleChats();

  describe("implHandleGetAllChats - Consolidated Function", () => {
    beforeEach(async () => {
      for (const chatsData of testChats) {
        await implHandleCreateChat(chatsData, businessLogic);
      }
    });

    it("should get all chats when no parameters provided", async () => {
      const result = await implHandleGetAllChats(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);
      expect(result.body.data?.total).toBe(4);
    });

    it("should search chats by STRING_FIELD", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Chat and Bob Johnson Chat

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testChats[0].STRING_FIELD); // John Doe Chat
      expect(STRING_FIELDs).toContain(testChats[2].STRING_FIELD); // Bob Johnson Chat
    });

    it("should search chats by ARRAY_FIELD2", async () => {
      const params = createSearchByDescriptionParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4); // All chats have "processing" in ARRAY_FIELD2
    });

    it("should filter chats by tag", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Chat and Bob Johnson Chat

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testChats[0].STRING_FIELD); // John Doe Chat
      expect(STRING_FIELDs).toContain(testChats[2].STRING_FIELD); // Bob Johnson Chat
    });

    it("should filter chats by Customer tag", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe Chat and Jane Smith Chat

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testChats[0].STRING_FIELD); // John Doe Chat
      expect(STRING_FIELDs).toContain(testChats[1].STRING_FIELD); // Jane Smith Chat
    });

    it("should handle pagination", async () => {
      const params = createPaginationParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(4);
    });

    it("should handle sorting by STRING_FIELD", async () => {
      const params = createSortByNameAscParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs![0]).toBe(testChats[3].STRING_FIELD); // Alice Brown Chat
      expect(STRING_FIELDs![1]).toBe(testChats[2].STRING_FIELD); // Bob Johnson Chat
      expect(STRING_FIELDs![2]).toBe(testChats[1].STRING_FIELD); // Jane Smith Chat
      expect(STRING_FIELDs![3]).toBe(testChats[0].STRING_FIELD); // John Doe Chat
    });

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // VIP chats (tag filter applied)
    });

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams();
      const result = await implHandleGetAllChats(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should include soft deleted chats when specified", async () => {
      // Get all chats first to get one to delete
      const allResult = await implHandleGetAllChats(businessLogic);
      expect(allResult.status).toBe(200);
      const chatsToDelete = allResult.body.data?.items[0];
      expect(chatsToDelete).toBeDefined();

      // Soft delete one chats
      const deleteResult = await implHandleDeleteChat(chatsToDelete!.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Get all without including deleted
      const resultWithoutDeleted = await implHandleGetAllChats(businessLogic);
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3);

      // Get all including deleted
      const params = createIncludeDeletedParams();
      const resultWithDeleted = await implHandleGetAllChats(businessLogic, params);
      expect(resultWithDeleted.body.data?.items).toHaveLength(4);
    });
  });
});
