//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatBusinessLogicInterface } from "@/lib/repositories/chats/interface";
import { ChatBusinessLogic } from "@/lib/repositories/chats/BusinessLogic";
import { MongoChatRepository } from "@/lib/repositories/chats/MongoRepository";
import { TestChatDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChat } from "@/app/api/v1/chats/impl";
import {
  createFullChat,
  createMinimalChat,
  createChatWithDescription,
  createChatWithTags,
  createChatWithWhitespace,
  createDuplicateChat,
  createSecondDuplicateChat,
  createInvalidChat,
  createChatWithManyTags,
  createChatWithoutDescription,
  createChatWithEmptyTags
} from "./object_creator";

describe("Create Chat API Tests", () => {
  let businessLogic: ChatBusinessLogicInterface;
    let dbRepository: TestChatDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("Chat");
      await driver.connect()
      const originalDb = new MongoChatRepository(driver);
      dbRepository = new TestChatDBRepositoryWrapper(originalDb, driver);
      businessLogic = new ChatBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("POST /api/v1/chats", () => {
    it("should successfully create a new chats with all fields", async () => {
      const chatsData = createFullChat();

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(chatsData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(chatsData.ARRAY_FIELD2);
      expect(result.body.data?.ARRAY_FIELD).toEqual(chatsData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(chatsData.variables);
      expect(result.body.data?.tags).toEqual(chatsData.tags);
      expect(result.body.data?.isActive).toBe(chatsData.isActive);
      expect(result.body.data?.id).toBeDefined();
      expect(result.body.data?.createdAt).toBeDefined();
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should successfully create a chats with minimal required fields", async () => {
      const chatsData = createMinimalChat();

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(chatsData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD).toEqual(chatsData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(chatsData.variables);
      expect(result.body.data?.isActive).toBe(true); // Should default to true
      expect(result.body.data?.tags).toEqual([]);
    });

    it("should create chat with ARRAY_FIELD2", async () => {
      const chatsData = createChatWithDescription();

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.ARRAY_FIELD2).toBe(chatsData.ARRAY_FIELD2);
    });

    it("should create chat with tags", async () => {
      const chatsData = createChatWithTags();

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.tags).toEqual(chatsData.tags);
    });

    it("should trim whitespace from STRING_FIELD", async () => {
      const chatsData = createChatWithWhitespace();

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Chat");
    });

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first chats
      const chatsData1 = createDuplicateChat();
      await implHandleCreateChat(chatsData1, businessLogic);

      // Try to create second chats with same STRING_FIELD
      const chatsData2 = createSecondDuplicateChat();
      const result = await implHandleCreateChat(chatsData2, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Chat with the same STRING_FIELD already exists");
    });

    it("should fail with missing STRING_FIELD", async () => {
      const chatsData = {
        ARRAY_FIELD2: "+6281234567890"
      };

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing ARRAY_FIELD2", async () => {
      const chatsData = createInvalidChat('missing-ARRAY_FIELD2');


      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing ARRAY_FIELD", async () => {
      const chatsData = createInvalidChat('missing-ARRAY_FIELD');

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("ARRAY_FIELD");
    });

    it("should fail with missing variables", async () => {
      const chatsData = createInvalidChat('missing-variables');

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("variables");
    });

    it("should fail with empty ARRAY_FIELD array", async () => {
      const chatsData = createInvalidChat('empty-ARRAY_FIELD');

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with empty variables array", async () => {
      const chatsData = createInvalidChat('empty-variables');

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should create chat with many tags", async () => {
      const chatsData = createChatWithManyTags();

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(chatsData.tags);
    });

    it("should handle optional ARRAY_FIELD2", async () => {
      const chatsData = createChatWithoutDescription();

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.ARRAY_FIELD2).toBe("");
    });

    it("should handle empty arrays for tags", async () => {
      const chatsData = createChatWithEmptyTags();

      const result = await implHandleCreateChat(chatsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(chatsData.tags);
    });
  });
});
