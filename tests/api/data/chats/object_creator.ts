import { ChatCreateInput, ChatUpdateInput } from "@/lib/repositories/chats/interface";

/**
 * Factory functions for creating test Chat objects
 * This allows for consistent test data across all test files
 * and easy modification of test objects in one place
 */

// Base creator functions for different scenarios
export function createChat(variant: number): ChatCreateInput {
  const baseChats: Record<number, ChatCreateInput> = {
    1: {
      STRING_FIELD: "Customer Support Chat",
      STRING_FIELD2: "Chat for handling customer support requests",
      ARRAY_FIELD2: ["user_message_contains('help')", "time_between('09:00', '17:00')"],
      ARRAY_FIELD: ["assign_to_support", "send_acknowledgment"],
      tags: ["Customer", "VIP"],
      isActive: true,
      createdBy: "admin"
    },
    2: {
      STRING_FIELD: "Simple Chat",
      ARRAY_FIELD2: ["always_true"],
      ARRAY_FIELD: ["log_message"],
      createdBy: "admin"
    },
    3: {
      STRING_FIELD: "Test Chat",
      STRING_FIELD2: "A test chat with STRING_FIELD2",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    4: {
      STRING_FIELD: "Tagged Chat",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      tags: ["urgent", "customer-service"],
      createdBy: "admin"
    },
    5: {
      STRING_FIELD: "John Doe Chat",
      STRING_FIELD2: "Chat for John Doe processing",
      ARRAY_FIELD2: ["user_STRING_FIELD_contains('john')", "time_between('09:00', '17:00')"],
      ARRAY_FIELD: ["assign_to_support", "send_ARRAY_FIELD"],
      tags: ["Customer", "VIP"],
      createdBy: "admin"
    },
    6: {
      STRING_FIELD: "Jane Smith Chat",
      STRING_FIELD2: "Chat for Jane Smith processing",
      ARRAY_FIELD2: ["user_STRING_FIELD_contains('jane')", "priority_high"],
      ARRAY_FIELD: ["escalate", "notify_manager"],
      tags: ["Customer"],
      createdBy: "admin"
    },
    7: {
      STRING_FIELD: "Bob Johnson Chat",
      STRING_FIELD2: "Chat for Bob Johnson processing",
      ARRAY_FIELD2: ["user_STRING_FIELD_contains('bob')", "vip_customer"],
      ARRAY_FIELD: ["priority_handling", "send_notification"],
      tags: ["VIP", "Premium"],
      createdBy: "admin"
    },
    8: {
      STRING_FIELD: "Alice Brown Chat",
      STRING_FIELD2: "Chat for Alice Brown processing",
      ARRAY_FIELD2: ["user_STRING_FIELD_contains('alice')", "lead_qualification"],
      ARRAY_FIELD: ["assign_to_sales", "track_conversion"],
      tags: ["Premium"],
      createdBy: "admin"
    }
  };

  if (!baseChats[variant]) {
    throw new Error(`Chat variant ${variant} not found. Available variants: ${Object.keys(baseChats).join(', ')}`);
  }

  return { ...baseChats[variant] };
}

// Specialized creator functions for specific test scenarios
export function createMinimalChat(): ChatCreateInput {
  return createChat(2);
}

export function createFullChat(): ChatCreateInput {
  return createChat(1);
}

export function createChatWithDescription(): ChatCreateInput {
  return createChat(3);
}

export function createChatWithTags(): ChatCreateInput {
  return createChat(4);
}

// Creator for multiple chats (useful for bulk operations and search tests)
export function createMultipleChats(): ChatCreateInput[] {
  return [
    createChat(5), // John Doe Chat
    createChat(6), // Jane Smith Chat
    createChat(7), // Bob Johnson Chat
    createChat(8)  // Alice Brown Chat
  ];
}

// Creator for simple test chats (useful for basic CRUD operations)
export function createSimpleChats(): ChatCreateInput[] {
  return [
    { STRING_FIELD: "A", ARRAY_FIELD2: ["1"], ARRAY_FIELD: ["a"], createdBy: "admin" },
    { STRING_FIELD: "B", ARRAY_FIELD2: ["2"], ARRAY_FIELD: ["b"], createdBy: "admin" },
    { STRING_FIELD: "C", ARRAY_FIELD2: ["3"], ARRAY_FIELD: ["c"], createdBy: "admin" }
  ];
}

// Creator for chats with specific tags (useful for filtering tests)
export function createChatsWithTags(): ChatCreateInput[] {
  return [
    { STRING_FIELD: "John Doe", ARRAY_FIELD2: ["x"], ARRAY_FIELD: ["a"], createdBy: "admin", tags: ["Customer", "VIP"] },
    { STRING_FIELD: "Jane Smith", ARRAY_FIELD2: ["y"], ARRAY_FIELD: ["b"], createdBy: "admin", tags: ["Lead", "Potential"] },
    { STRING_FIELD: "Bob Johnson", ARRAY_FIELD2: ["z"], ARRAY_FIELD: ["c"], createdBy: "admin", tags: ["Customer"] },
    { STRING_FIELD: "Alice Brown", ARRAY_FIELD2: ["a"], ARRAY_FIELD: ["d"], createdBy: "admin", tags: ["VIP"] }
  ];
}

// Update data creators
export function createChatUpdate(variant: number): ChatUpdateInput {
  const baseUpdates: Record<number, ChatUpdateInput> = {
    1: {
      STRING_FIELD: "Updated Chat",
      STRING_FIELD2: "Updated STRING_FIELD2",
      ARRAY_FIELD2: ["updated_condition"],
      ARRAY_FIELD: ["updated_action"],
      tags: ["VIP", "Premium"],
      isActive: false,
      updatedBy: "admin"
    },
    2: {
      STRING_FIELD: "New Name",
      updatedBy: "admin"
    },
    3: {
      STRING_FIELD2: "Updated STRING_FIELD2 only",
      updatedBy: "admin"
    },
    4: {
      tags: ["new-tag", "updated-tag"],
      updatedBy: "admin"
    },
    5: {
      isActive: false,
      updatedBy: "admin"
    }
  };

  if (!baseUpdates[variant]) {
    throw new Error(`Chat update variant ${variant} not found. Available variants: ${Object.keys(baseUpdates).join(', ')}`);
  }

  return { ...baseUpdates[variant] };
}

// Specialized update creators
export function createFullChatUpdate(): ChatUpdateInput {
  return createChatUpdate(1);
}

export function createNameOnlyUpdate(): ChatUpdateInput {
  return createChatUpdate(2);
}

export function createDescriptionOnlyUpdate(): ChatUpdateInput {
  return createChatUpdate(3);
}

export function createTagsOnlyUpdate(): ChatUpdateInput {
  return createChatUpdate(4);
}

export function createStatusOnlyUpdate(): ChatUpdateInput {
  return createChatUpdate(5);
}

// Invalid update data creators for validation tests
export function createInvalidUpdate(type: 'empty-STRING_FIELD' | 'empty-ARRAY_FIELD2' | 'empty-ARRAY_FIELD' | 'empty-object'): any {
  const invalidUpdates = {
    'empty-STRING_FIELD': {
      STRING_FIELD: "",
      updatedBy: "admin"
    },
    'empty-ARRAY_FIELD2': {
      ARRAY_FIELD2: [],
      updatedBy: "admin"
    },
    'empty-ARRAY_FIELD': {
      ARRAY_FIELD: [],
      updatedBy: "admin"
    },
    'empty-object': {}
  };

  return invalidUpdates[type];
}

// Update with whitespace for trimming tests
export function createUpdateWithWhitespace(): ChatUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    updatedBy: "admin"
  };
}

// Update for duplicate STRING_FIELD testing
export function createDuplicateNameUpdate(existingName: string): ChatUpdateInput {
  return {
    STRING_FIELD: existingName,
    updatedBy: "admin"
  };
}

// Update with same STRING_FIELD (no change scenario)
export function createSameNameUpdate(): ChatUpdateInput {
  return {
    STRING_FIELD: "Simple Chat", // Same as createMinimalChat
    STRING_FIELD2: "Updated STRING_FIELD2",
    updatedBy: "admin"
  };
}

// Chat for soft delete testing
export function createChatForSoftDelete(): ChatCreateInput {
  return {
    STRING_FIELD: "To Be Deleted",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin"
  };
}

// Update for soft deleted chat testing
export function createUpdateForSoftDeleted(): ChatUpdateInput {
  return {
    STRING_FIELD: "Should Not Work",
    updatedBy: "admin"
  };
}

// Update with whitespace in all fields for comprehensive trimming test
export function createUpdateWithAllFieldsWhitespace(): ChatUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    STRING_FIELD2: "   Trimmed Description   ",
    ARRAY_FIELD2: ["   trimmed_condition   "],
    ARRAY_FIELD: ["   trimmed_action   "],
    tags: ["   tag1   ", "   tag2   "],
    updatedBy: "admin"
  };
}

// Chat for trimming test
export function createChatForTrimming(): ChatCreateInput {
  return {
    STRING_FIELD: "Original Chat",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin"
  };
}

// Chat for active status testing
export function createActiveChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Active Chat",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    isActive: true,
    createdBy: "admin"
  };
}

// Update for status change testing
export function createStatusChangeUpdate(): ChatUpdateInput {
  return {
    isActive: false,
    updatedBy: "admin"
  };
}

// ========================================
// PARAMS CREATORS FOR implHandleGetAllChats
// ========================================

// Search params
export function createSearchByNameParams() {
  return { search: "John" };
}

export function createSearchByDescriptionParams() {
  return { search: "processing" };
}

export function createEmptySearchParams() {
  return { search: "" };
}

export function createWhitespaceSearchParams() {
  return { search: "   " };
}

export function createNonExistentSearchParams() {
  return { search: "NonExistent" };
}

// Filter params
export function createVipTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "VIP" }]
  };
}

export function createCustomerTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "Customer" }]
  };
}

// Pagination params
export function createPaginationParams() {
  return {
    page: 1,
    limit: 2
  };
}

// Sorting params
export function createSortByNameAscParams() {
  return {
    sorts: [{ field: "STRING_FIELD", direction: "asc" as const }]
  };
}

// Combined params
export function createSearchAndTagParams() {
  return {
    search: "John",
    tag: "VIP"
  };
}

// Include deleted params
export function createIncludeDeletedParams() {
  return { includeDeleted: true };
}

// Legacy tag params (converted to filters format)
export function createEmptyTagParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceTagParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

export function createNonExistentTagParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

// Additional search params for read.test.ts
export function createSearchByTagParams() {
  return { search: "VIP" };
}

export function createUnmatchedSearchParams() {
  return { search: "nonexistent" };
}

export function createUndefinedSearchParams() {
  return { search: undefined };
}

// Additional filter params for read.test.ts
export function createNonExistentFilterParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

export function createEmptyFilterFieldParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceFilterFieldParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

// ========================================
// CREATORS FOR DELETE TESTS
// ========================================

// Chat for retry delete testing
export function createRetryDeleteChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Retry Delete",
    ARRAY_FIELD2: ["attempt"],
    ARRAY_FIELD: ["log"],
    createdBy: "admin"
  };
}

// ========================================
// CREATORS FOR SPECIAL CASES (Chat-specific)
// ========================================

// Chat with special characters and unicode
export function createSpecialCharacterChat(): ChatCreateInput {
  return {
    STRING_FIELD: "José María O'Connor",
    STRING_FIELD2: "Handles unicode 🎉 & symbols",
    ARRAY_FIELD2: ["STRING_FIELD.includes('José')"],
    ARRAY_FIELD: ["notify", "log"],
    tags: ["Special", "🚀", "Test@Tag"],
    createdBy: "admin"
  };
}

// Chat with very long ARRAY_FIELD2 (Chat-specific test)
export function createLongContentChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Very Long Chat Name That Exceeds Normal Length Expectations And Tests System Limits",
    STRING_FIELD2: "This is a very long STRING_FIELD2 that tests how the system handles extensive text ARRAY_FIELD2 in chat STRING_FIELD2s. It includes multiple sentences and should test the limits of what the system can handle in terms of ARRAY_FIELD2 length and processing.",
    ARRAY_FIELD2: [
      "user.message.length > 1000",
      "user.message.includes('very long query with lots of details')",
      "user.session.duration > 3600"
    ],
    ARRAY_FIELD: [
      "log_extensive_details",
      "notify_admin_of_long_interaction",
      "create_detailed_report",
      "escalate_to_specialist"
    ],
    tags: ["LongContent", "EdgeCase", "SystemLimits", "Performance"],
    createdBy: "admin"
  };
}

// Chat with edge case ARRAY_FIELD2 (Chat-specific)
export function createEdgeCaseConditionsChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Edge Case Conditions",
    STRING_FIELD2: "Tests complex condition parsing",
    ARRAY_FIELD2: [
      "user.age >= 18 && user.age <= 65",
      "user.location.country === 'US' || user.location.country === 'CA'",
      "user.preferences.notifications === true"
    ],
    ARRAY_FIELD: [
      "apply_regional_chats",
      "send_age_appropriate_ARRAY_FIELD2"
    ],
    tags: ["EdgeCase", "Complex"],
    createdBy: "admin"
  };
}

// Chat with complex ARRAY_FIELD (Chat-specific)
export function createComplexActionsChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Complex Actions Chat",
    STRING_FIELD2: "Tests complex action execution",
    ARRAY_FIELD2: ["trigger_complex_workflow"],
    ARRAY_FIELD: [
      "webhook.call('https://api.example.com/notify')",
      "database.update('user_stats', {last_interaction: now()})",
      "ARRAY_FIELD.send(template='complex_notification', to=user.ARRAY_FIELD)",
      "analytics.track('complex_chat_triggered', {chat_id: this.id})"
    ],
    tags: ["Complex", "Integration"],
    createdBy: "admin"
  };
}

// Chat with empty optional fields (Chat-specific edge case)
export function createEmptyOptionalFieldsChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Empty Optional Fields",
    ARRAY_FIELD2: ["basic_condition"],
    ARRAY_FIELD: ["basic_action"],
    STRING_FIELD2: "",
    tags: [],
    createdBy: "admin"
  };
}

// Chat for testing AI-specific business logic
export function createAiLogicChat(): ChatCreateInput {
  return {
    STRING_FIELD: "AI Decision Chat",
    STRING_FIELD2: "Tests AI-specific decision making logic",
    ARRAY_FIELD2: [
      "ai.confidence > 0.8",
      "ai.model === 'gpt-4'",
      "ai.context.length > 100"
    ],
    ARRAY_FIELD: [
      "ai.respond_with_confidence",
      "ai.log_decision_path",
      "ai.update_learning_model"
    ],
    tags: ["AI", "MachineLearning", "Confidence"],
    createdBy: "admin"
  };
}

// Creators for delete test scenarios
export function createComplexChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Complex Chat",
    STRING_FIELD2: "Full field test",
    ARRAY_FIELD2: ["user.role == 'admin'"],
    ARRAY_FIELD: ["grant_access", "log_activity"],
    tags: ["admin", "security"],
    isActive: true,
    createdBy: "admin"
  };
}

export function createMinimalDeleteChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Minimal Chat",
    ARRAY_FIELD2: ["is.loggedIn"],
    ARRAY_FIELD: ["alert"],
    createdBy: "admin"
  };
}

// Chats for testing deletion effects on other chats
export function createChatsForDeletionTest(): ChatCreateInput[] {
  return [
    { STRING_FIELD: "Keep This One", ARRAY_FIELD2: ["x"], ARRAY_FIELD: ["a"], createdBy: "admin" },
    { STRING_FIELD: "Delete This One", ARRAY_FIELD2: ["y"], ARRAY_FIELD: ["b"], createdBy: "admin" },
    { STRING_FIELD: "Keep This Too", ARRAY_FIELD2: ["z"], ARRAY_FIELD: ["c"], createdBy: "admin" }
  ];
}

// Creators for bulk operations testing
export function createExistingChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Existing Chat",
    STRING_FIELD2: "An existing chat",
    ARRAY_FIELD2: ["User says test"],
    ARRAY_FIELD: ["Show test response"],
    createdBy: "admin"
  };
}

export function createDuplicateChatsForBulk(): ChatCreateInput[] {
  return [
    {
      STRING_FIELD: "Existing Chat", // Duplicate STRING_FIELD
      STRING_FIELD2: "Another chat with same STRING_FIELD",
      ARRAY_FIELD2: ["User says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "New Chat",
      STRING_FIELD2: "A new chat",
      ARRAY_FIELD2: ["User says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// Chats for bulk update testing
export function createChatsForBulkUpdate(): ChatCreateInput[] {
  return [
    {
      STRING_FIELD: "Chat 1",
      STRING_FIELD2: "First chat",
      ARRAY_FIELD2: ["User says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "Chat 2",
      STRING_FIELD2: "Second chat",
      ARRAY_FIELD2: ["User says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// Bulk update data
export function createBulkUpdateData(): any[] {
  return [
    {
      STRING_FIELD: "Updated Chat 1",
      STRING_FIELD2: "Updated first chat",
      updatedBy: "admin"
    },
    {
      STRING_FIELD: "Updated Chat 2",
      STRING_FIELD2: "Updated second chat",
      updatedBy: "admin"
    }
  ];
}

// Chats for bulk delete testing
export function createChatsForBulkDelete(): ChatCreateInput[] {
  return [
    {
      STRING_FIELD: "Chat 1",
      STRING_FIELD2: "First chat",
      ARRAY_FIELD2: ["User says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "Chat 2",
      STRING_FIELD2: "Second chat",
      ARRAY_FIELD2: ["User says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "Chat 3",
      STRING_FIELD2: "Third chat",
      ARRAY_FIELD2: ["User asks question"],
      ARRAY_FIELD: ["Show help"],
      createdBy: "admin"
    }
  ];
}

// Invalid data creators for validation tests
export function createInvalidChat(type: 'missing-STRING_FIELD' | 'missing-ARRAY_FIELD2' | 'missing-ARRAY_FIELD' | 'empty-ARRAY_FIELD2' | 'empty-ARRAY_FIELD' | 'missing-ARRAY_FIELD2'): any {
  const invalidChats = {
    'missing-ARRAY_FIELD2': {
      STRING_FIELD: "John Doe"
    },
    'missing-STRING_FIELD': {
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'missing-ARRAY_FIELD2': {
      STRING_FIELD: "Invalid Chat",
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'missing-ARRAY_FIELD': {
      STRING_FIELD: "Invalid Chat",
      ARRAY_FIELD2: ["test_condition"],
      createdBy: "admin"
    },
    'empty-ARRAY_FIELD2': {
      STRING_FIELD: "Invalid Chat",
      ARRAY_FIELD2: [],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'empty-ARRAY_FIELD': {
      STRING_FIELD: "Invalid Chat",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: [],
      createdBy: "admin"
    }
  };

  return invalidChats[type];
}

// Creator for chats with special characteristics
export function createChatWithWhitespace(): ChatCreateInput {
  return {
    STRING_FIELD: "  Trimmed Chat  ",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin"
  };
}

export function createChatWithManyTags(): ChatCreateInput {
  return {
    STRING_FIELD: "Multi-tag Chat",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    createdBy: "admin"
  };
}

export function createChatWithoutDescription(): ChatCreateInput {
  return {
    STRING_FIELD: "Chat without STRING_FIELD2",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin"
  };
}

export function createChatWithEmptyTags(): ChatCreateInput {
  return {
    STRING_FIELD: "Chat with empty tags",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: [],
    createdBy: "admin"
  };
}

// Duplicate chat creator for conflict testing
export function createDuplicateChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Duplicate Chat",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin"
  };
}

export function createSecondDuplicateChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Duplicate Chat", // Same STRING_FIELD as above
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin"
  };
}

// Test chat with specific STRING_FIELD for soft delete tests
export function createTestChat(): ChatCreateInput {
  return {
    STRING_FIELD: "Test Chat",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin"
  };
}

export function createTestChat2(): ChatCreateInput {
  return {
    STRING_FIELD: "Test Chat",
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin"
  };
}
