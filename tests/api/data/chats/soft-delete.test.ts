//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatBusinessLogicInterface } from "@/lib/repositories/chats/interface";
import { ChatBusinessLogic } from "@/lib/repositories/chats/BusinessLogic";
import { MongoChatRepository } from "@/lib/repositories/chats/MongoRepository";
import { TestChatDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateChat,
  implHandleGetChat,
  implHandleDeleteChat,
  implHandleUpdateChat,
  implHandleGetAllChats,
  implHandleRestoreChat
} from "@/app/api/v1/chats/impl";
import { createChat, createChatUpdate, createChatWithDescription, createTestChat, createTestChat2 } from "./object_creator";

describe("Chat Soft Delete Tests", () => {
  let businessLogic: ChatBusinessLogicInterface;
    let dbRepository: TestChatDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("Chat");
      await driver.connect()
      const originalDb = new MongoChatRepository(driver);
      dbRepository = new TestChatDBRepositoryWrapper(originalDb, driver);
      businessLogic = new ChatBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("Soft Delete", () => {
    it("should soft delete a chats by default", async () => {
      const chatData = createChatWithDescription();
      const createResult = await implHandleCreateChat(chatData, businessLogic);
      expect(createResult.status).toBe(201);

      const deleteResult = await implHandleDeleteChat(createResult.body.data.id, businessLogic);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");

      // Chat should not be accessible by default
      const getResult = await implHandleGetChat(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);

      // But should be accessible when including deleted
      const getDeletedResult = await implHandleGetChat(createResult.body.data.id, businessLogic, true);
      expect(getDeletedResult.status).toBe(200);
      expect(getDeletedResult.body.data).not.toBeNull();

      // Count should exclude soft deleted
      expect(await dbRepository.getChatCount()).toBe(0);
      expect(await dbRepository.getChatCount(true)).toBe(1);
    });

    it("should hard delete when specified", async () => {
      const chatData = createChatWithDescription();
      const createResult = await implHandleCreateChat(chatData, businessLogic);
      expect(createResult.status).toBe(201);

      // Hard delete using impl function
      const deleteResult = await implHandleDeleteChat(createResult.body.data.id, businessLogic, true);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");

      // Chat should not be accessible at all
      const getResult = await implHandleGetChat(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);
      const getDeletedResult = await implHandleGetChat(createResult.body.data.id, businessLogic, true);
      expect(getDeletedResult.status).toBe(404);

      // Count should be 0 in both cases
      expect(await dbRepository.getChatCount()).toBe(0);
      expect(await dbRepository.getChatCount(true)).toBe(0);
    });

    it("should not include soft deleted chats in getAll by default", async () => {
      const chatData1 = createChat(1);
      const chatData2 = createChat(2);

      const createResult1 = await implHandleCreateChat(chatData1, businessLogic);
      const createResult2 = await implHandleCreateChat(chatData2, businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      // Soft delete one chats
      const deleteResult = await implHandleDeleteChat(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const result = await implHandleGetAllChats(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.items).toHaveLength(1);
      expect(result.body.data?.total).toBe(1);
      expect(result.body.data?.items[0].id).toBe(createResult2.body.data.id);
    });

    it("should include soft deleted chats when specified", async () => {
      const chatData1 = createChat(1);
      const chatData2 = createChat(2);

      const createResult1 = await implHandleCreateChat(chatData1, businessLogic);
      const createResult2 = await implHandleCreateChat(chatData2, businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      // Soft delete one chats
      const deleteResult = await implHandleDeleteChat(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const result = await implHandleGetAllChats(businessLogic, { includeDeleted: true });

      expect(result.status).toBe(200);
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(2);

      const deletedChat = result.body.data?.items.find((c: any) => c.id === createResult1.body.data.id);
      expect(deletedChat).toBeDefined();
      expect(deletedChat?.deletedAt).toBeDefined();
    });

    it("should not allow updating soft deleted chats", async () => {
      const chatData = createChat(3);
      const createResult = await implHandleCreateChat(chatData, businessLogic);
      expect(createResult.status).toBe(201);

      const deleteResult = await implHandleDeleteChat(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const chatUpdate = createChatUpdate(1)

      const result = await implHandleUpdateChat(createResult.body.data.id, chatUpdate, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should not include soft deleted chats in search", async () => {
      const chatData1 = createChat(3); // "Test Chat"
      const createResult = await implHandleCreateChat(chatData1, businessLogic);
      expect(createResult.status).toBe(201);

      // Soft delete the chat
      const deleteResult = await implHandleDeleteChat(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Search should not find the soft deleted chat
      const searchResult = await implHandleGetAllChats(businessLogic, { search: "Test" });
      expect(searchResult.status).toBe(200);
      expect(searchResult.body.data).toHaveLength(0);
    });
  });

  describe("Restore", () => {
    it("should restore a soft deleted chats", async () => {
      const chatData = createChatWithDescription();
      const createResult = await implHandleCreateChat(chatData, businessLogic);
      expect(createResult.status).toBe(201);

      // Soft delete the chats
      const deleteResult = await implHandleDeleteChat(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const getResult = await implHandleGetChat(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);

      // Restore the chats
      const restoreResult = await implHandleRestoreChat(createResult.body.data.id, businessLogic);

      expect(restoreResult.status).toBe(200);
      expect(restoreResult.body.status).toBe("success");

      // Chat should be accessible again
      const restoredResult = await implHandleGetChat(createResult.body.data.id, businessLogic);
      expect(restoredResult.status).toBe(200);
      expect(restoredResult.body.data?.deletedAt).toBeUndefined();

      // Count should include the restored chats
      expect(await dbRepository.getChatCount()).toBe(1);
    });

    it("should fail to restore a non-existent chats", async () => {
      const restoreResult = await implHandleRestoreChat("507f1f77bcf86cd799439011", businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail to restore a chats that was never deleted", async () => {
      const chatData = createChat(3);
      const createResult = await implHandleCreateChat(chatData, businessLogic);
      expect(createResult.status).toBe(201);

      const restoreResult = await implHandleRestoreChat(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail to restore a hard deleted chats", async () => {
      const chatData = createChat(3);
      const createResult = await implHandleCreateChat(chatData, businessLogic);
      expect(createResult.status).toBe(201);

      // Hard delete the chats
      const deleteResult = await implHandleDeleteChat(createResult.body.data.id, businessLogic, true);
      expect(deleteResult.status).toBe(200);

      const restoreResult = await implHandleRestoreChat(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail with empty chats ID", async () => {
      const restoreResult = await implHandleRestoreChat("", businessLogic);
      expect(restoreResult.status).toBe(400);
      expect(restoreResult.body.status).toBe("failed");
      expect(restoreResult.body.error).toContain("Chat ID is required");
    });

    it("should fail with whitespace-only chats ID", async () => {
      const restoreResult = await implHandleRestoreChat("   ", businessLogic);
      expect(restoreResult.status).toBe(400);
      expect(restoreResult.body.status).toBe("failed");
      expect(restoreResult.body.error).toContain("Chat ID is required");
    });

    it("should update updatedAt when restoring", async () => {
      const chatData = createChat(3);
      const createResult = await implHandleCreateChat(chatData, businessLogic);
      expect(createResult.status).toBe(201);

      const originalUpdatedAt = createResult.body.data.updatedAt;

      // Wait a bit to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      // Soft delete and restore
      const deleteResult = await implHandleDeleteChat(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const restoreResult = await implHandleRestoreChat(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(200);

      const getResult = await implHandleGetChat(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(200);
      expect(getResult.body.data?.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating chats with STRING_FIELD of soft deleted chats", async () => {
      // Create and soft delete a chats
      const chatData1 = createTestChat();
      const createResult1 = await implHandleCreateChat(chatData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const deleteResult = await implHandleDeleteChat(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Should be able to create new chats with same STRING_FIELD
      const chatData2 = createTestChat2();
      const createResult2 = await implHandleCreateChat(chatData2, businessLogic);

      expect(createResult2.status).toBe(201);
      expect(createResult2.body.data.STRING_FIELD).toBe(chatData2.STRING_FIELD);
      expect(await dbRepository.getChatCount()).toBe(1);
    });

    it("should prevent creating chats with STRING_FIELD of active chats", async () => {
      const chatData1 = createTestChat();
      const createResult1 = await implHandleCreateChat(chatData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const chatData2 = createTestChat2();
      const createResult2 = await implHandleCreateChat(chatData2, businessLogic);

      expect(createResult2.status).toBe(409);
      expect(createResult2.body.status).toBe("failed");
      expect(createResult2.body.error).toContain("Chat with the same STRING_FIELD already exists");
    });

    it("should prevent restoring chats if STRING_FIELD is now taken", async () => {
      // Create and soft delete a chats
      const chatData1 = createTestChat();
      const createResult1 = await implHandleCreateChat(chatData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const deleteResult = await implHandleDeleteChat(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Create new chats with same STRING_FIELD
      const chatData2 = createTestChat2();
      const createResult2 = await implHandleCreateChat(chatData2, businessLogic);
      expect(createResult2.status).toBe(201);

      // Should not be able to restore the first chats
      const restoreResult = await implHandleRestoreChat(createResult1.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });
  });
});
