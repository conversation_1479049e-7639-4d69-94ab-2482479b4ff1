// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatBusinessLogicInterface } from "@/lib/repositories/chats/interface";
import { ChatBusinessLogic } from "@/lib/repositories/chats/BusinessLogic";
import { MongoChatRepository } from "@/lib/repositories/chats/MongoRepository";
import { TestChatDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChat, implHandleGetChat, implHandleGetAllChats } from "@/app/api/v1/chats/impl";
import {
  createChat,
  createSimpleChats,
  createChatsWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams
} from "./object_creator";

describe("Read Chat API Tests", () => {
  let businessLogic: ChatBusinessLogicInterface;
  let dbRepository: TestChatDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Chat");
    await driver.connect();
    const originalDb = new MongoChatRepository(driver);
    dbRepository = new TestChatDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("GET /api/v1/chats/:id", () => {
    it("should successfully get chat by ID", async () => {
      const chat = createChat(5); // John Doe Chat

      const createResult = await implHandleCreateChat(chat, businessLogic);
      const id = createResult.body.data.id;

      const result = await implHandleGetChat(id, businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.id).toBe(id);
      expect(result.body.data?.STRING_FIELD).toBe(chat.STRING_FIELD);
    });

    it("should fail to get non-existent chat", async () => {
      const result = await implHandleGetChat("507f1f77bcf86cd799439011", businessLogic);
      expect(result.status).toBe(404);
    });

    it("should fail with empty chat ID", async () => {
      const result = await implHandleGetChat("", businessLogic);
      expect(result.status).toBe(400);
    });

    it("should fail with whitespace-only chat ID", async () => {
      const result = await implHandleGetChat("   ", businessLogic);
      expect(result.status).toBe(400);
    });
  });

  describe("GET /api/v1/chats", () => {
    it("should get all chats", async () => {
      const chats = createSimpleChats();
      for (const r of chats) await implHandleCreateChat(r, businessLogic);

      const result = await implHandleGetAllChats(businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(3);
    });

    it("should return empty when no chats exist", async () => {
      const result = await implHandleGetAllChats(businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });
  });

  describe("GET /api/v1/chats/search", () => {
    beforeEach(async () => {
      const data = createChatsWithTags();
      for (const r of data) await implHandleCreateChat(r, businessLogic);
    });

    it("should search by STRING_FIELD", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should search by tag", async () => {
      const params = createSearchByTagParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(4);
    });
  });

  describe("GET /api/v1/chats/filters", () => {
    beforeEach(async () => {
      const data = createChatsWithTags();
      for (const r of data) await implHandleCreateChat(r, businessLogic);
    });

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams();
      const result = await implHandleGetAllChats(businessLogic, params);
      expect(result.status).toBe(400);
    });
  });
});
