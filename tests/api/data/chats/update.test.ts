//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatBusinessLogicInterface } from "@/lib/repositories/chats/interface";
import { ChatBusinessLogic } from "@/lib/repositories/chats/BusinessLogic";
import { MongoChatRepository } from "@/lib/repositories/chats/MongoRepository";
import { TestChatDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChat, implHandleUpdateChat, implHandleDeleteChat } from "@/app/api/v1/chats/impl";
import {
  createFullChat,
  createMinimalChat,
  createFullChatUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createChatForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createChatForTrimming,
  createActiveChat,
  createStatusChangeUpdate
} from "./object_creator";

describe("Update Chat API Tests", () => {
  let businessLogic: ChatBusinessLogicInterface;
  let dbRepository: TestChatDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Chat");
    await driver.connect();
    const originalDb = new MongoChatRepository(driver);
    dbRepository = new TestChatDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("PUT /api/v1/chats/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullChat();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      const updateData = createFullChatUpdate();
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2);
      expect(result.body.data?.ARRAY_FIELD).toEqual(updateData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(updateData.variables);
      expect(result.body.data?.tags).toEqual(updateData.tags);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should update only the STRING_FIELD", async () => {
      const createData = createMinimalChat();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD).toEqual(createData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(createData.variables);
    });

    it("should trim STRING_FIELD when updating", async () => {
      const createData = createMinimalChat();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      const updateData = createUpdateWithWhitespace();
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name");
    });

    it("should fail to update non-existent chat", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateChat("507f1f77bcf86cd799439011", updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Chat not found");
    });

    it("should fail with invalid input: empty STRING_FIELD", async () => {
      const createData = createMinimalChat();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-STRING_FIELD');
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with invalid input: empty ARRAY_FIELD", async () => {
      const createData = createMinimalChat();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-ARRAY_FIELD');
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty update object", async () => {
      const createData = createMinimalChat();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-object');
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("No data provided for update");
    });

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateChat("", updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Chat ID is required");
    });

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first chat
      const createData1 = createMinimalChat();
      await implHandleCreateChat(createData1, businessLogic);

      // Create second chat
      const createData2 = createFullChat();
      const createResult2 = await implHandleCreateChat(createData2, businessLogic);

      // Try to update second chat with first chat's STRING_FIELD
      const updateData = createDuplicateNameUpdate(createData1.STRING_FIELD);
      const result = await implHandleUpdateChat(createResult2.body.data.id, updateData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Another Chat with this STRING_FIELD exists");
    });

    it("should allow updating chat with same STRING_FIELD (no change)", async () => {
      const createData = createMinimalChat();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      const updateData = createSameNameUpdate();
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2);
    });

    it("should fail to update soft-deleted chat", async () => {
      const createData = createChatForSoftDelete();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      // Soft delete the chat
      const deleteResult = await implHandleDeleteChat(chatsId, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Try to update the soft-deleted chat
      const updateData = createUpdateForSoftDeleted();
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Chat not found");
    });

    it("should trim all string fields when updating", async () => {
      const createData = createChatForTrimming();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      const updateData = createUpdateWithAllFieldsWhitespace();
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name");
      expect(result.body.data?.ARRAY_FIELD2).toBe("Trimmed Description");
      expect(result.body.data?.ARRAY_FIELD).toEqual(["trimmed_condition"]);
      expect(result.body.data?.variables).toEqual(["trimmed_action"]);
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"]);
    });

    it("should fail with invalid input: empty variables", async () => {
      const createData = createMinimalChat();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-variables');
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should update isActive status", async () => {
      const createData = createActiveChat();
      const createResult = await implHandleCreateChat(createData, businessLogic);
      const chatsId = createResult.body.data.id;

      const updateData = createStatusChangeUpdate();
      const result = await implHandleUpdateChat(chatsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
    });
  });
});
