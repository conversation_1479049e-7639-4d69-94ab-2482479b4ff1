//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatBusinessLogicInterface } from "@/lib/repositories/chats/interface";
import { ChatBusinessLogic } from "@/lib/repositories/chats/BusinessLogic";
import { MongoChatRepository } from "@/lib/repositories/chats/MongoRepository";
import { TestChatDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateChat,
  implHandleGetChat,
  implHandleBulkCreateChats,
  implHandleBulkUpdateChats,
  implHandleBulkDeleteChats
} from "@/app/api/v1/chats/impl";
import {
  createMultipleChats,
  createSimpleChats,
  createExistingChat,
  createDuplicateChatsForBulk,
  createChatsForBulkUpdate,
  createBulkUpdateData,
  createChatsForBulkDelete
} from "./object_creator";

describe("Chat Bulk Operations Tests", () => {
  let businessLogic: ChatBusinessLogicInterface;
  let dbRepository: TestChatDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Chat");
    await driver.connect()
    const originalDb = new MongoChatRepository(driver);
    dbRepository = new TestChatDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("Bulk Create", () => {
    it("should successfully create multiple chats", async () => {
      const chatsData = createMultipleChats();

      const result = await implHandleBulkCreateChats(chatsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(chatsData.length);
      expect(result.body.data[0].STRING_FIELD).toBe(chatsData[0].STRING_FIELD);
      expect(result.body.data[1].STRING_FIELD).toBe(chatsData[1].STRING_FIELD);
      expect(result.body.data[2].STRING_FIELD).toBe(chatsData[2].STRING_FIELD);
      expect(await dbRepository.getChatCount()).toBe(chatsData.length);
    });

    it("should fail if any chat has duplicate STRING_FIELD", async () => {
      const existingChat = createExistingChat();
      const createResult = await implHandleCreateChat(existingChat, businessLogic);
      expect(createResult.status).toBe(201);

      const chatsData = createDuplicateChatsForBulk();

      const result = await implHandleBulkCreateChats(chatsData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate STRING_FIELD found: Existing Chat");
      expect(await dbRepository.getChatCount()).toBe(1);
    });

    it("should handle simple chats creation", async () => {
      const chatsData = createSimpleChats();

      const result = await implHandleBulkCreateChats(chatsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(chatsData.length);
      expect(await dbRepository.getChatCount()).toBe(chatsData.length);
    });
  });

  describe("Bulk Update", () => {
    it("should successfully update multiple chats", async () => {
      const chatsData = createChatsForBulkUpdate();
      const createResult1 = await implHandleCreateChat(chatsData[0], businessLogic);
      const createResult2 = await implHandleCreateChat(chatsData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult1.body.data.id, data: updateData[0] },
        { id: createResult2.body.data.id, data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateChats(updates, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.updatedCount).toBe(2);

      const getResult1 = await implHandleGetChat(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetChat(createResult2.body.data.id, businessLogic);

      expect(getResult1.body.data?.STRING_FIELD).toBe(updateData[0].STRING_FIELD);
      expect(getResult1.body.data?.updatedBy).toBe(updateData[0].updatedBy);
      expect(getResult2.body.data?.STRING_FIELD).toBe(updateData[1].STRING_FIELD);
      expect(getResult2.body.data?.updatedBy).toBe(updateData[1].updatedBy);
    });

    it("should fail if any chat doesn't exist", async () => {
      const chatData = createChatsForBulkUpdate()[0];
      const createResult = await implHandleCreateChat(chatData, businessLogic);
      expect(createResult.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult.body.data.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateChats(updates, businessLogic);

      expect(result.status).toBe(500);
      expect(result.body.status).toBe("failed");
    });

    it("should fail if any update would create duplicate STRING_FIELD", async () => {
      const chatsData = createChatsForBulkUpdate();
      const createResult1 = await implHandleCreateChat(chatsData[0], businessLogic);
      const createResult2 = await implHandleCreateChat(chatsData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updates = [
        {
          id: createResult2.body.data.id,
          data: { STRING_FIELD: chatsData[0].STRING_FIELD, updatedBy: "admin" }, // Try to update second chat with first chat's STRING_FIELD
        },
      ];

      const result = await implHandleBulkUpdateChats(updates, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate STRING_FIELD in update: Chat 1");
    });
  });

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple chats", async () => {
      const chatsData = createChatsForBulkDelete();
      const createResult1 = await implHandleCreateChat(chatsData[0], businessLogic);
      const createResult2 = await implHandleCreateChat(chatsData[1], businessLogic);
      const createResult3 = await implHandleCreateChat(chatsData[2], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);
      expect(createResult3.status).toBe(201);

      const result = await implHandleBulkDeleteChats([createResult1.body.data.id, createResult2.body.data.id], businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getChatCount()).toBe(1); // Only non-deleted

      const getResult1 = await implHandleGetChat(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetChat(createResult2.body.data.id, businessLogic);
      const getResult3 = await implHandleGetChat(createResult3.body.data.id, businessLogic);

      expect(getResult1.status).toBe(404);
      expect(getResult2.status).toBe(404);
      expect(getResult3.status).toBe(200);
    });

    it("should successfully hard delete multiple chats", async () => {
      const chatsData = createChatsForBulkDelete();
      const createResult1 = await implHandleCreateChat(chatsData[0], businessLogic);
      const createResult2 = await implHandleCreateChat(chatsData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const result = await implHandleBulkDeleteChats([createResult1.body.data.id, createResult2.body.data.id], businessLogic, true);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getChatCount()).toBe(0);
    });

    it("should fail if any chat doesn't exist", async () => {
      const chatData = createChatsForBulkDelete()[0];
      const createResult = await implHandleCreateChat(chatData, businessLogic);
      expect(createResult.status).toBe(201);

      const result = await implHandleBulkDeleteChats([createResult.body.data.id, "non-existent-id"], businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty chat IDs", async () => {
      const result = await implHandleBulkDeleteChats(["", "valid-id"], businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ID at index 0 is required");
    });
  });
});
