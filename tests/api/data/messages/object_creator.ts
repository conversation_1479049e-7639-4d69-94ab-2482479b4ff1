import { ChatMessageCreateInput, ChatMessageUpdateInput } from "@/lib/repositories/chatMessages/interface";

/**
 * Factory functions for creating test ChatMessage objects
 * This allows for consistent test data across all test files
 * and easy modification of test objects in one place
 */

// Base creator functions for different scenarios
export function createChatMessage(variant: number): ChatMessageCreateInput {
  const baseChatMessages: Record<number, ChatMessageCreateInput> = {
    1: {
      STRING_FIELD: "Customer Support ChatMessage",
      STRING_FIELD2: "ChatMessage for handling customer support requests",
      ARRAY_FIELD2: ["user_message_contains('help')", "time_between('09:00', '17:00')"],
      ARRAY_FIELD: ["assign_to_support", "send_acknowledgment"],
      tags: ["Customer", "VIP"],
      isActive: true,
      createdBy: "admin"
    },
    2: {
      STRING_FIELD: "Simple ChatMessage",
      ARRAY_FIELD2: ["always_true"],
      ARRAY_FIELD: ["log_message"],
      createdBy: "admin"
    },
    3: {
      STRING_FIELD: "Test ChatMessage",
      STRING_FIELD2: "A test chatMessage with STRING_FIELD2",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    4: {
      STRING_FIELD: "Tagged ChatMessage",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      tags: ["urgent", "customer-service"],
      createdBy: "admin"
    },
    5: {
      STRING_FIELD: "John Doe ChatMessage",
      STRING_FIELD2: "ChatMessage for John Doe processing",
      ARRAY_FIELD2: ["user_STRING_FIELD_contains('john')", "time_between('09:00', '17:00')"],
      ARRAY_FIELD: ["assign_to_support", "send_ARRAY_FIELD"],
      tags: ["Customer", "VIP"],
      createdBy: "admin"
    },
    6: {
      STRING_FIELD: "Jane Smith ChatMessage",
      STRING_FIELD2: "ChatMessage for Jane Smith processing",
      ARRAY_FIELD2: ["user_STRING_FIELD_contains('jane')", "priority_high"],
      ARRAY_FIELD: ["escalate", "notify_manager"],
      tags: ["Customer"],
      createdBy: "admin"
    },
    7: {
      STRING_FIELD: "Bob Johnson ChatMessage",
      STRING_FIELD2: "ChatMessage for Bob Johnson processing",
      ARRAY_FIELD2: ["user_STRING_FIELD_contains('bob')", "vip_customer"],
      ARRAY_FIELD: ["priority_handling", "send_notification"],
      tags: ["VIP", "Premium"],
      createdBy: "admin"
    },
    8: {
      STRING_FIELD: "Alice Brown ChatMessage",
      STRING_FIELD2: "ChatMessage for Alice Brown processing",
      ARRAY_FIELD2: ["user_STRING_FIELD_contains('alice')", "lead_qualification"],
      ARRAY_FIELD: ["assign_to_sales", "track_conversion"],
      tags: ["Premium"],
      createdBy: "admin"
    }
  };

  if (!baseChatMessages[variant]) {
    throw new Error(`ChatMessage variant ${variant} not found. Available variants: ${Object.keys(baseChatMessages).join(', ')}`);
  }

  return { ...baseChatMessages[variant] };
}

// Specialized creator functions for specific test scenarios
export function createMinimalChatMessage(): ChatMessageCreateInput {
  return createChatMessage(2);
}

export function createFullChatMessage(): ChatMessageCreateInput {
  return createChatMessage(1);
}

export function createChatMessageWithDescription(): ChatMessageCreateInput {
  return createChatMessage(3);
}

export function createChatMessageWithTags(): ChatMessageCreateInput {
  return createChatMessage(4);
}

// Creator for multiple chatMessages (useful for bulk operations and search tests)
export function createMultipleChatMessages(): ChatMessageCreateInput[] {
  return [
    createChatMessage(5), // John Doe ChatMessage
    createChatMessage(6), // Jane Smith ChatMessage
    createChatMessage(7), // Bob Johnson ChatMessage
    createChatMessage(8)  // Alice Brown ChatMessage
  ];
}

// Creator for simple test chatMessages (useful for basic CRUD operations)
export function createSimpleChatMessages(): ChatMessageCreateInput[] {
  return [
    { STRING_FIELD: "A", ARRAY_FIELD2: ["1"], ARRAY_FIELD: ["a"], createdBy: "admin" },
    { STRING_FIELD: "B", ARRAY_FIELD2: ["2"], ARRAY_FIELD: ["b"], createdBy: "admin" },
    { STRING_FIELD: "C", ARRAY_FIELD2: ["3"], ARRAY_FIELD: ["c"], createdBy: "admin" }
  ];
}

// Creator for chatMessages with specific tags (useful for filtering tests)
export function createChatMessagesWithTags(): ChatMessageCreateInput[] {
  return [
    { STRING_FIELD: "John Doe", ARRAY_FIELD2: ["x"], ARRAY_FIELD: ["a"], createdBy: "admin", tags: ["Customer", "VIP"] },
    { STRING_FIELD: "Jane Smith", ARRAY_FIELD2: ["y"], ARRAY_FIELD: ["b"], createdBy: "admin", tags: ["Lead", "Potential"] },
    { STRING_FIELD: "Bob Johnson", ARRAY_FIELD2: ["z"], ARRAY_FIELD: ["c"], createdBy: "admin", tags: ["Customer"] },
    { STRING_FIELD: "Alice Brown", ARRAY_FIELD2: ["a"], ARRAY_FIELD: ["d"], createdBy: "admin", tags: ["VIP"] }
  ];
}

// Update data creators
export function createChatMessageUpdate(variant: number): ChatMessageUpdateInput {
  const baseUpdates: Record<number, ChatMessageUpdateInput> = {
    1: {
      STRING_FIELD: "Updated ChatMessage",
      STRING_FIELD2: "Updated STRING_FIELD2",
      ARRAY_FIELD2: ["updated_condition"],
      ARRAY_FIELD: ["updated_action"],
      tags: ["VIP", "Premium"],
      isActive: false,
      updatedBy: "admin"
    },
    2: {
      STRING_FIELD: "New Name",
      updatedBy: "admin"
    },
    3: {
      STRING_FIELD2: "Updated STRING_FIELD2 only",
      updatedBy: "admin"
    },
    4: {
      tags: ["new-tag", "updated-tag"],
      updatedBy: "admin"
    },
    5: {
      isActive: false,
      updatedBy: "admin"
    }
  };

  if (!baseUpdates[variant]) {
    throw new Error(`ChatMessage update variant ${variant} not found. Available variants: ${Object.keys(baseUpdates).join(', ')}`);
  }

  return { ...baseUpdates[variant] };
}

// Specialized update creators
export function createFullChatMessageUpdate(): ChatMessageUpdateInput {
  return createChatMessageUpdate(1);
}

export function createNameOnlyUpdate(): ChatMessageUpdateInput {
  return createChatMessageUpdate(2);
}

export function createDescriptionOnlyUpdate(): ChatMessageUpdateInput {
  return createChatMessageUpdate(3);
}

export function createTagsOnlyUpdate(): ChatMessageUpdateInput {
  return createChatMessageUpdate(4);
}

export function createStatusOnlyUpdate(): ChatMessageUpdateInput {
  return createChatMessageUpdate(5);
}

// Invalid update data creators for validation tests
export function createInvalidUpdate(type: 'empty-STRING_FIELD' | 'empty-ARRAY_FIELD2' | 'empty-ARRAY_FIELD' | 'empty-object'): any {
  const invalidUpdates = {
    'empty-STRING_FIELD': {
      STRING_FIELD: "",
      updatedBy: "admin"
    },
    'empty-ARRAY_FIELD2': {
      ARRAY_FIELD2: [],
      updatedBy: "admin"
    },
    'empty-ARRAY_FIELD': {
      ARRAY_FIELD: [],
      updatedBy: "admin"
    },
    'empty-object': {}
  };

  return invalidUpdates[type];
}

// Update with whitespace for trimming tests
export function createUpdateWithWhitespace(): ChatMessageUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    updatedBy: "admin"
  };
}

// Update for duplicate STRING_FIELD testing
export function createDuplicateNameUpdate(existingName: string): ChatMessageUpdateInput {
  return {
    STRING_FIELD: existingName,
    updatedBy: "admin"
  };
}

// Update with same STRING_FIELD (no change scenario)
export function createSameNameUpdate(): ChatMessageUpdateInput {
  return {
    STRING_FIELD: "Simple ChatMessage", // Same as createMinimalChatMessage
    STRING_FIELD2: "Updated STRING_FIELD2",
    updatedBy: "admin"
  };
}

// ChatMessage for soft delete testing
export function createChatMessageForSoftDelete(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "To Be Deleted",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin"
  };
}

// Update for soft deleted chatMessage testing
export function createUpdateForSoftDeleted(): ChatMessageUpdateInput {
  return {
    STRING_FIELD: "Should Not Work",
    updatedBy: "admin"
  };
}

// Update with whitespace in all fields for comprehensive trimming test
export function createUpdateWithAllFieldsWhitespace(): ChatMessageUpdateInput {
  return {
    STRING_FIELD: "   Trimmed Name   ",
    STRING_FIELD2: "   Trimmed Description   ",
    ARRAY_FIELD2: ["   trimmed_condition   "],
    ARRAY_FIELD: ["   trimmed_action   "],
    tags: ["   tag1   ", "   tag2   "],
    updatedBy: "admin"
  };
}

// ChatMessage for trimming test
export function createChatMessageForTrimming(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Original ChatMessage",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    createdBy: "admin"
  };
}

// ChatMessage for active status testing
export function createActiveChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Active ChatMessage",
    ARRAY_FIELD2: ["cond"],
    ARRAY_FIELD: ["act"],
    isActive: true,
    createdBy: "admin"
  };
}

// Update for status change testing
export function createStatusChangeUpdate(): ChatMessageUpdateInput {
  return {
    isActive: false,
    updatedBy: "admin"
  };
}

// ========================================
// PARAMS CREATORS FOR implHandleGetAllChatMessages
// ========================================

// Search params
export function createSearchByNameParams() {
  return { search: "John" };
}

export function createSearchByDescriptionParams() {
  return { search: "processing" };
}

export function createEmptySearchParams() {
  return { search: "" };
}

export function createWhitespaceSearchParams() {
  return { search: "   " };
}

export function createNonExistentSearchParams() {
  return { search: "NonExistent" };
}

// Filter params
export function createVipTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "VIP" }]
  };
}

export function createCustomerTagFilterParams() {
  return {
    filters: [{ field: "tags", value: "Customer" }]
  };
}

// Pagination params
export function createPaginationParams() {
  return {
    page: 1,
    limit: 2
  };
}

// Sorting params
export function createSortByNameAscParams() {
  return {
    sorts: [{ field: "STRING_FIELD", direction: "asc" as const }]
  };
}

// Combined params
export function createSearchAndTagParams() {
  return {
    search: "John",
    tag: "VIP"
  };
}

// Include deleted params
export function createIncludeDeletedParams() {
  return { includeDeleted: true };
}

// Legacy tag params (converted to filters format)
export function createEmptyTagParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceTagParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

export function createNonExistentTagParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

// Additional search params for read.test.ts
export function createSearchByTagParams() {
  return { search: "VIP" };
}

export function createUnmatchedSearchParams() {
  return { search: "nonexistent" };
}

export function createUndefinedSearchParams() {
  return { search: undefined };
}

// Additional filter params for read.test.ts
export function createNonExistentFilterParams() {
  return {
    filters: [{ field: "tags", value: "NonExistent" }]
  };
}

export function createEmptyFilterFieldParams() {
  return {
    filters: [{ field: "", value: "test" }]
  };
}

export function createWhitespaceFilterFieldParams() {
  return {
    filters: [{ field: "   ", value: "test" }]
  };
}

// ========================================
// CREATORS FOR DELETE TESTS
// ========================================

// ChatMessage for retry delete testing
export function createRetryDeleteChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Retry Delete",
    ARRAY_FIELD2: ["attempt"],
    ARRAY_FIELD: ["log"],
    createdBy: "admin"
  };
}

// ========================================
// CREATORS FOR SPECIAL CASES (ChatMessage-specific)
// ========================================

// ChatMessage with special characters and unicode
export function createSpecialCharacterChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "José María O'Connor",
    STRING_FIELD2: "Handles unicode 🎉 & symbols",
    ARRAY_FIELD2: ["STRING_FIELD.includes('José')"],
    ARRAY_FIELD: ["notify", "log"],
    tags: ["Special", "🚀", "Test@Tag"],
    createdBy: "admin"
  };
}

// ChatMessage with very long ARRAY_FIELD2 (ChatMessage-specific test)
export function createLongContentChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Very Long ChatMessage Name That Exceeds Normal Length Expectations And Tests System Limits",
    STRING_FIELD2: "This is a very long STRING_FIELD2 that tests how the system handles extensive text ARRAY_FIELD2 in chatMessage STRING_FIELD2s. It includes multiple sentences and should test the limits of what the system can handle in terms of ARRAY_FIELD2 length and processing.",
    ARRAY_FIELD2: [
      "user.message.length > 1000",
      "user.message.includes('very long query with lots of details')",
      "user.session.duration > 3600"
    ],
    ARRAY_FIELD: [
      "log_extensive_details",
      "notify_admin_of_long_interaction",
      "create_detailed_report",
      "escalate_to_specialist"
    ],
    tags: ["LongContent", "EdgeCase", "SystemLimits", "Performance"],
    createdBy: "admin"
  };
}

// ChatMessage with edge case ARRAY_FIELD2 (ChatMessage-specific)
export function createEdgeCaseConditionsChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Edge Case Conditions",
    STRING_FIELD2: "Tests complex condition parsing",
    ARRAY_FIELD2: [
      "user.age >= 18 && user.age <= 65",
      "user.location.country === 'US' || user.location.country === 'CA'",
      "user.preferences.notifications === true"
    ],
    ARRAY_FIELD: [
      "apply_regional_chatMessages",
      "send_age_appropriate_ARRAY_FIELD2"
    ],
    tags: ["EdgeCase", "Complex"],
    createdBy: "admin"
  };
}

// ChatMessage with complex ARRAY_FIELD (ChatMessage-specific)
export function createComplexActionsChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Complex Actions ChatMessage",
    STRING_FIELD2: "Tests complex action execution",
    ARRAY_FIELD2: ["trigger_complex_workflow"],
    ARRAY_FIELD: [
      "webhook.call('https://api.example.com/notify')",
      "database.update('user_stats', {last_interaction: now()})",
      "ARRAY_FIELD.send(template='complex_notification', to=user.ARRAY_FIELD)",
      "analytics.track('complex_chatMessage_triggered', {chatMessage_id: this.id})"
    ],
    tags: ["Complex", "Integration"],
    createdBy: "admin"
  };
}

// ChatMessage with empty optional fields (ChatMessage-specific edge case)
export function createEmptyOptionalFieldsChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Empty Optional Fields",
    ARRAY_FIELD2: ["basic_condition"],
    ARRAY_FIELD: ["basic_action"],
    STRING_FIELD2: "",
    tags: [],
    createdBy: "admin"
  };
}

// ChatMessage for testing AI-specific business logic
export function createAiLogicChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "AI Decision ChatMessage",
    STRING_FIELD2: "Tests AI-specific decision making logic",
    ARRAY_FIELD2: [
      "ai.confidence > 0.8",
      "ai.model === 'gpt-4'",
      "ai.context.length > 100"
    ],
    ARRAY_FIELD: [
      "ai.respond_with_confidence",
      "ai.log_decision_path",
      "ai.update_learning_model"
    ],
    tags: ["AI", "MachineLearning", "Confidence"],
    createdBy: "admin"
  };
}

// Creators for delete test scenarios
export function createComplexChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Complex ChatMessage",
    STRING_FIELD2: "Full field test",
    ARRAY_FIELD2: ["user.role == 'admin'"],
    ARRAY_FIELD: ["grant_access", "log_activity"],
    tags: ["admin", "security"],
    isActive: true,
    createdBy: "admin"
  };
}

export function createMinimalDeleteChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Minimal ChatMessage",
    ARRAY_FIELD2: ["is.loggedIn"],
    ARRAY_FIELD: ["alert"],
    createdBy: "admin"
  };
}

// ChatMessages for testing deletion effects on other chatMessages
export function createChatMessagesForDeletionTest(): ChatMessageCreateInput[] {
  return [
    { STRING_FIELD: "Keep This One", ARRAY_FIELD2: ["x"], ARRAY_FIELD: ["a"], createdBy: "admin" },
    { STRING_FIELD: "Delete This One", ARRAY_FIELD2: ["y"], ARRAY_FIELD: ["b"], createdBy: "admin" },
    { STRING_FIELD: "Keep This Too", ARRAY_FIELD2: ["z"], ARRAY_FIELD: ["c"], createdBy: "admin" }
  ];
}

// Creators for bulk operations testing
export function createExistingChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Existing ChatMessage",
    STRING_FIELD2: "An existing chatMessage",
    ARRAY_FIELD2: ["User says test"],
    ARRAY_FIELD: ["Show test response"],
    createdBy: "admin"
  };
}

export function createDuplicateChatMessagesForBulk(): ChatMessageCreateInput[] {
  return [
    {
      STRING_FIELD: "Existing ChatMessage", // Duplicate STRING_FIELD
      STRING_FIELD2: "Another chatMessage with same STRING_FIELD",
      ARRAY_FIELD2: ["User says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "New ChatMessage",
      STRING_FIELD2: "A new chatMessage",
      ARRAY_FIELD2: ["User says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// ChatMessages for bulk update testing
export function createChatMessagesForBulkUpdate(): ChatMessageCreateInput[] {
  return [
    {
      STRING_FIELD: "ChatMessage 1",
      STRING_FIELD2: "First chatMessage",
      ARRAY_FIELD2: ["User says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "ChatMessage 2",
      STRING_FIELD2: "Second chatMessage",
      ARRAY_FIELD2: ["User says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    }
  ];
}

// Bulk update data
export function createBulkUpdateData(): any[] {
  return [
    {
      STRING_FIELD: "Updated ChatMessage 1",
      STRING_FIELD2: "Updated first chatMessage",
      updatedBy: "admin"
    },
    {
      STRING_FIELD: "Updated ChatMessage 2",
      STRING_FIELD2: "Updated second chatMessage",
      updatedBy: "admin"
    }
  ];
}

// ChatMessages for bulk delete testing
export function createChatMessagesForBulkDelete(): ChatMessageCreateInput[] {
  return [
    {
      STRING_FIELD: "ChatMessage 1",
      STRING_FIELD2: "First chatMessage",
      ARRAY_FIELD2: ["User says hello"],
      ARRAY_FIELD: ["Show greeting"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "ChatMessage 2",
      STRING_FIELD2: "Second chatMessage",
      ARRAY_FIELD2: ["User says goodbye"],
      ARRAY_FIELD: ["Show farewell"],
      createdBy: "admin"
    },
    {
      STRING_FIELD: "ChatMessage 3",
      STRING_FIELD2: "Third chatMessage",
      ARRAY_FIELD2: ["User asks question"],
      ARRAY_FIELD: ["Show help"],
      createdBy: "admin"
    }
  ];
}

// Invalid data creators for validation tests
export function createInvalidChatMessage(type: 'missing-STRING_FIELD' | 'missing-ARRAY_FIELD2' | 'missing-ARRAY_FIELD' | 'empty-ARRAY_FIELD2' | 'empty-ARRAY_FIELD' | 'missing-ARRAY_FIELD2'): any {
  const invalidChatMessages = {
    'missing-ARRAY_FIELD2': {
      STRING_FIELD: "John Doe"
    },
    'missing-STRING_FIELD': {
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'missing-ARRAY_FIELD2': {
      STRING_FIELD: "Invalid ChatMessage",
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'missing-ARRAY_FIELD': {
      STRING_FIELD: "Invalid ChatMessage",
      ARRAY_FIELD2: ["test_condition"],
      createdBy: "admin"
    },
    'empty-ARRAY_FIELD2': {
      STRING_FIELD: "Invalid ChatMessage",
      ARRAY_FIELD2: [],
      ARRAY_FIELD: ["test_action"],
      createdBy: "admin"
    },
    'empty-ARRAY_FIELD': {
      STRING_FIELD: "Invalid ChatMessage",
      ARRAY_FIELD2: ["test_condition"],
      ARRAY_FIELD: [],
      createdBy: "admin"
    }
  };

  return invalidChatMessages[type];
}

// Creator for chatMessages with special characteristics
export function createChatMessageWithWhitespace(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "  Trimmed ChatMessage  ",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin"
  };
}

export function createChatMessageWithManyTags(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Multi-tag ChatMessage",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    createdBy: "admin"
  };
}

export function createChatMessageWithoutDescription(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "ChatMessage without STRING_FIELD2",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    createdBy: "admin"
  };
}

export function createChatMessageWithEmptyTags(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "ChatMessage with empty tags",
    ARRAY_FIELD2: ["test_condition"],
    ARRAY_FIELD: ["test_action"],
    tags: [],
    createdBy: "admin"
  };
}

// Duplicate chatMessage creator for conflict testing
export function createDuplicateChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Duplicate ChatMessage",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin"
  };
}

export function createSecondDuplicateChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Duplicate ChatMessage", // Same STRING_FIELD as above
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin"
  };
}

// Test chatMessage with specific STRING_FIELD for soft delete tests
export function createTestChatMessage(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Test ChatMessage",
    ARRAY_FIELD2: ["condition1"],
    ARRAY_FIELD: ["action1"],
    createdBy: "admin"
  };
}

export function createTestChatMessage2(): ChatMessageCreateInput {
  return {
    STRING_FIELD: "Test ChatMessage",
    ARRAY_FIELD2: ["condition2"],
    ARRAY_FIELD: ["action2"],
    createdBy: "admin"
  };
}
