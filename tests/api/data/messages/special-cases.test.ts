//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatMessageBusinessLogicInterface } from "@/lib/repositories/chatMessages/interface";
import { ChatMessageBusinessLogic } from "@/lib/repositories/chatMessages/BusinessLogic";
import { MongoChatMessageRepository } from "@/lib/repositories/chatMessages/MongoRepository";
import { TestChatMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChatMessage, implHandleGetChatMessage, implHandleUpdateChatMessage, implHandleDeleteChatMessage } from "@/app/api/v1/chats/[chatId]/messages/impl";
import {
  createSpecialCharacterChatMessage,
  createLongContentChatMessage,
  createEdgeCaseConditionsChatMessage,
  createComplexActionsChatMessage,
  createEmptyOptionalFieldsChatMessage,
  createAiLogicChatMessage
} from "./object_creator";

/**
 * Special Cases Tests for ChatMessages
 * 
 * This file contains tests for ChatMessage-specific functionality that doesn't exist
 * in other features like ChatMessages or ChatMessages. These tests focus on:
 * - AI-specific business logic (ARRAY_FIELD, variables, AI decision making)
 * - Complex chatMessage processing scenarios
 * - Edge cases unique to chatMessage engines
 * - Special character and unicode handling in chatMessage contexts
 * - Performance and limits testing for chatMessage ARRAY_FIELD2
 * 
 * By keeping these tests separate, other features can easily copy the standard
 * CRUD test files without inheriting ChatMessage-specific complexity.
 */

describe("ChatMessage Special Cases Tests", () => {
  let businessLogic: ChatMessageBusinessLogicInterface;
  let dbRepository: TestChatMessageDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ChatMessage");
    await driver.connect();
    const originalDb = new MongoChatMessageRepository(driver);
    dbRepository = new TestChatMessageDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatMessageBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("Special Character and Unicode Handling", () => {
    it("should handle special characters and unicode in all fields", async () => {
      const chatMessageData = createSpecialCharacterChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);

      expect(createResult.status).toBe(201);
      expect(createResult.body.status).toBe("success");
      expect(createResult.body.data?.STRING_FIELD).toBe(chatMessageData.STRING_FIELD);
      expect(createResult.body.data?.ARRAY_FIELD2).toBe(chatMessageData.ARRAY_FIELD2);
      expect(createResult.body.data?.tags).toContain("🚀");
      expect(createResult.body.data?.tags).toContain("Test@Tag");
    });

    it("should search chatMessages with special characters", async () => {
      const chatMessageData = createSpecialCharacterChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      const searchResult = await implHandleGetAllChatMessages(businessLogic, { search: "José" });
      expect(searchResult.status).toBe(200);
      expect(searchResult.body.data).toHaveLength(1);
      expect(searchResult.body.data[0].STRING_FIELD).toBe("José María O'Connor");
    });

    it("should update chatMessages with special characters", async () => {
      const chatMessageData = createSpecialCharacterChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      const chatMessageId = createResult.body.data.id;

      const updateResult = await implHandleUpdateChatMessage(chatMessageId, {
        STRING_FIELD: "Updated José María 🎯",
        ARRAY_FIELD2: "Updated with more symbols ⭐ & emojis 🚀",
        updatedBy: "admin"
      }, businessLogic);

      expect(updateResult.status).toBe(200);
      expect(updateResult.body.data?.STRING_FIELD).toBe("Updated José María 🎯");
      expect(updateResult.body.data?.ARRAY_FIELD2).toBe("Updated with more symbols ⭐ & emojis 🚀");
    });

    it("should delete chatMessages with special characters", async () => {
      const chatMessageData = createSpecialCharacterChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      const chatMessageId = createResult.body.data.id;

      const deleteResult = await implHandleDeleteChatMessage(chatMessageId, businessLogic);
      expect(deleteResult.status).toBe(200);

      const getResult = await implHandleGetChatMessage(chatMessageId, businessLogic);
      expect(getResult.status).toBe(404);
    });
  });

  describe("Content Length and Performance", () => {
    it("should handle very long ARRAY_FIELD2 in all fields", async () => {
      const chatMessageData = createLongContentChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);

      expect(createResult.status).toBe(201);
      expect(createResult.body.status).toBe("success");
      expect(createResult.body.data?.STRING_FIELD.length).toBeGreaterThan(50);
      expect(createResult.body.data?.ARRAY_FIELD2.length).toBeGreaterThan(200);
      expect(createResult.body.data?.ARRAY_FIELD.length).toBe(3);
      expect(createResult.body.data?.variables.length).toBe(4);
    });

    it("should search through long ARRAY_FIELD2 efficiently", async () => {
      const chatMessageData = createLongContentChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      const searchResult = await implHandleGetAllChatMessages(businessLogic, { search: "extensive" });
      expect(searchResult.status).toBe(200);
      expect(searchResult.body.data).toHaveLength(1);
      expect(searchResult.body.data[0].ARRAY_FIELD2).toContain("extensive");
    });
  });

  describe("Complex ChatMessage Logic (ChatMessage-specific)", () => {
    it("should handle complex conditional logic", async () => {
      const chatMessageData = createEdgeCaseConditionsChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);

      expect(createResult.status).toBe(201);
      expect(createResult.body.data?.ARRAY_FIELD).toContain("user.age >= 18 && user.age <= 65");
      expect(createResult.body.data?.ARRAY_FIELD).toContain("user.location.country === 'US' || user.location.country === 'CA'");
    });

    it("should handle complex action definitions", async () => {
      const chatMessageData = createComplexActionsChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);

      expect(createResult.status).toBe(201);
      expect(createResult.body.data?.variables).toContain("webhook.call('https://api.example.com/notify')");
      expect(createResult.body.data?.variables).toContain("database.update('user_stats', {last_interaction: now()})");
    });

    it("should handle AI-specific business logic", async () => {
      const chatMessageData = createAiLogicChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);

      expect(createResult.status).toBe(201);
      expect(createResult.body.data?.ARRAY_FIELD).toContain("ai.confidence > 0.8");
      expect(createResult.body.data?.variables).toContain("ai.respond_with_confidence");
      expect(createResult.body.data?.tags).toContain("AI");
      expect(createResult.body.data?.tags).toContain("MachineLearning");
    });
  });

  describe("Edge Cases and Boundary Conditions", () => {
    it("should handle empty optional fields gracefully", async () => {
      const chatMessageData = createEmptyOptionalFieldsChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);

      expect(createResult.status).toBe(201);
      expect(createResult.body.data?.ARRAY_FIELD2).toBe("");
      expect(createResult.body.data?.tags).toEqual([]);
    });

    it("should validate chatMessage activation logic", async () => {
      const chatMessageData = createAiLogicChatMessage();
      chatMessageData.isActive = false;
      
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);
      expect(createResult.body.data?.isActive).toBe(false);

      // Test activation toggle
      const updateResult = await implHandleUpdateChatMessage(createResult.body.data.id, {
        isActive: true,
        updatedBy: "admin"
      }, businessLogic);

      expect(updateResult.status).toBe(200);
      expect(updateResult.body.data?.isActive).toBe(true);
    });

    it("should handle chatMessage priority and execution order concepts", async () => {
      // This test demonstrates ChatMessage-specific concepts that don't exist in ChatMessages/ChatMessages
      const chatMessages = [
        createAiLogicChatMessage(),
        createEdgeCaseConditionsChatMessage(),
        createComplexActionsChatMessage()
      ];

      const createdChatMessages = [];
      for (const chatMessage of chatMessages) {
        const result = await implHandleCreateChatMessage(chatMessage, businessLogic);
        createdChatMessages.push(result.body.data);
      }

      expect(createdChatMessages).toHaveLength(3);
      
      // Verify all chatMessages are created with proper timestamps for execution order
      for (let i = 1; i < createdChatMessages.length; i++) {
        expect(new Date(createdChatMessages[i].createdAt).getTime())
          .toBeGreaterThanOrEqual(new Date(createdChatMessages[i-1].createdAt).getTime());
      }
    });
  });

  describe("ChatMessage Engine Specific Functionality", () => {
    it("should handle chatMessage condition parsing and validation", async () => {
      const chatMessageData = createEdgeCaseConditionsChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);

      expect(createResult.status).toBe(201);
      
      // Test that ARRAY_FIELD are stored as-is for later parsing by chatMessage engine
      const ARRAY_FIELD = createResult.body.data?.ARRAY_FIELD;
      expect(ARRAY_FIELD).toBeDefined();
      expect(ARRAY_FIELD?.some(c => c.includes("&&"))).toBe(true);
      expect(ARRAY_FIELD?.some(c => c.includes("||"))).toBe(true);
    });

    it("should handle action execution metadata", async () => {
      const chatMessageData = createComplexActionsChatMessage();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);

      expect(createResult.status).toBe(201);
      
      // Test that variables contain execution metadata
      const variables = createResult.body.data?.variables;
      expect(variables?.some(a => a.includes("webhook.call"))).toBe(true);
      expect(variables?.some(a => a.includes("database.update"))).toBe(true);
      expect(variables?.some(a => a.includes("ARRAY_FIELD.send"))).toBe(true);
    });
  });
});
