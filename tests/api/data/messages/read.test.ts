// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatMessageBusinessLogicInterface } from "@/lib/repositories/chatMessages/interface";
import { ChatMessageBusinessLogic } from "@/lib/repositories/chatMessages/BusinessLogic";
import { MongoChatMessageRepository } from "@/lib/repositories/chatMessages/MongoRepository";
import { TestChatMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChatMessage, implHandleGetChatMessage, implHandleGetAllChatMessages } from "@/app/api/v1/chats/[chatId]/messages/impl";
import {
  createChatMessage,
  createSimpleChatMessages,
  createChatMessagesWithTags,
  createSearchByNameParams,
  createSearchByTagParams,
  createUnmatchedSearchParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createUndefinedSearchParams,
  createCustomerTagFilterParams,
  createVipTagFilterParams,
  createNonExistentFilterParams,
  createEmptyFilterFieldParams,
  createWhitespaceFilterFieldParams
} from "./object_creator";

describe("Read ChatMessage API Tests", () => {
  let businessLogic: ChatMessageBusinessLogicInterface;
  let dbRepository: TestChatMessageDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ChatMessage");
    await driver.connect();
    const originalDb = new MongoChatMessageRepository(driver);
    dbRepository = new TestChatMessageDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatMessageBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("GET /api/v1/chats/[chatId]/messages/:id", () => {
    it("should successfully get chatMessage by ID", async () => {
      const chatMessage = createChatMessage(5); // John Doe ChatMessage

      const createResult = await implHandleCreateChatMessage(chatMessage, businessLogic);
      const id = createResult.body.data.id;

      const result = await implHandleGetChatMessage(id, businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.id).toBe(id);
      expect(result.body.data?.STRING_FIELD).toBe(chatMessage.STRING_FIELD);
    });

    it("should fail to get non-existent chatMessage", async () => {
      const result = await implHandleGetChatMessage("507f1f77bcf86cd799439011", businessLogic);
      expect(result.status).toBe(404);
    });

    it("should fail with empty chatMessage ID", async () => {
      const result = await implHandleGetChatMessage("", businessLogic);
      expect(result.status).toBe(400);
    });

    it("should fail with whitespace-only chatMessage ID", async () => {
      const result = await implHandleGetChatMessage("   ", businessLogic);
      expect(result.status).toBe(400);
    });
  });

  describe("GET /api/v1/chats/[chatId]/messages", () => {
    it("should get all chatMessages", async () => {
      const chatMessages = createSimpleChatMessages();
      for (const r of chatMessages) await implHandleCreateChatMessage(r, businessLogic);

      const result = await implHandleGetAllChatMessages(businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(3);
    });

    it("should return empty when no chatMessages exist", async () => {
      const result = await implHandleGetAllChatMessages(businessLogic);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });
  });

  describe("GET /api/v1/chats/[chatId]/messages/search", () => {
    beforeEach(async () => {
      const data = createChatMessagesWithTags();
      for (const r of data) await implHandleCreateChatMessage(r, businessLogic);
    });

    it("should search by STRING_FIELD", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should search by tag", async () => {
      const params = createSearchByTagParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should return empty for unmatched search", async () => {
      const params = createUnmatchedSearchParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });

    it("should reject empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should reject whitespace-only search keyword", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should return all if search is undefined", async () => {
      const params = createUndefinedSearchParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(4);
    });
  });

  describe("GET /api/v1/chats/[chatId]/messages/filters", () => {
    beforeEach(async () => {
      const data = createChatMessagesWithTags();
      for (const r of data) await implHandleCreateChatMessage(r, businessLogic);
    });

    it("should filter by tag 'Customer'", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should filter by tag 'VIP'", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(2);
    });

    it("should return empty for non-existent tag", async () => {
      const params = createNonExistentFilterParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(200);
      expect(result.body.data?.items.length).toBe(0);
    });

    it("should reject empty filter field", async () => {
      const params = createEmptyFilterFieldParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(400);
    });

    it("should reject whitespace-only filter field", async () => {
      const params = createWhitespaceFilterFieldParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);
      expect(result.status).toBe(400);
    });
  });
});
