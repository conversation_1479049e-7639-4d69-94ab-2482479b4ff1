// @ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatMessageBusinessLogicInterface } from "@/lib/repositories/chatMessages/interface";
import { ChatMessageBusinessLogic } from "@/lib/repositories/chatMessages/BusinessLogic";
import { MongoChatMessageRepository } from "@/lib/repositories/chatMessages/MongoRepository";
import { TestChatMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChatMessage, implHandleGetChatMessage, implHandleDeleteChatMessage } from "@/app/api/v1/chats/[chatId]/messages/impl";
import {
  createChatMessage,
  createSimpleChatMessages,
  createComplexChatMessage,
  createMinimalDeleteChatMessage,
  createChatMessagesForDeletionTest,
  createRetryDeleteChatMessage
} from "./object_creator";

describe("Delete ChatMessage API Tests", () => {
  let businessLogic: ChatMessageBusinessLogicInterface;
  let dbRepository: TestChatMessageDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ChatMessage");
    await driver.connect();
    const originalDb = new MongoChatMessageRepository(driver);
    dbRepository = new TestChatMessageDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatMessageBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("DELETE /api/v1/chats/[chatId]/messages/:id", () => {
    it("should successfully delete an existing chatMessage", async () => {
      const chatMessagesData = createChatMessage(5); // John Doe ChatMessage
      const createResult = await implHandleCreateChatMessage(chatMessagesData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const getResult = await implHandleGetChatMessage(chatMessagesId, businessLogic);
      expect(getResult.status).toBe(200);

      const deleteResult = await implHandleDeleteChatMessage(chatMessagesId, businessLogic);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");
      expect(deleteResult.body.data.message).toBe("ChatMessage deleted successfully");

      const getAfterDelete = await implHandleGetChatMessage(chatMessagesId, businessLogic);
      expect(getAfterDelete.status).toBe(404);
    });

    it("should verify chatMessage count decreases after deletion", async () => {
      const chatMessagesData = createSimpleChatMessages();

      const chatMessagesIds: string[] = [];
      for (const data of chatMessagesData) {
        const result = await implHandleCreateChatMessage(data, businessLogic);
        chatMessagesIds.push(result.body.data?.id);
      }

      expect(await dbRepository.getChatMessageCount()).toBe(3);

      const deleteResult = await implHandleDeleteChatMessage(chatMessagesIds[1], businessLogic);
      expect(deleteResult.status).toBe(200);
      expect(await dbRepository.getChatMessageCount()).toBe(2);

      const getDeleted = await implHandleGetChatMessage(chatMessagesIds[1], businessLogic);
      expect(getDeleted.status).toBe(404);
    });

    it("should fail to delete non-existent chatMessage", async () => {
      const result = await implHandleDeleteChatMessage("507f1f77bcf86cd799439011", businessLogic);
      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ChatMessage not found");
    });

    it("should fail with empty chatMessage ID", async () => {
      const result = await implHandleDeleteChatMessage("", businessLogic);
      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ChatMessage ID is required");
    });

    it("should handle deletion with all fields", async () => {
      const chatMessagesData = createComplexChatMessage();
      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);
      const id = result.body.data?.id;

      const deleteResult = await implHandleDeleteChatMessage(id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const getAfter = await implHandleGetChatMessage(id, businessLogic);
      expect(getAfter.status).toBe(404);
    });

    it("should handle deletion with minimal fields", async () => {
      const chatMessagesData = createMinimalDeleteChatMessage();
      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);
      const id = result.body.data?.id;

      const deleteResult = await implHandleDeleteChatMessage(id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const getAfter = await implHandleGetChatMessage(id, businessLogic);
      expect(getAfter.status).toBe(404);
    });

    it("should allow deletion of multiple chatMessages", async () => {
      const chatMessagesData = createSimpleChatMessages();

      const ids: string[] = [];
      for (const data of chatMessagesData) {
        const res = await implHandleCreateChatMessage(data, businessLogic);
        ids.push(res.body.data.id);
      }

      for (const id of ids) {
        const res = await implHandleDeleteChatMessage(id, businessLogic);
        expect(res.status).toBe(200);
      }

      expect(await dbRepository.getChatMessageCount()).toBe(0);
    });

    it("should not affect other chatMessages when deleting one", async () => {
      const chatMessagesData = createChatMessagesForDeletionTest();

      const ids: string[] = [];
      for (const data of chatMessagesData) {
        const res = await implHandleCreateChatMessage(data, businessLogic);
        ids.push(res.body.data.id);
      }

      await implHandleDeleteChatMessage(ids[1], businessLogic);

      const getDeleted = await implHandleGetChatMessage(ids[1], businessLogic);
      expect(getDeleted.status).toBe(404);

      const first = await implHandleGetChatMessage(ids[0], businessLogic);
      expect(first.status).toBe(200);

      const third = await implHandleGetChatMessage(ids[2], businessLogic);
      expect(third.status).toBe(200);

      expect(await dbRepository.getChatMessageCount()).toBe(2);
    });

    it("should handle attempting to delete the same chatMessage twice", async () => {
      const chatMessagesData = createRetryDeleteChatMessage();
      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);
      const id = result.body.data?.id;

      const first = await implHandleDeleteChatMessage(id, businessLogic);
      expect(first.status).toBe(200);

      const second = await implHandleDeleteChatMessage(id, businessLogic);
      expect(second.status).toBe(404);
    });
  });
});
