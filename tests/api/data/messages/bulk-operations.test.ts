//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatMessageBusinessLogicInterface } from "@/lib/repositories/chatMessages/interface";
import { ChatMessageBusinessLogic } from "@/lib/repositories/chatMessages/BusinessLogic";
import { MongoChatMessageRepository } from "@/lib/repositories/chatMessages/MongoRepository";
import { TestChatMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateChatMessage,
  implHandleGetChatMessage,
  implHandleBulkCreateChatMessages,
  implHandleBulkUpdateChatMessages,
  implHandleBulkDeleteChatMessages
} from "@/app/api/v1/chats/[chatId]/messages/impl";
import {
  createMultipleChatMessages,
  createSimpleChatMessages,
  createExistingChatMessage,
  createDuplicateChatMessagesForBulk,
  createChatMessagesForBulkUpdate,
  createBulkUpdateData,
  createChatMessagesForBulkDelete
} from "./object_creator";

describe("ChatMessage Bulk Operations Tests", () => {
  let businessLogic: ChatMessageBusinessLogicInterface;
  let dbRepository: TestChatMessageDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ChatMessage");
    await driver.connect()
    const originalDb = new MongoChatMessageRepository(driver);
    dbRepository = new TestChatMessageDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatMessageBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("Bulk Create", () => {
    it("should successfully create multiple chatMessages", async () => {
      const chatMessagesData = createMultipleChatMessages();

      const result = await implHandleBulkCreateChatMessages(chatMessagesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(chatMessagesData.length);
      expect(result.body.data[0].STRING_FIELD).toBe(chatMessagesData[0].STRING_FIELD);
      expect(result.body.data[1].STRING_FIELD).toBe(chatMessagesData[1].STRING_FIELD);
      expect(result.body.data[2].STRING_FIELD).toBe(chatMessagesData[2].STRING_FIELD);
      expect(await dbRepository.getChatMessageCount()).toBe(chatMessagesData.length);
    });

    it("should fail if any chatMessage has duplicate STRING_FIELD", async () => {
      const existingChatMessage = createExistingChatMessage();
      const createResult = await implHandleCreateChatMessage(existingChatMessage, businessLogic);
      expect(createResult.status).toBe(201);

      const chatMessagesData = createDuplicateChatMessagesForBulk();

      const result = await implHandleBulkCreateChatMessages(chatMessagesData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate STRING_FIELD found: Existing ChatMessage");
      expect(await dbRepository.getChatMessageCount()).toBe(1);
    });

    it("should handle simple chatMessages creation", async () => {
      const chatMessagesData = createSimpleChatMessages();

      const result = await implHandleBulkCreateChatMessages(chatMessagesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(chatMessagesData.length);
      expect(await dbRepository.getChatMessageCount()).toBe(chatMessagesData.length);
    });
  });

  describe("Bulk Update", () => {
    it("should successfully update multiple chatMessages", async () => {
      const chatMessagesData = createChatMessagesForBulkUpdate();
      const createResult1 = await implHandleCreateChatMessage(chatMessagesData[0], businessLogic);
      const createResult2 = await implHandleCreateChatMessage(chatMessagesData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult1.body.data.id, data: updateData[0] },
        { id: createResult2.body.data.id, data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateChatMessages(updates, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.updatedCount).toBe(2);

      const getResult1 = await implHandleGetChatMessage(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetChatMessage(createResult2.body.data.id, businessLogic);

      expect(getResult1.body.data?.STRING_FIELD).toBe(updateData[0].STRING_FIELD);
      expect(getResult1.body.data?.updatedBy).toBe(updateData[0].updatedBy);
      expect(getResult2.body.data?.STRING_FIELD).toBe(updateData[1].STRING_FIELD);
      expect(getResult2.body.data?.updatedBy).toBe(updateData[1].updatedBy);
    });

    it("should fail if any chatMessage doesn't exist", async () => {
      const chatMessageData = createChatMessagesForBulkUpdate()[0];
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult.body.data.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateChatMessages(updates, businessLogic);

      expect(result.status).toBe(500);
      expect(result.body.status).toBe("failed");
    });

    it("should fail if any update would create duplicate STRING_FIELD", async () => {
      const chatMessagesData = createChatMessagesForBulkUpdate();
      const createResult1 = await implHandleCreateChatMessage(chatMessagesData[0], businessLogic);
      const createResult2 = await implHandleCreateChatMessage(chatMessagesData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updates = [
        {
          id: createResult2.body.data.id,
          data: { STRING_FIELD: chatMessagesData[0].STRING_FIELD, updatedBy: "admin" }, // Try to update second chatMessage with first chatMessage's STRING_FIELD
        },
      ];

      const result = await implHandleBulkUpdateChatMessages(updates, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate STRING_FIELD in update: ChatMessage 1");
    });
  });

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple chatMessages", async () => {
      const chatMessagesData = createChatMessagesForBulkDelete();
      const createResult1 = await implHandleCreateChatMessage(chatMessagesData[0], businessLogic);
      const createResult2 = await implHandleCreateChatMessage(chatMessagesData[1], businessLogic);
      const createResult3 = await implHandleCreateChatMessage(chatMessagesData[2], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);
      expect(createResult3.status).toBe(201);

      const result = await implHandleBulkDeleteChatMessages([createResult1.body.data.id, createResult2.body.data.id], businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getChatMessageCount()).toBe(1); // Only non-deleted

      const getResult1 = await implHandleGetChatMessage(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetChatMessage(createResult2.body.data.id, businessLogic);
      const getResult3 = await implHandleGetChatMessage(createResult3.body.data.id, businessLogic);

      expect(getResult1.status).toBe(404);
      expect(getResult2.status).toBe(404);
      expect(getResult3.status).toBe(200);
    });

    it("should successfully hard delete multiple chatMessages", async () => {
      const chatMessagesData = createChatMessagesForBulkDelete();
      const createResult1 = await implHandleCreateChatMessage(chatMessagesData[0], businessLogic);
      const createResult2 = await implHandleCreateChatMessage(chatMessagesData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const result = await implHandleBulkDeleteChatMessages([createResult1.body.data.id, createResult2.body.data.id], businessLogic, true);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getChatMessageCount()).toBe(0);
    });

    it("should fail if any chatMessage doesn't exist", async () => {
      const chatMessageData = createChatMessagesForBulkDelete()[0];
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      const result = await implHandleBulkDeleteChatMessages([createResult.body.data.id, "non-existent-id"], businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty chatMessage IDs", async () => {
      const result = await implHandleBulkDeleteChatMessages(["", "valid-id"], businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ID at index 0 is required");
    });
  });
});
