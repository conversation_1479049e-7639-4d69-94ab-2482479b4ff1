//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatMessageBusinessLogicInterface } from "@/lib/repositories/chatMessages/interface";
import { ChatMessageBusinessLogic } from "@/lib/repositories/chatMessages/BusinessLogic";
import { MongoChatMessageRepository } from "@/lib/repositories/chatMessages/MongoRepository";
import { TestChatMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateChatMessage,
  implHandleGetChatMessage,
  implHandleDeleteChatMessage,
  implHandleUpdateChatMessage,
  implHandleGetAllChatMessages,
  implHandleRestoreChatMessage
} from "@/app/api/v1/chats/[chatId]/messages/impl";
import { createChatMessage, createChatMessageUpdate, createChatMessageWithDescription, createTestChatMessage, createTestChatMessage2 } from "./object_creator";

describe("ChatMessage Soft Delete Tests", () => {
  let businessLogic: ChatMessageBusinessLogicInterface;
    let dbRepository: TestChatMessageDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("ChatMessage");
      await driver.connect()
      const originalDb = new MongoChatMessageRepository(driver);
      dbRepository = new TestChatMessageDBRepositoryWrapper(originalDb, driver);
      businessLogic = new ChatMessageBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("Soft Delete", () => {
    it("should soft delete a chatMessages by default", async () => {
      const chatMessageData = createChatMessageWithDescription();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      const deleteResult = await implHandleDeleteChatMessage(createResult.body.data.id, businessLogic);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");

      // ChatMessage should not be accessible by default
      const getResult = await implHandleGetChatMessage(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);

      // But should be accessible when including deleted
      const getDeletedResult = await implHandleGetChatMessage(createResult.body.data.id, businessLogic, true);
      expect(getDeletedResult.status).toBe(200);
      expect(getDeletedResult.body.data).not.toBeNull();

      // Count should exclude soft deleted
      expect(await dbRepository.getChatMessageCount()).toBe(0);
      expect(await dbRepository.getChatMessageCount(true)).toBe(1);
    });

    it("should hard delete when specified", async () => {
      const chatMessageData = createChatMessageWithDescription();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      // Hard delete using impl function
      const deleteResult = await implHandleDeleteChatMessage(createResult.body.data.id, businessLogic, true);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");

      // ChatMessage should not be accessible at all
      const getResult = await implHandleGetChatMessage(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);
      const getDeletedResult = await implHandleGetChatMessage(createResult.body.data.id, businessLogic, true);
      expect(getDeletedResult.status).toBe(404);

      // Count should be 0 in both cases
      expect(await dbRepository.getChatMessageCount()).toBe(0);
      expect(await dbRepository.getChatMessageCount(true)).toBe(0);
    });

    it("should not include soft deleted chatMessages in getAll by default", async () => {
      const chatMessageData1 = createChatMessage(1);
      const chatMessageData2 = createChatMessage(2);

      const createResult1 = await implHandleCreateChatMessage(chatMessageData1, businessLogic);
      const createResult2 = await implHandleCreateChatMessage(chatMessageData2, businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      // Soft delete one chatMessages
      const deleteResult = await implHandleDeleteChatMessage(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const result = await implHandleGetAllChatMessages(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.items).toHaveLength(1);
      expect(result.body.data?.total).toBe(1);
      expect(result.body.data?.items[0].id).toBe(createResult2.body.data.id);
    });

    it("should include soft deleted chatMessages when specified", async () => {
      const chatMessageData1 = createChatMessage(1);
      const chatMessageData2 = createChatMessage(2);

      const createResult1 = await implHandleCreateChatMessage(chatMessageData1, businessLogic);
      const createResult2 = await implHandleCreateChatMessage(chatMessageData2, businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      // Soft delete one chatMessages
      const deleteResult = await implHandleDeleteChatMessage(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const result = await implHandleGetAllChatMessages(businessLogic, { includeDeleted: true });

      expect(result.status).toBe(200);
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(2);

      const deletedChatMessage = result.body.data?.items.find((c: any) => c.id === createResult1.body.data.id);
      expect(deletedChatMessage).toBeDefined();
      expect(deletedChatMessage?.deletedAt).toBeDefined();
    });

    it("should not allow updating soft deleted chatMessages", async () => {
      const chatMessageData = createChatMessage(3);
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      const deleteResult = await implHandleDeleteChatMessage(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const chatMessageUpdate = createChatMessageUpdate(1)

      const result = await implHandleUpdateChatMessage(createResult.body.data.id, chatMessageUpdate, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should not include soft deleted chatMessages in search", async () => {
      const chatMessageData1 = createChatMessage(3); // "Test ChatMessage"
      const createResult = await implHandleCreateChatMessage(chatMessageData1, businessLogic);
      expect(createResult.status).toBe(201);

      // Soft delete the chatMessage
      const deleteResult = await implHandleDeleteChatMessage(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Search should not find the soft deleted chatMessage
      const searchResult = await implHandleGetAllChatMessages(businessLogic, { search: "Test" });
      expect(searchResult.status).toBe(200);
      expect(searchResult.body.data).toHaveLength(0);
    });
  });

  describe("Restore", () => {
    it("should restore a soft deleted chatMessages", async () => {
      const chatMessageData = createChatMessageWithDescription();
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      // Soft delete the chatMessages
      const deleteResult = await implHandleDeleteChatMessage(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const getResult = await implHandleGetChatMessage(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);

      // Restore the chatMessages
      const restoreResult = await implHandleRestoreChatMessage(createResult.body.data.id, businessLogic);

      expect(restoreResult.status).toBe(200);
      expect(restoreResult.body.status).toBe("success");

      // ChatMessage should be accessible again
      const restoredResult = await implHandleGetChatMessage(createResult.body.data.id, businessLogic);
      expect(restoredResult.status).toBe(200);
      expect(restoredResult.body.data?.deletedAt).toBeUndefined();

      // Count should include the restored chatMessages
      expect(await dbRepository.getChatMessageCount()).toBe(1);
    });

    it("should fail to restore a non-existent chatMessages", async () => {
      const restoreResult = await implHandleRestoreChatMessage("507f1f77bcf86cd799439011", businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail to restore a chatMessages that was never deleted", async () => {
      const chatMessageData = createChatMessage(3);
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      const restoreResult = await implHandleRestoreChatMessage(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail to restore a hard deleted chatMessages", async () => {
      const chatMessageData = createChatMessage(3);
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      // Hard delete the chatMessages
      const deleteResult = await implHandleDeleteChatMessage(createResult.body.data.id, businessLogic, true);
      expect(deleteResult.status).toBe(200);

      const restoreResult = await implHandleRestoreChatMessage(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail with empty chatMessages ID", async () => {
      const restoreResult = await implHandleRestoreChatMessage("", businessLogic);
      expect(restoreResult.status).toBe(400);
      expect(restoreResult.body.status).toBe("failed");
      expect(restoreResult.body.error).toContain("ChatMessage ID is required");
    });

    it("should fail with whitespace-only chatMessages ID", async () => {
      const restoreResult = await implHandleRestoreChatMessage("   ", businessLogic);
      expect(restoreResult.status).toBe(400);
      expect(restoreResult.body.status).toBe("failed");
      expect(restoreResult.body.error).toContain("ChatMessage ID is required");
    });

    it("should update updatedAt when restoring", async () => {
      const chatMessageData = createChatMessage(3);
      const createResult = await implHandleCreateChatMessage(chatMessageData, businessLogic);
      expect(createResult.status).toBe(201);

      const originalUpdatedAt = createResult.body.data.updatedAt;

      // Wait a bit to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      // Soft delete and restore
      const deleteResult = await implHandleDeleteChatMessage(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const restoreResult = await implHandleRestoreChatMessage(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(200);

      const getResult = await implHandleGetChatMessage(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(200);
      expect(getResult.body.data?.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating chatMessages with STRING_FIELD of soft deleted chatMessages", async () => {
      // Create and soft delete a chatMessages
      const chatMessageData1 = createTestChatMessage();
      const createResult1 = await implHandleCreateChatMessage(chatMessageData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const deleteResult = await implHandleDeleteChatMessage(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Should be able to create new chatMessages with same STRING_FIELD
      const chatMessageData2 = createTestChatMessage2();
      const createResult2 = await implHandleCreateChatMessage(chatMessageData2, businessLogic);

      expect(createResult2.status).toBe(201);
      expect(createResult2.body.data.STRING_FIELD).toBe(chatMessageData2.STRING_FIELD);
      expect(await dbRepository.getChatMessageCount()).toBe(1);
    });

    it("should prevent creating chatMessages with STRING_FIELD of active chatMessages", async () => {
      const chatMessageData1 = createTestChatMessage();
      const createResult1 = await implHandleCreateChatMessage(chatMessageData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const chatMessageData2 = createTestChatMessage2();
      const createResult2 = await implHandleCreateChatMessage(chatMessageData2, businessLogic);

      expect(createResult2.status).toBe(409);
      expect(createResult2.body.status).toBe("failed");
      expect(createResult2.body.error).toContain("ChatMessage with the same STRING_FIELD already exists");
    });

    it("should prevent restoring chatMessages if STRING_FIELD is now taken", async () => {
      // Create and soft delete a chatMessages
      const chatMessageData1 = createTestChatMessage();
      const createResult1 = await implHandleCreateChatMessage(chatMessageData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const deleteResult = await implHandleDeleteChatMessage(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Create new chatMessages with same STRING_FIELD
      const chatMessageData2 = createTestChatMessage2();
      const createResult2 = await implHandleCreateChatMessage(chatMessageData2, businessLogic);
      expect(createResult2.status).toBe(201);

      // Should not be able to restore the first chatMessages
      const restoreResult = await implHandleRestoreChatMessage(createResult1.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });
  });
});
