//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatMessageBusinessLogicInterface } from "@/lib/repositories/chatMessages/interface";
import { ChatMessageBusinessLogic } from "@/lib/repositories/chatMessages/BusinessLogic";
import { MongoChatMessageRepository } from "@/lib/repositories/chatMessages/MongoRepository";
import { TestChatMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChatMessage } from "@/app/api/v1/chats/[chatId]/messages/impl";
import {
  createFullChatMessage,
  createMinimalChatMessage,
  createChatMessageWithDescription,
  createChatMessageWithTags,
  createChatMessageWithWhitespace,
  createDuplicateChatMessage,
  createSecondDuplicateChatMessage,
  createInvalidChatMessage,
  createChatMessageWithManyTags,
  createChatMessageWithoutDescription,
  createChatMessageWithEmptyTags
} from "./object_creator";

describe("Create ChatMessage API Tests", () => {
  let businessLogic: ChatMessageBusinessLogicInterface;
    let dbRepository: TestChatMessageDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("ChatMessage");
      await driver.connect()
      const originalDb = new MongoChatMessageRepository(driver);
      dbRepository = new TestChatMessageDBRepositoryWrapper(originalDb, driver);
      businessLogic = new ChatMessageBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("POST /api/v1/chats/[chatId]/messages", () => {
    it("should successfully create a new chatMessages with all fields", async () => {
      const chatMessagesData = createFullChatMessage();

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(chatMessagesData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(chatMessagesData.ARRAY_FIELD2);
      expect(result.body.data?.ARRAY_FIELD).toEqual(chatMessagesData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(chatMessagesData.variables);
      expect(result.body.data?.tags).toEqual(chatMessagesData.tags);
      expect(result.body.data?.isActive).toBe(chatMessagesData.isActive);
      expect(result.body.data?.id).toBeDefined();
      expect(result.body.data?.createdAt).toBeDefined();
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should successfully create a chatMessages with minimal required fields", async () => {
      const chatMessagesData = createMinimalChatMessage();

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(chatMessagesData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD).toEqual(chatMessagesData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(chatMessagesData.variables);
      expect(result.body.data?.isActive).toBe(true); // Should default to true
      expect(result.body.data?.tags).toEqual([]);
    });

    it("should create chatMessage with ARRAY_FIELD2", async () => {
      const chatMessagesData = createChatMessageWithDescription();

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.ARRAY_FIELD2).toBe(chatMessagesData.ARRAY_FIELD2);
    });

    it("should create chatMessage with tags", async () => {
      const chatMessagesData = createChatMessageWithTags();

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.tags).toEqual(chatMessagesData.tags);
    });

    it("should trim whitespace from STRING_FIELD", async () => {
      const chatMessagesData = createChatMessageWithWhitespace();

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed ChatMessage");
    });

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first chatMessages
      const chatMessagesData1 = createDuplicateChatMessage();
      await implHandleCreateChatMessage(chatMessagesData1, businessLogic);

      // Try to create second chatMessages with same STRING_FIELD
      const chatMessagesData2 = createSecondDuplicateChatMessage();
      const result = await implHandleCreateChatMessage(chatMessagesData2, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ChatMessage with the same STRING_FIELD already exists");
    });

    it("should fail with missing STRING_FIELD", async () => {
      const chatMessagesData = {
        ARRAY_FIELD2: "+6281234567890"
      };

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing ARRAY_FIELD2", async () => {
      const chatMessagesData = createInvalidChatMessage('missing-ARRAY_FIELD2');


      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing ARRAY_FIELD", async () => {
      const chatMessagesData = createInvalidChatMessage('missing-ARRAY_FIELD');

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("ARRAY_FIELD");
    });

    it("should fail with missing variables", async () => {
      const chatMessagesData = createInvalidChatMessage('missing-variables');

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("variables");
    });

    it("should fail with empty ARRAY_FIELD array", async () => {
      const chatMessagesData = createInvalidChatMessage('empty-ARRAY_FIELD');

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with empty variables array", async () => {
      const chatMessagesData = createInvalidChatMessage('empty-variables');

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should create chatMessage with many tags", async () => {
      const chatMessagesData = createChatMessageWithManyTags();

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(chatMessagesData.tags);
    });

    it("should handle optional ARRAY_FIELD2", async () => {
      const chatMessagesData = createChatMessageWithoutDescription();

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.ARRAY_FIELD2).toBe("");
    });

    it("should handle empty arrays for tags", async () => {
      const chatMessagesData = createChatMessageWithEmptyTags();

      const result = await implHandleCreateChatMessage(chatMessagesData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(chatMessagesData.tags);
    });
  });
});
