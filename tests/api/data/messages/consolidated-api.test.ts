//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatMessageBusinessLogicInterface } from "@/lib/repositories/chatMessages/interface";
import { ChatMessageBusinessLogic } from "@/lib/repositories/chatMessages/BusinessLogic";
import { MongoChatMessageRepository } from "@/lib/repositories/chatMessages/MongoRepository";
import { TestChatMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChatMessage, implHandleGetAllChatMessages, implHandleDeleteChatMessage } from "@/app/api/v1/chats/[chatId]/messages/impl";
import {
  createMultipleChatMessages,
  createSearchByNameParams,
  createSearchByDescriptionParams,
  createEmptySearchParams,
  createWhitespaceSearchParams,
  createNonExistentSearchParams,
  createVipTagFilterParams,
  createCustomerTagFilterParams,
  createPaginationParams,
  createSortByNameAscParams,
  createSearchAndTagParams,
  createIncludeDeletedParams,
  createEmptyTagParams,
  createWhitespaceTagParams,
  createNonExistentTagParams
} from "./object_creator";

describe("Consolidated ChatMessage API Tests", () => {
  let businessLogic: ChatMessageBusinessLogicInterface;
  let dbRepository: TestChatMessageDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ChatMessage");
    await driver.connect()
    const originalDb = new MongoChatMessageRepository(driver);
    dbRepository = new TestChatMessageDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatMessageBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  const testChatMessages = createMultipleChatMessages();

  describe("implHandleGetAllChatMessages - Consolidated Function", () => {
    beforeEach(async () => {
      for (const chatMessagesData of testChatMessages) {
        await implHandleCreateChatMessage(chatMessagesData, businessLogic);
      }
    });

    it("should get all chatMessages when no parameters provided", async () => {
      const result = await implHandleGetAllChatMessages(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);
      expect(result.body.data?.total).toBe(4);
    });

    it("should search chatMessages by STRING_FIELD", async () => {
      const params = createSearchByNameParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe ChatMessage and Bob Johnson ChatMessage

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testChatMessages[0].STRING_FIELD); // John Doe ChatMessage
      expect(STRING_FIELDs).toContain(testChatMessages[2].STRING_FIELD); // Bob Johnson ChatMessage
    });

    it("should search chatMessages by ARRAY_FIELD2", async () => {
      const params = createSearchByDescriptionParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4); // All chatMessages have "processing" in ARRAY_FIELD2
    });

    it("should filter chatMessages by tag", async () => {
      const params = createVipTagFilterParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe ChatMessage and Bob Johnson ChatMessage

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testChatMessages[0].STRING_FIELD); // John Doe ChatMessage
      expect(STRING_FIELDs).toContain(testChatMessages[2].STRING_FIELD); // Bob Johnson ChatMessage
    });

    it("should filter chatMessages by Customer tag", async () => {
      const params = createCustomerTagFilterParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // John Doe ChatMessage and Jane Smith ChatMessage

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs).toContain(testChatMessages[0].STRING_FIELD); // John Doe ChatMessage
      expect(STRING_FIELDs).toContain(testChatMessages[1].STRING_FIELD); // Jane Smith ChatMessage
    });

    it("should handle pagination", async () => {
      const params = createPaginationParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(4);
    });

    it("should handle sorting by STRING_FIELD", async () => {
      const params = createSortByNameAscParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(4);

      const STRING_FIELDs = result.body.data?.items.map((c: any) => c.STRING_FIELD);
      expect(STRING_FIELDs![0]).toBe(testChatMessages[3].STRING_FIELD); // Alice Brown ChatMessage
      expect(STRING_FIELDs![1]).toBe(testChatMessages[2].STRING_FIELD); // Bob Johnson ChatMessage
      expect(STRING_FIELDs![2]).toBe(testChatMessages[1].STRING_FIELD); // Jane Smith ChatMessage
      expect(STRING_FIELDs![3]).toBe(testChatMessages[0].STRING_FIELD); // John Doe ChatMessage
    });

    it("should combine search and tag filtering", async () => {
      // This should work if the implementation supports both search and tag filtering
      // For now, tag filtering takes precedence over search in our implementation
      const params = createSearchAndTagParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(2); // VIP chatMessages (tag filter applied)
    });

    it("should return empty results for non-existent search", async () => {
      const params = createNonExistentSearchParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should return empty results for non-existent tag", async () => {
      const params = createNonExistentTagParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.items).toHaveLength(0);
      expect(result.body.data?.total).toBe(0);
    });

    it("should fail with empty search keyword", async () => {
      const params = createEmptySearchParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should fail with empty filter field", async () => {
      const params = createEmptyTagParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should handle whitespace-only search", async () => {
      const params = createWhitespaceSearchParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Search keyword cannot be empty");
    });

    it("should handle whitespace-only filter field", async () => {
      const params = createWhitespaceTagParams();
      const result = await implHandleGetAllChatMessages(businessLogic, params);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Filter field cannot be empty");
    });

    it("should include soft deleted chatMessages when specified", async () => {
      // Get all chatMessages first to get one to delete
      const allResult = await implHandleGetAllChatMessages(businessLogic);
      expect(allResult.status).toBe(200);
      const chatMessagesToDelete = allResult.body.data?.items[0];
      expect(chatMessagesToDelete).toBeDefined();

      // Soft delete one chatMessages
      const deleteResult = await implHandleDeleteChatMessage(chatMessagesToDelete!.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Get all without including deleted
      const resultWithoutDeleted = await implHandleGetAllChatMessages(businessLogic);
      expect(resultWithoutDeleted.body.data?.items).toHaveLength(3);

      // Get all including deleted
      const params = createIncludeDeletedParams();
      const resultWithDeleted = await implHandleGetAllChatMessages(businessLogic, params);
      expect(resultWithDeleted.body.data?.items).toHaveLength(4);
    });
  });
});
