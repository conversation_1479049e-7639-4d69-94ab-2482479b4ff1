//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { ChatMessageBusinessLogicInterface } from "@/lib/repositories/chatMessages/interface";
import { ChatMessageBusinessLogic } from "@/lib/repositories/chatMessages/BusinessLogic";
import { MongoChatMessageRepository } from "@/lib/repositories/chatMessages/MongoRepository";
import { TestChatMessageDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateChatMessage, implHandleUpdateChatMessage, implHandleDeleteChatMessage } from "@/app/api/v1/chats/[chatId]/messages/impl";
import {
  createFullChatMessage,
  createMinimalChatMessage,
  createFullChatMessageUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createChatMessageForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createChatMessageForTrimming,
  createActiveChatMessage,
  createStatusChangeUpdate
} from "./object_creator";

describe("Update ChatMessage API Tests", () => {
  let businessLogic: ChatMessageBusinessLogicInterface;
  let dbRepository: TestChatMessageDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("ChatMessage");
    await driver.connect();
    const originalDb = new MongoChatMessageRepository(driver);
    dbRepository = new TestChatMessageDBRepositoryWrapper(originalDb, driver);
    businessLogic = new ChatMessageBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("PUT /api/v1/chats/[chatId]/messages/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullChatMessage();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const updateData = createFullChatMessageUpdate();
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2);
      expect(result.body.data?.ARRAY_FIELD).toEqual(updateData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(updateData.variables);
      expect(result.body.data?.tags).toEqual(updateData.tags);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should update only the STRING_FIELD", async () => {
      const createData = createMinimalChatMessage();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD).toEqual(createData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(createData.variables);
    });

    it("should trim STRING_FIELD when updating", async () => {
      const createData = createMinimalChatMessage();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const updateData = createUpdateWithWhitespace();
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name");
    });

    it("should fail to update non-existent chatMessage", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateChatMessage("507f1f77bcf86cd799439011", updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ChatMessage not found");
    });

    it("should fail with invalid input: empty STRING_FIELD", async () => {
      const createData = createMinimalChatMessage();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-STRING_FIELD');
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with invalid input: empty ARRAY_FIELD", async () => {
      const createData = createMinimalChatMessage();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-ARRAY_FIELD');
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty update object", async () => {
      const createData = createMinimalChatMessage();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-object');
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("No data provided for update");
    });

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateChatMessage("", updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ChatMessage ID is required");
    });

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first chatMessage
      const createData1 = createMinimalChatMessage();
      await implHandleCreateChatMessage(createData1, businessLogic);

      // Create second chatMessage
      const createData2 = createFullChatMessage();
      const createResult2 = await implHandleCreateChatMessage(createData2, businessLogic);

      // Try to update second chatMessage with first chatMessage's STRING_FIELD
      const updateData = createDuplicateNameUpdate(createData1.STRING_FIELD);
      const result = await implHandleUpdateChatMessage(createResult2.body.data.id, updateData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Another ChatMessage with this STRING_FIELD exists");
    });

    it("should allow updating chatMessage with same STRING_FIELD (no change)", async () => {
      const createData = createMinimalChatMessage();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const updateData = createSameNameUpdate();
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2);
    });

    it("should fail to update soft-deleted chatMessage", async () => {
      const createData = createChatMessageForSoftDelete();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      // Soft delete the chatMessage
      const deleteResult = await implHandleDeleteChatMessage(chatMessagesId, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Try to update the soft-deleted chatMessage
      const updateData = createUpdateForSoftDeleted();
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ChatMessage not found");
    });

    it("should trim all string fields when updating", async () => {
      const createData = createChatMessageForTrimming();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const updateData = createUpdateWithAllFieldsWhitespace();
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name");
      expect(result.body.data?.ARRAY_FIELD2).toBe("Trimmed Description");
      expect(result.body.data?.ARRAY_FIELD).toEqual(["trimmed_condition"]);
      expect(result.body.data?.variables).toEqual(["trimmed_action"]);
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"]);
    });

    it("should fail with invalid input: empty variables", async () => {
      const createData = createMinimalChatMessage();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-variables');
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should update isActive status", async () => {
      const createData = createActiveChatMessage();
      const createResult = await implHandleCreateChatMessage(createData, businessLogic);
      const chatMessagesId = createResult.body.data.id;

      const updateData = createStatusChangeUpdate();
      const result = await implHandleUpdateChatMessage(chatMessagesId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
    });
  });
});
