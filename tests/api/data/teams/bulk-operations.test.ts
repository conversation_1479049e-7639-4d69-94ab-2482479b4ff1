//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { TeamBusinessLogicInterface } from "@/lib/repositories/teams/interface";
import { TeamBusinessLogic } from "@/lib/repositories/teams/BusinessLogic";
import { MongoTeamRepository } from "@/lib/repositories/teams/MongoRepository";
import { TestTeamDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateTeam,
  implHandleGetTeam,
  implHandleBulkCreateTeams,
  implHandleBulkUpdateTeams,
  implHandleBulkDeleteTeams
} from "@/app/api/v1/teams/impl";
import {
  createMultipleTeams,
  createSimpleTeams,
  createExistingTeam,
  createDuplicateTeamsForBulk,
  createTeamsForBulkUpdate,
  createBulkUpdateData,
  createTeamsForBulkDelete
} from "./object_creator";

describe("Team Bulk Operations Tests", () => {
  let businessLogic: TeamBusinessLogicInterface;
  let dbRepository: TestTeamDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Team");
    await driver.connect()
    const originalDb = new MongoTeamRepository(driver);
    dbRepository = new TestTeamDBRepositoryWrapper(originalDb, driver);
    businessLogic = new TeamBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("Bulk Create", () => {
    it("should successfully create multiple teams", async () => {
      const teamsData = createMultipleTeams();

      const result = await implHandleBulkCreateTeams(teamsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(teamsData.length);
      expect(result.body.data[0].STRING_FIELD).toBe(teamsData[0].STRING_FIELD);
      expect(result.body.data[1].STRING_FIELD).toBe(teamsData[1].STRING_FIELD);
      expect(result.body.data[2].STRING_FIELD).toBe(teamsData[2].STRING_FIELD);
      expect(await dbRepository.getTeamCount()).toBe(teamsData.length);
    });

    it("should fail if any team has duplicate STRING_FIELD", async () => {
      const existingTeam = createExistingTeam();
      const createResult = await implHandleCreateTeam(existingTeam, businessLogic);
      expect(createResult.status).toBe(201);

      const teamsData = createDuplicateTeamsForBulk();

      const result = await implHandleBulkCreateTeams(teamsData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate STRING_FIELD found: Existing Team");
      expect(await dbRepository.getTeamCount()).toBe(1);
    });

    it("should handle simple teams creation", async () => {
      const teamsData = createSimpleTeams();

      const result = await implHandleBulkCreateTeams(teamsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data).toHaveLength(teamsData.length);
      expect(await dbRepository.getTeamCount()).toBe(teamsData.length);
    });
  });

  describe("Bulk Update", () => {
    it("should successfully update multiple teams", async () => {
      const teamsData = createTeamsForBulkUpdate();
      const createResult1 = await implHandleCreateTeam(teamsData[0], businessLogic);
      const createResult2 = await implHandleCreateTeam(teamsData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult1.body.data.id, data: updateData[0] },
        { id: createResult2.body.data.id, data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateTeams(updates, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.updatedCount).toBe(2);

      const getResult1 = await implHandleGetTeam(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetTeam(createResult2.body.data.id, businessLogic);

      expect(getResult1.body.data?.STRING_FIELD).toBe(updateData[0].STRING_FIELD);
      expect(getResult1.body.data?.updatedBy).toBe(updateData[0].updatedBy);
      expect(getResult2.body.data?.STRING_FIELD).toBe(updateData[1].STRING_FIELD);
      expect(getResult2.body.data?.updatedBy).toBe(updateData[1].updatedBy);
    });

    it("should fail if any team doesn't exist", async () => {
      const teamData = createTeamsForBulkUpdate()[0];
      const createResult = await implHandleCreateTeam(teamData, businessLogic);
      expect(createResult.status).toBe(201);

      const updateData = createBulkUpdateData();
      const updates = [
        { id: createResult.body.data.id, data: updateData[0] },
        { id: "non-existent-id", data: updateData[1] },
      ];

      const result = await implHandleBulkUpdateTeams(updates, businessLogic);

      expect(result.status).toBe(500);
      expect(result.body.status).toBe("failed");
    });

    it("should fail if any update would create duplicate STRING_FIELD", async () => {
      const teamsData = createTeamsForBulkUpdate();
      const createResult1 = await implHandleCreateTeam(teamsData[0], businessLogic);
      const createResult2 = await implHandleCreateTeam(teamsData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const updates = [
        {
          id: createResult2.body.data.id,
          data: { STRING_FIELD: teamsData[0].STRING_FIELD, updatedBy: "admin" }, // Try to update second team with first team's STRING_FIELD
        },
      ];

      const result = await implHandleBulkUpdateTeams(updates, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Duplicate STRING_FIELD in update: Team 1");
    });
  });

  describe("Bulk Delete", () => {
    it("should successfully soft delete multiple teams", async () => {
      const teamsData = createTeamsForBulkDelete();
      const createResult1 = await implHandleCreateTeam(teamsData[0], businessLogic);
      const createResult2 = await implHandleCreateTeam(teamsData[1], businessLogic);
      const createResult3 = await implHandleCreateTeam(teamsData[2], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);
      expect(createResult3.status).toBe(201);

      const result = await implHandleBulkDeleteTeams([createResult1.body.data.id, createResult2.body.data.id], businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getTeamCount()).toBe(1); // Only non-deleted

      const getResult1 = await implHandleGetTeam(createResult1.body.data.id, businessLogic);
      const getResult2 = await implHandleGetTeam(createResult2.body.data.id, businessLogic);
      const getResult3 = await implHandleGetTeam(createResult3.body.data.id, businessLogic);

      expect(getResult1.status).toBe(404);
      expect(getResult2.status).toBe(404);
      expect(getResult3.status).toBe(200);
    });

    it("should successfully hard delete multiple teams", async () => {
      const teamsData = createTeamsForBulkDelete();
      const createResult1 = await implHandleCreateTeam(teamsData[0], businessLogic);
      const createResult2 = await implHandleCreateTeam(teamsData[1], businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      const result = await implHandleBulkDeleteTeams([createResult1.body.data.id, createResult2.body.data.id], businessLogic, true);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data.deletedCount).toBe(2);
      expect(await dbRepository.getTeamCount()).toBe(0);
    });

    it("should fail if any team doesn't exist", async () => {
      const teamData = createTeamsForBulkDelete()[0];
      const createResult = await implHandleCreateTeam(teamData, businessLogic);
      expect(createResult.status).toBe(201);

      const result = await implHandleBulkDeleteTeams([createResult.body.data.id, "non-existent-id"], businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty team IDs", async () => {
      const result = await implHandleBulkDeleteTeams(["", "valid-id"], businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("ID at index 0 is required");
    });
  });
});
