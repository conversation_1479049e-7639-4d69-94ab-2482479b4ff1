//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { TeamBusinessLogicInterface } from "@/lib/repositories/teams/interface";
import { TeamBusinessLogic } from "@/lib/repositories/teams/BusinessLogic";
import { MongoTeamRepository } from "@/lib/repositories/teams/MongoRepository";
import { TestTeamDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateTeam, implHandleUpdateTeam, implHandleDeleteTeam } from "@/app/api/v1/teams/impl";
import {
  createFullTeam,
  createMinimalTeam,
  createFullTeamUpdate,
  createNameOnlyUpdate,
  createInvalidUpdate,
  createUpdateWithWhitespace,
  createDuplicateNameUpdate,
  createSameNameUpdate,
  createTeamForSoftDelete,
  createUpdateForSoftDeleted,
  createUpdateWithAllFieldsWhitespace,
  createTeamForTrimming,
  createActiveTeam,
  createStatusChangeUpdate
} from "./object_creator";

describe("Update Team API Tests", () => {
  let businessLogic: TeamBusinessLogicInterface;
  let dbRepository: TestTeamDBRepositoryWrapper;

  beforeEach(async () => {
    const driver = new InMemoryMongoDriver("Team");
    await driver.connect();
    const originalDb = new MongoTeamRepository(driver);
    dbRepository = new TestTeamDBRepositoryWrapper(originalDb, driver);
    businessLogic = new TeamBusinessLogic(dbRepository);
    await dbRepository.clear();
  });

  describe("PUT /api/v1/teams/:id", () => {
    it("should successfully update all fields", async () => {
      const createData = createFullTeam();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      const updateData = createFullTeamUpdate();
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2);
      expect(result.body.data?.ARRAY_FIELD).toEqual(updateData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(updateData.variables);
      expect(result.body.data?.tags).toEqual(updateData.tags);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should update only the STRING_FIELD", async () => {
      const createData = createMinimalTeam();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD).toEqual(createData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(createData.variables);
    });

    it("should trim STRING_FIELD when updating", async () => {
      const createData = createMinimalTeam();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      const updateData = createUpdateWithWhitespace();
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name");
    });

    it("should fail to update non-existent team", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateTeam("507f1f77bcf86cd799439011", updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Team not found");
    });

    it("should fail with invalid input: empty STRING_FIELD", async () => {
      const createData = createMinimalTeam();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-STRING_FIELD');
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with invalid input: empty ARRAY_FIELD", async () => {
      const createData = createMinimalTeam();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-ARRAY_FIELD');
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should fail with empty update object", async () => {
      const createData = createMinimalTeam();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-object');
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("No data provided for update");
    });

    it("should fail with empty ID", async () => {
      const updateData = createNameOnlyUpdate();
      const result = await implHandleUpdateTeam("", updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Team ID is required");
    });

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first team
      const createData1 = createMinimalTeam();
      await implHandleCreateTeam(createData1, businessLogic);

      // Create second team
      const createData2 = createFullTeam();
      const createResult2 = await implHandleCreateTeam(createData2, businessLogic);

      // Try to update second team with first team's STRING_FIELD
      const updateData = createDuplicateNameUpdate(createData1.STRING_FIELD);
      const result = await implHandleUpdateTeam(createResult2.body.data.id, updateData, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Another Team with this STRING_FIELD exists");
    });

    it("should allow updating team with same STRING_FIELD (no change)", async () => {
      const createData = createMinimalTeam();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      const updateData = createSameNameUpdate();
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(updateData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(updateData.ARRAY_FIELD2);
    });

    it("should fail to update soft-deleted team", async () => {
      const createData = createTeamForSoftDelete();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      // Soft delete the team
      const deleteResult = await implHandleDeleteTeam(teamsId, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Try to update the soft-deleted team
      const updateData = createUpdateForSoftDeleted();
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Team not found");
    });

    it("should trim all string fields when updating", async () => {
      const createData = createTeamForTrimming();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      const updateData = createUpdateWithAllFieldsWhitespace();
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Name");
      expect(result.body.data?.ARRAY_FIELD2).toBe("Trimmed Description");
      expect(result.body.data?.ARRAY_FIELD).toEqual(["trimmed_condition"]);
      expect(result.body.data?.variables).toEqual(["trimmed_action"]);
      expect(result.body.data?.tags).toEqual(["tag1", "tag2"]);
    });

    it("should fail with invalid input: empty variables", async () => {
      const createData = createMinimalTeam();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      const updateData = createInvalidUpdate('empty-variables');
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
    });

    it("should update isActive status", async () => {
      const createData = createActiveTeam();
      const createResult = await implHandleCreateTeam(createData, businessLogic);
      const teamsId = createResult.body.data.id;

      const updateData = createStatusChangeUpdate();
      const result = await implHandleUpdateTeam(teamsId, updateData, businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.isActive).toBe(updateData.isActive);
    });
  });
});
