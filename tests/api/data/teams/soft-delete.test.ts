//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { TeamBusinessLogicInterface } from "@/lib/repositories/teams/interface";
import { TeamBusinessLogic } from "@/lib/repositories/teams/BusinessLogic";
import { MongoTeamRepository } from "@/lib/repositories/teams/MongoRepository";
import { TestTeamDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import {
  implHandleCreateTeam,
  implHandleGetTeam,
  implHandleDeleteTeam,
  implHandleUpdateTeam,
  implHandleGetAllTeams,
  implHandleRestoreTeam
} from "@/app/api/v1/teams/impl";
import { createTeam, createTeamUpdate, createTeamWithDescription, createTestTeam, createTestTeam2 } from "./object_creator";

describe("Team Soft Delete Tests", () => {
  let businessLogic: TeamBusinessLogicInterface;
    let dbRepository: TestTeamDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("Team");
      await driver.connect()
      const originalDb = new MongoTeamRepository(driver);
      dbRepository = new TestTeamDBRepositoryWrapper(originalDb, driver);
      businessLogic = new TeamBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("Soft Delete", () => {
    it("should soft delete a teams by default", async () => {
      const teamData = createTeamWithDescription();
      const createResult = await implHandleCreateTeam(teamData, businessLogic);
      expect(createResult.status).toBe(201);

      const deleteResult = await implHandleDeleteTeam(createResult.body.data.id, businessLogic);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");

      // Team should not be accessible by default
      const getResult = await implHandleGetTeam(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);

      // But should be accessible when including deleted
      const getDeletedResult = await implHandleGetTeam(createResult.body.data.id, businessLogic, true);
      expect(getDeletedResult.status).toBe(200);
      expect(getDeletedResult.body.data).not.toBeNull();

      // Count should exclude soft deleted
      expect(await dbRepository.getTeamCount()).toBe(0);
      expect(await dbRepository.getTeamCount(true)).toBe(1);
    });

    it("should hard delete when specified", async () => {
      const teamData = createTeamWithDescription();
      const createResult = await implHandleCreateTeam(teamData, businessLogic);
      expect(createResult.status).toBe(201);

      // Hard delete using impl function
      const deleteResult = await implHandleDeleteTeam(createResult.body.data.id, businessLogic, true);

      expect(deleteResult.status).toBe(200);
      expect(deleteResult.body.status).toBe("success");

      // Team should not be accessible at all
      const getResult = await implHandleGetTeam(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);
      const getDeletedResult = await implHandleGetTeam(createResult.body.data.id, businessLogic, true);
      expect(getDeletedResult.status).toBe(404);

      // Count should be 0 in both cases
      expect(await dbRepository.getTeamCount()).toBe(0);
      expect(await dbRepository.getTeamCount(true)).toBe(0);
    });

    it("should not include soft deleted teams in getAll by default", async () => {
      const teamData1 = createTeam(1);
      const teamData2 = createTeam(2);

      const createResult1 = await implHandleCreateTeam(teamData1, businessLogic);
      const createResult2 = await implHandleCreateTeam(teamData2, businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      // Soft delete one teams
      const deleteResult = await implHandleDeleteTeam(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const result = await implHandleGetAllTeams(businessLogic);

      expect(result.status).toBe(200);
      expect(result.body.data?.items).toHaveLength(1);
      expect(result.body.data?.total).toBe(1);
      expect(result.body.data?.items[0].id).toBe(createResult2.body.data.id);
    });

    it("should include soft deleted teams when specified", async () => {
      const teamData1 = createTeam(1);
      const teamData2 = createTeam(2);

      const createResult1 = await implHandleCreateTeam(teamData1, businessLogic);
      const createResult2 = await implHandleCreateTeam(teamData2, businessLogic);
      expect(createResult1.status).toBe(201);
      expect(createResult2.status).toBe(201);

      // Soft delete one teams
      const deleteResult = await implHandleDeleteTeam(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const result = await implHandleGetAllTeams(businessLogic, { includeDeleted: true });

      expect(result.status).toBe(200);
      expect(result.body.data?.items).toHaveLength(2);
      expect(result.body.data?.total).toBe(2);

      const deletedTeam = result.body.data?.items.find((c: any) => c.id === createResult1.body.data.id);
      expect(deletedTeam).toBeDefined();
      expect(deletedTeam?.deletedAt).toBeDefined();
    });

    it("should not allow updating soft deleted teams", async () => {
      const teamData = createTeam(3);
      const createResult = await implHandleCreateTeam(teamData, businessLogic);
      expect(createResult.status).toBe(201);

      const deleteResult = await implHandleDeleteTeam(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const teamUpdate = createTeamUpdate(1)

      const result = await implHandleUpdateTeam(createResult.body.data.id, teamUpdate, businessLogic);

      expect(result.status).toBe(404);
      expect(result.body.status).toBe("failed");
    });

    it("should not include soft deleted teams in search", async () => {
      const teamData1 = createTeam(3); // "Test Team"
      const createResult = await implHandleCreateTeam(teamData1, businessLogic);
      expect(createResult.status).toBe(201);

      // Soft delete the team
      const deleteResult = await implHandleDeleteTeam(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Search should not find the soft deleted team
      const searchResult = await implHandleGetAllTeams(businessLogic, { search: "Test" });
      expect(searchResult.status).toBe(200);
      expect(searchResult.body.data).toHaveLength(0);
    });
  });

  describe("Restore", () => {
    it("should restore a soft deleted teams", async () => {
      const teamData = createTeamWithDescription();
      const createResult = await implHandleCreateTeam(teamData, businessLogic);
      expect(createResult.status).toBe(201);

      // Soft delete the teams
      const deleteResult = await implHandleDeleteTeam(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const getResult = await implHandleGetTeam(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(404);

      // Restore the teams
      const restoreResult = await implHandleRestoreTeam(createResult.body.data.id, businessLogic);

      expect(restoreResult.status).toBe(200);
      expect(restoreResult.body.status).toBe("success");

      // Team should be accessible again
      const restoredResult = await implHandleGetTeam(createResult.body.data.id, businessLogic);
      expect(restoredResult.status).toBe(200);
      expect(restoredResult.body.data?.deletedAt).toBeUndefined();

      // Count should include the restored teams
      expect(await dbRepository.getTeamCount()).toBe(1);
    });

    it("should fail to restore a non-existent teams", async () => {
      const restoreResult = await implHandleRestoreTeam("507f1f77bcf86cd799439011", businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail to restore a teams that was never deleted", async () => {
      const teamData = createTeam(3);
      const createResult = await implHandleCreateTeam(teamData, businessLogic);
      expect(createResult.status).toBe(201);

      const restoreResult = await implHandleRestoreTeam(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail to restore a hard deleted teams", async () => {
      const teamData = createTeam(3);
      const createResult = await implHandleCreateTeam(teamData, businessLogic);
      expect(createResult.status).toBe(201);

      // Hard delete the teams
      const deleteResult = await implHandleDeleteTeam(createResult.body.data.id, businessLogic, true);
      expect(deleteResult.status).toBe(200);

      const restoreResult = await implHandleRestoreTeam(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });

    it("should fail with empty teams ID", async () => {
      const restoreResult = await implHandleRestoreTeam("", businessLogic);
      expect(restoreResult.status).toBe(400);
      expect(restoreResult.body.status).toBe("failed");
      expect(restoreResult.body.error).toContain("Team ID is required");
    });

    it("should fail with whitespace-only teams ID", async () => {
      const restoreResult = await implHandleRestoreTeam("   ", businessLogic);
      expect(restoreResult.status).toBe(400);
      expect(restoreResult.body.status).toBe("failed");
      expect(restoreResult.body.error).toContain("Team ID is required");
    });

    it("should update updatedAt when restoring", async () => {
      const teamData = createTeam(3);
      const createResult = await implHandleCreateTeam(teamData, businessLogic);
      expect(createResult.status).toBe(201);

      const originalUpdatedAt = createResult.body.data.updatedAt;

      // Wait a bit to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      // Soft delete and restore
      const deleteResult = await implHandleDeleteTeam(createResult.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      const restoreResult = await implHandleRestoreTeam(createResult.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(200);

      const getResult = await implHandleGetTeam(createResult.body.data.id, businessLogic);
      expect(getResult.status).toBe(200);
      expect(getResult.body.data?.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe("Duplicate Name Validation with Soft Delete", () => {
    it("should allow creating teams with STRING_FIELD of soft deleted teams", async () => {
      // Create and soft delete a teams
      const teamData1 = createTestTeam();
      const createResult1 = await implHandleCreateTeam(teamData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const deleteResult = await implHandleDeleteTeam(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Should be able to create new teams with same STRING_FIELD
      const teamData2 = createTestTeam2();
      const createResult2 = await implHandleCreateTeam(teamData2, businessLogic);

      expect(createResult2.status).toBe(201);
      expect(createResult2.body.data.STRING_FIELD).toBe(teamData2.STRING_FIELD);
      expect(await dbRepository.getTeamCount()).toBe(1);
    });

    it("should prevent creating teams with STRING_FIELD of active teams", async () => {
      const teamData1 = createTestTeam();
      const createResult1 = await implHandleCreateTeam(teamData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const teamData2 = createTestTeam2();
      const createResult2 = await implHandleCreateTeam(teamData2, businessLogic);

      expect(createResult2.status).toBe(409);
      expect(createResult2.body.status).toBe("failed");
      expect(createResult2.body.error).toContain("Team with the same STRING_FIELD already exists");
    });

    it("should prevent restoring teams if STRING_FIELD is now taken", async () => {
      // Create and soft delete a teams
      const teamData1 = createTestTeam();
      const createResult1 = await implHandleCreateTeam(teamData1, businessLogic);
      expect(createResult1.status).toBe(201);

      const deleteResult = await implHandleDeleteTeam(createResult1.body.data.id, businessLogic);
      expect(deleteResult.status).toBe(200);

      // Create new teams with same STRING_FIELD
      const teamData2 = createTestTeam2();
      const createResult2 = await implHandleCreateTeam(teamData2, businessLogic);
      expect(createResult2.status).toBe(201);

      // Should not be able to restore the first teams
      const restoreResult = await implHandleRestoreTeam(createResult1.body.data.id, businessLogic);
      expect(restoreResult.status).toBe(404);
      expect(restoreResult.body.status).toBe("failed");
    });
  });
});
