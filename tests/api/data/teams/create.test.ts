//@ts-ignore
import { describe, it, expect, beforeEach } from "bun:test";
import { TeamBusinessLogicInterface } from "@/lib/repositories/teams/interface";
import { TeamBusinessLogic } from "@/lib/repositories/teams/BusinessLogic";
import { MongoTeamRepository } from "@/lib/repositories/teams/MongoRepository";
import { TestTeamDBRepositoryWrapper } from "./TestDBRepositoryWrapper";
import { InMemoryMongoDriver } from "@/tests/InMemoryMongoDriver";
import { implHandleCreateTeam } from "@/app/api/v1/teams/impl";
import {
  createFullTeam,
  createMinimalTeam,
  createTeamWithDescription,
  createTeamWithTags,
  createTeamWithWhitespace,
  createDuplicateTeam,
  createSecondDuplicateTeam,
  createInvalidTeam,
  createTeamWithManyTags,
  createTeamWithoutDescription,
  createTeamWithEmptyTags
} from "./object_creator";

describe("Create Team API Tests", () => {
  let businessLogic: TeamBusinessLogicInterface;
    let dbRepository: TestTeamDBRepositoryWrapper;
  
    beforeEach(async () => {
      const driver = new InMemoryMongoDriver("Team");
      await driver.connect()
      const originalDb = new MongoTeamRepository(driver);
      dbRepository = new TestTeamDBRepositoryWrapper(originalDb, driver);
      businessLogic = new TeamBusinessLogic(dbRepository);
      await dbRepository.clear();
    });

  describe("POST /api/v1/teams", () => {
    it("should successfully create a new teams with all fields", async () => {
      const teamsData = createFullTeam();

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(teamsData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD2).toBe(teamsData.ARRAY_FIELD2);
      expect(result.body.data?.ARRAY_FIELD).toEqual(teamsData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(teamsData.variables);
      expect(result.body.data?.tags).toEqual(teamsData.tags);
      expect(result.body.data?.isActive).toBe(teamsData.isActive);
      expect(result.body.data?.id).toBeDefined();
      expect(result.body.data?.createdAt).toBeDefined();
      expect(result.body.data?.updatedAt).toBeDefined();
    });

    it("should successfully create a teams with minimal required fields", async () => {
      const teamsData = createMinimalTeam();

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.STRING_FIELD).toBe(teamsData.STRING_FIELD);
      expect(result.body.data?.ARRAY_FIELD).toEqual(teamsData.ARRAY_FIELD);
      expect(result.body.data?.variables).toEqual(teamsData.variables);
      expect(result.body.data?.isActive).toBe(true); // Should default to true
      expect(result.body.data?.tags).toEqual([]);
    });

    it("should create team with ARRAY_FIELD2", async () => {
      const teamsData = createTeamWithDescription();

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.ARRAY_FIELD2).toBe(teamsData.ARRAY_FIELD2);
    });

    it("should create team with tags", async () => {
      const teamsData = createTeamWithTags();

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.tags).toEqual(teamsData.tags);
    });

    it("should trim whitespace from STRING_FIELD", async () => {
      const teamsData = createTeamWithWhitespace();

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.data?.STRING_FIELD).toBe("Trimmed Team");
    });

    it("should fail with duplicate STRING_FIELD", async () => {
      // Create first teams
      const teamsData1 = createDuplicateTeam();
      await implHandleCreateTeam(teamsData1, businessLogic);

      // Try to create second teams with same STRING_FIELD
      const teamsData2 = createSecondDuplicateTeam();
      const result = await implHandleCreateTeam(teamsData2, businessLogic);

      expect(result.status).toBe(409);
      expect(result.body.status).toBe("failed");
      expect(result.body.error).toContain("Team with the same STRING_FIELD already exists");
    });

    it("should fail with missing STRING_FIELD", async () => {
      const teamsData = {
        ARRAY_FIELD2: "+6281234567890"
      };

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing ARRAY_FIELD2", async () => {
      const teamsData = createInvalidTeam('missing-ARRAY_FIELD2');


      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with missing ARRAY_FIELD", async () => {
      const teamsData = createInvalidTeam('missing-ARRAY_FIELD');

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("ARRAY_FIELD");
    });

    it("should fail with missing variables", async () => {
      const teamsData = createInvalidTeam('missing-variables');

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
      expect(result.body.error![0]).toContain("variables");
    });

    it("should fail with empty ARRAY_FIELD array", async () => {
      const teamsData = createInvalidTeam('empty-ARRAY_FIELD');

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should fail with empty variables array", async () => {
      const teamsData = createInvalidTeam('empty-variables');

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(400);
      expect(result.body.status).toBe("failed");
      expect(Array.isArray(result.body.error)).toBe(true);
    });

    it("should create team with many tags", async () => {
      const teamsData = createTeamWithManyTags();

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(teamsData.tags);
    });

    it("should handle optional ARRAY_FIELD2", async () => {
      const teamsData = createTeamWithoutDescription();

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.ARRAY_FIELD2).toBe("");
    });

    it("should handle empty arrays for tags", async () => {
      const teamsData = createTeamWithEmptyTags();

      const result = await implHandleCreateTeam(teamsData, businessLogic);

      expect(result.status).toBe(201);
      expect(result.body.status).toBe("success");
      expect(result.body.data?.tags).toEqual(teamsData.tags);
    });
  });
});
