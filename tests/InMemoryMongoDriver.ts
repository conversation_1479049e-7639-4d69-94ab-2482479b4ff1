/**
 * InMemoryMongoDriver (Serverless-aware)
 * ======================================
 * 
 * This implementation uses an in-memory MongoDB (via `mongodb-memory-server`)
 * and is designed to be **serverless-aware** by maintaining a **shared global instance**.
 * 
 * ## Why?
 * In test environments (including serverless-style tests), connecting to a new
 * in-memory DB for every instantiation is slow and redundant. This implementation:
 * 
 * - ✅ Connects **once** per process
 * - ✅ Reuses the in-memory DB between test files or re-renders
 * - ✅ Mimics production connection caching behavior
 * 
 * ## Usage
 * Always call `await connect()` before accessing collections.
 * 
 * ```ts
 * const driver = new InMemoryMongoDriver("TestDB");
 * await driver.connect();
 * const users = driver.getCollection("users");
 * ```
 * 
 * ## Note
 * Call `close()` at the end of your test suite to release resources.
 */


import { MongoMemoryServer } from "mongodb-memory-server";
import { MongoClient, Collection, Document, Db } from "mongodb";
import { MongoDriver } from "@/lib/repositories/MongoDriver";

// Global shared state to mimic serverless-aware behavior
let sharedMongoServer: MongoMemoryServer | null = null;
let sharedClient: MongoClient | null = null;
let sharedDb: Db | null = null;
let sharedIsConnected = false;

export class InMemoryMongoDriver implements MongoDriver {
  private dbName: string;

  constructor(dbName: string) {
    this.dbName = dbName;
  }

  async connect(): Promise<void> {
    if (sharedIsConnected && sharedClient && sharedDb) return;

    sharedMongoServer = await MongoMemoryServer.create();
    const uri = sharedMongoServer.getUri();

    sharedClient = new MongoClient(uri);
    await sharedClient.connect();
    sharedDb = sharedClient.db(this.dbName);

    sharedIsConnected = true;
  }

  getCollection<T extends Document = Document>(name: string): Collection<T> {
    if (!sharedIsConnected || !sharedDb) {
      throw new Error("InMemoryMongoDriver: Must call connect() before accessing collections");
    }
    return sharedDb.collection<T>(name);
  }

  async close(): Promise<void> {
    if (sharedClient) {
      await sharedClient.close();
      sharedClient = null;
    }

    if (sharedMongoServer) {
      await sharedMongoServer.stop();
      sharedMongoServer = null;
    }

    sharedDb = null;
    sharedIsConnected = false;
  }

  async clear(): Promise<void> {
    if (!sharedDb) return;
    const collections = await sharedDb.collections();
    for (const collection of collections) {
      await collection.deleteMany({});
    }
  }

}
